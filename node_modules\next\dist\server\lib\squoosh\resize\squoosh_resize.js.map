{"version": 3, "sources": ["../../../../../server/lib/squoosh/resize/squoosh_resize.js"], "names": ["resize", "cleanup", "wasm", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "ptr", "length", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "cachegetUint8ClampedMemory0", "getUint8ClampedMemory0", "Uint8ClampedArray", "getClampedArrayU8FromWasm0", "len", "subarray", "input_image", "input_width", "input_height", "output_width", "output_height", "typ_idx", "premultiply", "color_space_conversion", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module"], "mappings": "Y;;;E;QA0DgBA,MAAM,GAANA,MAAM,A;QA6ENC,OAAO,GAAPA,OAAO,A;wB;AAvIvB,GAAG,CAACC,IAAI;AAER,GAAG,CAACC,oBAAoB,GAAG,IAAI;SACtBC,eAAe,GAAG,CAAC;IAC1B,EAAE,EACAD,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACE,MAAM,KAAKH,IAAI,CAACI,MAAM,CAACD,MAAM,EAClD,CAAC;QACDF,oBAAoB,GAAG,GAAG,CAACI,UAAU,CAACL,IAAI,CAACI,MAAM,CAACD,MAAM,C;IAC1D,CAAC;IACD,MAAM,CAACF,oBAAoB;AAC7B,CAAC;AAED,GAAG,CAACK,eAAe,GAAG,CAAC;SAEdC,iBAAiB,CAACC,GAAG,EAAEC,MAAM,EAAE,CAAC;IACvC,KAAK,CAACC,GAAG,GAAGD,MAAM,CAACD,GAAG,CAACG,MAAM,GAAG,CAAC;IACjCT,eAAe,GAAGU,GAAG,CAACJ,GAAG,EAAEE,GAAG,GAAG,CAAC,C;IAClCJ,eAAe,GAAGE,GAAG,CAACG,MAAM,A;IAC5B,MAAM,CAACD,GAAG;AACZ,CAAC;AAED,GAAG,CAACG,oBAAoB,GAAG,IAAI;SACtBC,eAAe,GAAG,CAAC;IAC1B,EAAE,EACAD,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACV,MAAM,KAAKH,IAAI,CAACI,MAAM,CAACD,MAAM,EAClD,CAAC;QACDU,oBAAoB,GAAG,GAAG,CAACE,UAAU,CAACf,IAAI,CAACI,MAAM,CAACD,MAAM,C;IAC1D,CAAC;IACD,MAAM,CAACU,oBAAoB;AAC7B,CAAC;AAED,GAAG,CAACG,2BAA2B,GAAG,IAAI;SAC7BC,sBAAsB,GAAG,CAAC;IACjC,EAAE,EACAD,2BAA2B,KAAK,IAAI,IACpCA,2BAA2B,CAACb,MAAM,KAAKH,IAAI,CAACI,MAAM,CAACD,MAAM,EACzD,CAAC;QACDa,2BAA2B,GAAG,GAAG,CAACE,iBAAiB,CAAClB,IAAI,CAACI,MAAM,CAACD,MAAM,C;IACxE,CAAC;IACD,MAAM,CAACa,2BAA2B;AACpC,CAAC;SAEQG,0BAA0B,CAACT,GAAG,EAAEU,GAAG,EAAE,CAAC;IAC7C,MAAM,CAACH,sBAAsB,GAAGI,QAAQ,CAACX,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,GAAGU,GAAG;AACjE,CAAC;SAYetB,MAAM,CACpBwB,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,OAAO,EACPC,WAAW,EACXC,sBAAsB,EACtB,CAAC;IACD,GAAG,CAAC,CAAC;QACH,KAAK,CAACC,MAAM,GAAG9B,IAAI,CAAC+B,+BAA+B,EAAE,EAAE;QACvD,GAAG,CAACC,IAAI,GAAGzB,iBAAiB,CAACe,WAAW,EAAEtB,IAAI,CAACiC,iBAAiB;QAChE,GAAG,CAACC,IAAI,GAAG5B,eAAe;QAC1BN,IAAI,CAACF,MAAM,CACTgC,MAAM,EACNE,IAAI,EACJE,IAAI,EACJX,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,OAAO,EACPC,WAAW,EACXC,sBAAsB,C;QAExB,GAAG,CAACM,EAAE,GAAGrB,eAAe,GAAGgB,MAAM,GAAG,CAAC,GAAG,CAAC;QACzC,GAAG,CAACM,EAAE,GAAGtB,eAAe,GAAGgB,MAAM,GAAG,CAAC,GAAG,CAAC;QACzC,GAAG,CAACO,EAAE,GAAGlB,0BAA0B,CAACgB,EAAE,EAAEC,EAAE,EAAEE,KAAK;QACjDtC,IAAI,CAACuC,eAAe,CAACJ,EAAE,EAAEC,EAAE,GAAG,CAAC,C;QAC/B,MAAM,CAACC,EAAE;IACX,CAAC,QAAS,CAAC;QACTrC,IAAI,CAAC+B,+BAA+B,CAAC,EAAE,C;IACzC,CAAC;AACH,CAAC;eAEcS,IAAI,CAACC,MAAM,EAAEC,OAAO,EAAE,CAAC;IACpC,EAAE,EAAE,MAAM,CAACC,QAAQ,KAAK,CAAU,aAAIF,MAAM,YAAYE,QAAQ,EAAE,CAAC;QACjE,EAAE,EAAE,MAAM,CAACC,WAAW,CAACC,oBAAoB,KAAK,CAAU,WAAE,CAAC;YAC3D,MAAM,CAAC,KAAK,CAACD,WAAW,CAACC,oBAAoB,CAACJ,MAAM,EAAEC,OAAO;QAC/D,CAAC;QAED,KAAK,CAACI,KAAK,GAAG,KAAK,CAACL,MAAM,CAACM,WAAW;QACtC,MAAM,CAAC,KAAK,CAACH,WAAW,CAACI,WAAW,CAACF,KAAK,EAAEJ,OAAO;IACrD,CAAC,MAAM,CAAC;QACN,KAAK,CAACO,QAAQ,GAAG,KAAK,CAACL,WAAW,CAACI,WAAW,CAACP,MAAM,EAAEC,OAAO;QAE9D,EAAE,EAAEO,QAAQ,YAAYL,WAAW,CAACM,QAAQ,EAAE,CAAC;YAC7C,MAAM,CAAC,CAAC;gBAACD,QAAQ;gBAAER,MAAM;YAAC,CAAC;QAC7B,CAAC,MAAM,CAAC;YACN,MAAM,CAACQ,QAAQ;QACjB,CAAC;IACH,CAAC;AACH,CAAC;eAEcE,IAAI,CAACC,KAAK,EAAE,CAAC;IAC1B,KAAK,CAACV,OAAO,GAAG,CAAC,CAAC;IAElB,EAAE,EACA,MAAM,CAACU,KAAK,KAAK,CAAQ,WACxB,MAAM,CAACC,OAAO,KAAK,CAAU,aAAID,KAAK,YAAYC,OAAO,IACzD,MAAM,CAACC,GAAG,KAAK,CAAU,aAAIF,KAAK,YAAYE,GAAG,EAClD,CAAC;QACDF,KAAK,GAAGG,KAAK,CAACH,KAAK,C;IACrB,CAAC;IAED,KAAK,CAAC,CAAC,CAACH,QAAQ,GAAER,MAAM,EAAC,CAAC,GAAG,KAAK,CAACD,IAAI,CAAC,KAAK,CAACY,KAAK,EAAEV,OAAO;IAE5D1C,IAAI,GAAGiD,QAAQ,CAACO,OAAO,A;IACvBL,IAAI,CAACM,sBAAsB,GAAGhB,MAAM,A;IAEpC,MAAM,CAACzC,IAAI;AACb,CAAC;eAEcmD,IAAI;0B;SAGHpD,OAAO,GAAG,CAAC;IACzBC,IAAI,GAAG,IAAI,A;IACXC,oBAAoB,GAAG,IAAI,A;IAC3BY,oBAAoB,GAAG,IAAI,A;AAC7B,CAAC"}