{"version": 3, "sources": ["../../server/utils.ts"], "names": ["isBlockedPage", "cleanAmpPath", "isBot", "isTargetLikeServerless", "pathname", "BLOCKED_PAGES", "includes", "match", "replace", "userAgent", "test", "target", "isServerless", "isServerlessTrace"], "mappings": "Y;;;E;QAEgBA,aAAa,GAAbA,aAAa,A;QAIbC,YAAY,GAAZA,YAAY,A;QAWZC,KAAK,GAALA,KAAK,A;QAMLC,sBAAsB,GAAtBA,sBAAsB,A;AAvBR,GAAyB,CAAzB,UAAyB;SAEvCH,aAAa,CAACI,QAAgB,EAAW,CAAC;IACxD,MAAM,CAACC,UAAa,eAACC,QAAQ,CAACF,QAAQ;AACxC,CAAC;SAEeH,YAAY,CAACG,QAAgB,EAAU,CAAC;IACtD,EAAE,EAAEA,QAAQ,CAACG,KAAK,0BAA0B,CAAC;QAC3CH,QAAQ,GAAGA,QAAQ,CAACI,OAAO,2BAA2B,CAAG,G;IAC3D,CAAC;IACD,EAAE,EAAEJ,QAAQ,CAACG,KAAK,yBAAyB,CAAC;QAC1CH,QAAQ,GAAGA,QAAQ,CAACI,OAAO,wBAAwB,CAAE,E;IACvD,CAAC;IACDJ,QAAQ,GAAGA,QAAQ,CAACI,OAAO,QAAQ,CAAE,E;IACrC,MAAM,CAACJ,QAAQ;AACjB,CAAC;SAEeF,KAAK,CAACO,SAAiB,EAAW,CAAC;IACjD,MAAM,qVAAqVC,IAAI,CAC7VD,SAAS;AAEb,CAAC;SAEeN,sBAAsB,CAACQ,MAAc,EAAE,CAAC;IACtD,KAAK,CAACC,YAAY,GAAGD,MAAM,KAAK,CAAY;IAC5C,KAAK,CAACE,iBAAiB,GAAGF,MAAM,KAAK,CAA+B;IACpE,MAAM,CAACC,YAAY,IAAIC,iBAAiB;AAC1C,CAAC"}