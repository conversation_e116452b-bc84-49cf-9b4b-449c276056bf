{"version": 3, "sources": ["../../telemetry/storage.ts"], "names": ["ciEnvironment", "TELEMETRY_KEY_ENABLED", "TELEMETRY_KEY_NOTIFY_DATE", "TELEMETRY_KEY_ID", "TELEMETRY_KEY_SALT", "Telemetry", "distDir", "notify", "isDisabled", "conf", "get", "set", "Date", "now", "toString", "console", "log", "chalk", "magenta", "bold", "cyan", "setEnabled", "_enabled", "enabled", "path", "oneWayHash", "payload", "hash", "createHash", "update", "salt", "digest", "record", "_events", "_this", "wrapper", "submitRecord", "prom", "then", "value", "isFulfilled", "isRejected", "catch", "reason", "res", "queue", "delete", "add", "flush", "Promise", "all", "events", "Array", "isArray", "length", "resolve", "NEXT_TELEMETRY_DEBUG", "for<PERSON>ach", "eventName", "error", "JSON", "stringify", "context", "anonymousId", "projectId", "sessionId", "meta", "getAnonymousMeta", "_postPayload", "map", "fields", "NEXT_TELEMETRY_DISABLED", "process", "env", "storageDirectory", "getStorageDirectory", "Conf", "projectName", "cwd", "_", "randomBytes", "rawProjectId", "getRawProjectId", "Set", "val", "generated", "isEnabled", "isLikelyEphemeral", "isCI", "isDockerFunction", "join", "undefined"], "mappings": "Y;;;E;AAAkB,GAA0B,CAA1B,MAA0B;AAC3B,GAAyB,CAAzB,KAAyB;AACU,GAAQ,CAAR,OAAQ;AAC/B,GAA8B,CAA9B,SAA8B;AAC1C,GAAM,CAAN,KAAM;AAEU,GAAkB,CAAlB,cAAkB;AACvCA,GAAa,CAAbA,aAAa;AACI,GAAgB,CAAhB,YAAgB;AACb,GAAc,CAAd,UAAc;;;;;;;;;;;;;;;;gE;;8C;;;;;4B;;;;AAE9C,EAA+E,AAA/E,6EAA+E;AAC/E,KAAK,CAACC,qBAAqB,GAAG,CAAmB;AAEjD,EAA4E,AAA5E,0EAA4E;AAC5E,EAAwB,AAAxB,sBAAwB;AACxB,KAAK,CAACC,yBAAyB,GAAG,CAAsB;AAExD,EAA8E,AAA9E,4EAA8E;AAC9E,EAAuD,AAAvD,qDAAuD;AACvD,KAAK,CAACC,gBAAgB,IAAI,qBAAqB;AAE/C,EAA6E,AAA7E,2EAA6E;AAC7E,EAA+E,AAA/E,6EAA+E;AAC/E,EAAoE,AAApE,kEAAoE;AACpE,EAAiC,AAAjC,+BAAiC;AACjC,KAAK,CAACC,kBAAkB,IAAI,cAAc;MAqB7BC,SAAS;gBASR,CAAC,CAACC,OAAO,EAAsB,CAAC,CAAE,CAAC;QAT1C,IAsMN,CAtKSC,MAAM,OAAS,CAAC;YACtB,EAAE,EAAE,IAAI,CAACC,UAAU,KAAK,IAAI,CAACC,IAAI,EAAE,CAAC;gBAClC,MAAM;YACR,CAAC;YAED,EAA6E,AAA7E,2EAA6E;YAC7E,EAAgD,AAAhD,8CAAgD;YAChD,EAAyE,AAAzE,uEAAyE;YACzE,EAAa,AAAb,WAAa;YACb,EAAE,EAAE,IAAI,CAACA,IAAI,CAACC,GAAG,CAACR,yBAAyB,EAAE,CAAE,IAAG,CAAC;gBACjD,MAAM;YACR,CAAC;YACD,IAAI,CAACO,IAAI,CAACE,GAAG,CAACT,yBAAyB,EAAEU,IAAI,CAACC,GAAG,GAAGC,QAAQ,G;YAE5DC,OAAO,CAACC,GAAG,IACNC,MAAK,SAACC,OAAO,CAACC,IAAI,CACnB,CAAW,YACX,sEAAsE,E;YAE1EJ,OAAO,CAACC,GAAG,EACR,2EAA2E,E;YAE9ED,OAAO,CAACC,GAAG,EACR,uIAAuI,E;YAE1ID,OAAO,CAACC,GAAG,CAACC,MAAK,SAACG,IAAI,CAAC,CAA8B,+B;YACrDL,OAAO,CAACC,GAAG,E;QACb,CAAC,AA1GH,CA0GG;QA3DI,IAsMN,CA5GCK,UAAU,IAAIC,QAAiB,GAAK,CAAC;YACnC,KAAK,CAACC,OAAO,KAAKD,QAAQ;YAC1B,IAAI,CAACb,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAACV,qBAAqB,EAAEsB,OAAO,C;YACzD,MAAM,CAAC,IAAI,CAACd,IAAI,IAAI,IAAI,CAACA,IAAI,CAACe,IAAI;QACpC,CAAC,AA7IH,CA6IG;QA9FI,IAsMN,CAlGCC,UAAU,IAAIC,OAAmB,GAAa,CAAC;YAC7C,KAAK,CAACC,IAAI,OAAGC,OAAU,aAAC,CAAQ;YAEhC,EAA6E,AAA7E,2EAA6E;YAC7E,EAAW,AAAX,SAAW;YACXD,IAAI,CAACE,MAAM,CAAC,IAAI,CAACC,IAAI,C;YAErB,EAA4E,AAA5E,0EAA4E;YAC5E,EAA2B,AAA3B,yBAA2B;YAC3BH,IAAI,CAACE,MAAM,CAACH,OAAO,C;YACnB,MAAM,CAACC,IAAI,CAACI,MAAM,CAAC,CAAK;QAC1B,CAAC,AA9JH,CA8JG;QA/GI,IAsMN,CAjFCC,MAAM,IACJC,OAA0C,GAChB,CAAC;YAC3B,KAAK,CAACC,KAAK,GAAG,IAAI;YAClB,EAAmB,AAAnB,iBAAmB;2BACJC,OAAO,GAAG,CAAC;gBACxB,MAAM,CAAC,KAAK,CAACD,KAAK,CAACE,YAAY,CAACH,OAAO;YACzC,CAAC;YAED,KAAK,CAACI,IAAI,GAAGF,OAAO,GACjBG,IAAI,EAAEC,KAAK,IAAM,CAAC;oBACjBC,WAAW,EAAE,IAAI;oBACjBC,UAAU,EAAE,KAAK;oBACjBF,KAAK;gBACP,CAAC;cACAG,KAAK,EAAEC,MAAM,IAAM,CAAC;oBACnBH,WAAW,EAAE,KAAK;oBAClBC,UAAU,EAAE,IAAI;oBAChBE,MAAM;gBACR,CAAC;aACD,EAAiE,AAAjE,+DAAiE;aAChEL,IAAI,EAAEM,GAAG,GAAK,CAAC;gBACd,EAAuD,AAAvD,qDAAuD;gBACvD,IAAI,CAACC,KAAK,CAACC,MAAM,CAACT,IAAI,C;gBACtB,MAAM,CAACO,GAAG;YACZ,CAAC;YAEH,EAAsD,AAAtD,oDAAsD;YACtD,IAAI,CAACC,KAAK,CAACE,GAAG,CAACV,IAAI,C;YAEnB,MAAM,CAACA,IAAI;QACb,CAAC,AAnMH,CAmMG;QApJI,IAsMN,CAhDCW,KAAK,aAAeC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACL,KAAK,EAAEH,KAAK,KAAO,IAAI;;QArM9D,CAqM+D;QAtJxD,IAsMN,CA9CSN,YAAY,IAClBH,OAA0C,GACzB,CAAC;YAClB,GAAG,CAACkB,MAAM;YACV,EAAE,EAAEC,KAAK,CAACC,OAAO,CAACpB,OAAO,GAAG,CAAC;gBAC3BkB,MAAM,GAAGlB,OAAO,A;YAClB,CAAC,MAAM,CAAC;gBACNkB,MAAM,GAAG,CAAClB;oBAAAA,OAAO;gBAAA,CAAC,A;YACpB,CAAC;YAED,EAAE,EAAEkB,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,MAAM,CAACL,OAAO,CAACM,OAAO;YACxB,CAAC;YAED,EAAE,EAAE,IAAI,CAACC,oBAAoB,EAAE,CAAC;gBAC9B,EAA2D,AAA3D,yDAA2D;gBAC3DL,MAAM,CAACM,OAAO,EAAE,CAAC,CAACC,SAAS,GAAEhC,OAAO,EAAC,CAAC,GACpCX,OAAO,CAAC4C,KAAK,EACV,YAAY,IAAIC,IAAI,CAACC,SAAS,CAAC,CAAC;wBAACH,SAAS;wBAAEhC,OAAO;oBAAC,CAAC,EAAE,IAAI,EAAE,CAAC;iB;gBAGnE,EAA0E,AAA1E,wEAA0E;gBAC1E,EAAsC,AAAtC,oCAAsC;gBACtC,MAAM,CAACuB,OAAO,CAACM,OAAO;YACxB,CAAC;YAED,EAAsD,AAAtD,oDAAsD;YACtD,EAAE,EAAE,IAAI,CAAC/C,UAAU,EAAE,CAAC;gBACpB,MAAM,CAACyC,OAAO,CAACM,OAAO;YACxB,CAAC;YAED,KAAK,CAACO,OAAO,GAAiB,CAAC;gBAC7BC,WAAW,EAAE,IAAI,CAACA,WAAW;gBAC7BC,SAAS,EAAE,IAAI,CAACA,SAAS;gBACzBC,SAAS,EAAE,IAAI,CAACA,SAAS;YAC3B,CAAC;YACD,KAAK,CAACC,IAAI,OAAcC,cAAgB;YACxC,MAAM,KAACC,YAAY,gBAAE,0CAA0C,GAAG,CAAC;gBACjEN,OAAO;gBACPI,IAAI;gBACJf,MAAM,EAAEA,MAAM,CAACkB,GAAG,EAAE,CAAC,CAACX,SAAS,GAAEhC,OAAO,EAAC,CAAC,IAAM,CAAC;wBAC/CgC,SAAS;wBACTY,MAAM,EAAE5C,OAAO;oBACjB,CAAC;;YACH,CAAC;QACH,CAAC,AApPH,CAoPG;QA3LC,EAAoE,AAApE,kEAAoE;QACpE,KAAK,CAAC,CAAC,CAAC6C,uBAAuB,GAAEf,oBAAoB,EAAC,CAAC,GAAGgB,OAAO,CAACC,GAAG;QACrE,IAAI,CAACF,uBAAuB,GAAGA,uBAAuB,A;QACtD,IAAI,CAACf,oBAAoB,GAAGA,oBAAoB,A;QAChD,KAAK,CAACkB,gBAAgB,GAAGC,mBAAmB,CAACrE,OAAO;QAEpD,GAAG,CAAC,CAAC;YACH,EAAqE,AAArE,mEAAqE;YACrE,EAAwE,AAAxE,sEAAwE;YACxE,EAAqB,AAArB,mBAAqB;YACrB,IAAI,CAACG,IAAI,GAAG,GAAG,CAACmE,KAAI,SAAC,CAAC;gBAACC,WAAW,EAAE,CAAQ;gBAAEC,GAAG,EAAEJ,gBAAgB;YAAC,CAAC,C;QACvE,CAAC,CAAC,KAAK,EAAEK,CAAC,EAAE,CAAC;YACX,IAAI,CAACtE,IAAI,GAAG,IAAI,A;QAClB,CAAC;QACD,IAAI,CAACwD,SAAS,OAAGe,OAAW,cAAC,EAAE,EAAElE,QAAQ,CAAC,CAAK,K;QAC/C,IAAI,CAACmE,YAAY,OAAGC,UAAe,mB;QAEnC,IAAI,CAACrC,KAAK,GAAG,GAAG,CAACsC,GAAG,E;QAEpB,IAAI,CAAC5E,MAAM,E;IACb,CAAC;QA+BGwD,WAAW,GAAW,CAAC;QACzB,KAAK,CAACqB,GAAG,GAAG,IAAI,CAAC3E,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,CAACP,gBAAgB;QACvD,EAAE,EAAEiF,GAAG,EAAE,CAAC;YACR,MAAM,CAACA,GAAG;QACZ,CAAC;QAED,KAAK,CAACC,SAAS,OAAGL,OAAW,cAAC,EAAE,EAAElE,QAAQ,CAAC,CAAK;QAChD,IAAI,CAACL,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAACR,gBAAgB,EAAEkF,SAAS,C;QACtD,MAAM,CAACA,SAAS;IAClB,CAAC;QAEGvD,IAAI,GAAW,CAAC;QAClB,KAAK,CAACsD,GAAG,GAAG,IAAI,CAAC3E,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,CAACN,kBAAkB;QACzD,EAAE,EAAEgF,GAAG,EAAE,CAAC;YACR,MAAM,CAACA,GAAG;QACZ,CAAC;QAED,KAAK,CAACC,SAAS,OAAGL,OAAW,cAAC,EAAE,EAAElE,QAAQ,CAAC,CAAK;QAChD,IAAI,CAACL,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAACP,kBAAkB,EAAEiF,SAAS,C;QACxD,MAAM,CAACA,SAAS;IAClB,CAAC;QAEW7E,UAAU,GAAY,CAAC;QACjC,EAAE,IAAI,IAAI,CAAC+D,uBAAuB,KAAK,IAAI,CAAC9D,IAAI,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI;QACb,CAAC;QACD,MAAM,CAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAACT,qBAAqB,EAAE,IAAI,MAAM,KAAK;IAC7D,CAAC;QAQGqF,SAAS,GAAY,CAAC;QACxB,MAAM,GAAG,IAAI,CAAC7E,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,CAACT,qBAAqB,EAAE,IAAI,MAAM,KAAK;IAC5E,CAAC;QAeW+D,SAAS,GAAW,CAAC;QAC/B,MAAM,CAAC,IAAI,CAACvC,UAAU,CAAC,IAAI,CAACwD,YAAY;IAC1C,CAAC;;QAnHU5E,SAAS,GAATA,SAAS,A;SAwMbsE,mBAAmB,CAACrE,OAAe,EAAsB,CAAC;IACjE,KAAK,CAACiF,iBAAiB,GAAGvF,aAAa,CAACwF,IAAI,QAAIC,SAAgB;IAEhE,EAAE,EAAEF,iBAAiB,EAAE,CAAC;QACtB,MAAM,CAAC/D,KAAI,SAACkE,IAAI,CAACpF,OAAO,EAAE,CAAO;IACnC,CAAC;IAED,MAAM,CAACqF,SAAS;AAClB,CAAC"}