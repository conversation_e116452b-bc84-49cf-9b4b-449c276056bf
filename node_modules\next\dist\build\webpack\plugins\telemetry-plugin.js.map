{"version": 3, "sources": ["../../../../build/webpack/plugins/telemetry-plugin.ts"], "names": ["FEATURE_MODULE_MAP", "Map", "BUILD_FEATURES", "TelemetryPlugin", "buildFeaturesMap", "usageTracker", "featureName", "set", "invocationCount", "get", "keys", "apply", "compiler", "hooks", "make", "tapAsync", "name", "compilation", "callback", "finishModules", "modules", "modulesFinish", "module", "feature", "findFeatureInModule", "connections", "moduleGraph", "getIncomingConnections", "originModules", "findUniqueOriginModulesInConnections", "size", "usages", "values", "type", "path", "identifier", "replace", "endsWith", "Set", "connection", "has", "originModule", "add"], "mappings": "Y;;;E;AAuDA,EAAsE,AAAtE,oEAAsE;AACtE,KAAK,CAACA,kBAAkB,GAAiC,GAAG,CAACC,GAAG,CAAC,CAAC;IAChE,CAAC;QAAA,CAAY;QAAE,CAAgB;IAAA,CAAC;IAChC,CAAC;QAAA,CAAa;QAAE,CAAiB;IAAA,CAAC;IAClC,CAAC;QAAA,CAAc;QAAE,CAAkB;IAAA,CAAC;AACtC,CAAC;AAED,EAAuD,AAAvD,qDAAuD;AACvD,KAAK,CAACC,cAAc,GAAmB,CAAC;IACtC,CAAW;IACX,CAAW;IACX,CAAU;IACV,CAAqB;IACrB,CAA0B;IAC1B,CAA2B;IAC3B,CAAkB;IAClB,CAAiB;IACjB,CAAY;IACZ,CAAgC;IAChC,CAAqC;IACrC,CAAmC;IACnC,CAAiC;IACjC,CAAsC;IACtC,CAA0C;IAC1C,CAAiC;IACjC,CAAkC;IAClC,CAAkC;IAClC,CAAmC;IACnC,CAAsC;IACtC,CAAuC;IACvC,CAAoC;AACtC,CAAC;MAOYC,eAAe;IAG1B,EAAqE,AAArE,mEAAqE;gBACzDC,gBAAuC,CAAE,CAAC;QAJjD,IAmDN,CAlDSC,YAAY,GAAG,GAAG,CAACJ,GAAG,EA9FhC,CA8FyD;QAIrD,GAAG,EAAE,KAAK,CAACK,WAAW,IAAIJ,cAAc,CAAE,CAAC;YACzC,IAAI,CAACG,YAAY,CAACE,GAAG,CAACD,WAAW,EAAE,CAAC;gBAClCA,WAAW;gBACXE,eAAe,EAAEJ,gBAAgB,CAACK,GAAG,CAACH,WAAW,IAAI,CAAC,GAAG,CAAC;YAC5D,CAAC,C;QACH,CAAC;QAED,GAAG,EAAE,KAAK,CAACA,YAAW,IAAIN,kBAAkB,CAACU,IAAI,GAAI,CAAC;YACpD,IAAI,CAACL,YAAY,CAACE,GAAG,CAACD,YAAW,EAAE,CAAC;gBAClCA,WAAW,EAAXA,YAAW;gBACXE,eAAe,EAAE,CAAC;YACpB,CAAC,C;QACH,CAAC;IACH,CAAC;IAEDG,KAAK,CAACC,QAA0B,EAAQ,CAAC;QACvCA,QAAQ,CAACC,KAAK,CAACC,IAAI,CAACC,QAAQ,CAC1BZ,eAAe,CAACa,IAAI,SACbC,WAAgC,EAAEC,QAAoB,GAAK,CAAC;YACjED,WAAW,CAACJ,KAAK,CAACM,aAAa,CAACJ,QAAQ,CACtCZ,eAAe,CAACa,IAAI,SACbI,OAAyB,EAAEC,aAAyB,GAAK,CAAC;gBAC/D,GAAG,EAAE,KAAK,CAACC,MAAM,IAAIF,OAAO,CAAE,CAAC;oBAC7B,KAAK,CAACG,OAAO,GAAGC,mBAAmB,CAACF,MAAM;oBAC1C,EAAE,GAAGC,OAAO,EAAE,CAAC;wBACb,QAAQ;oBACV,CAAC;oBACD,KAAK,CAACE,WAAW,GACfR,WAAW,CACXS,WAAW,CAACC,sBAAsB,CAACL,MAAM;oBAC3C,KAAK,CAACM,aAAa,GACjBC,oCAAoC,CAACJ,WAAW;oBAClD,IAAI,CAACpB,YAAY,CAACI,GAAG,CAACc,OAAO,EAAGf,eAAe,GAC7CoB,aAAa,CAACE,IAAI,A;gBACtB,CAAC;gBACDT,aAAa,E;YACf,CAAC,C;YAEHH,QAAQ,E;QACV,CAAC,C;IAEL,CAAC;IAEDa,MAAM,GAAmB,CAAC;QACxB,MAAM,CAAC,CAAC;eAAG,IAAI,CAAC1B,YAAY,CAAC2B,MAAM;QAAE,CAAC;IACxC,CAAC;;QAlDU7B,eAAe,GAAfA,eAAe,A;AAqD5B,EAEG,AAFH;;CAEG,AAFH,EAEG,UACMqB,mBAAmB,CAACF,MAAc,EAAuB,CAAC;IACjE,EAAE,EAAEA,MAAM,CAACW,IAAI,KAAK,CAAiB,kBAAE,CAAC;QACtC,MAAM;IACR,CAAC;IACD,GAAG,EAAE,KAAK,EAAEV,OAAO,EAAEW,IAAI,KAAKlC,kBAAkB,CAAE,CAAC;QACjD,EAAE,EAAEsB,MAAM,CAACa,UAAU,GAAGC,OAAO,QAAQ,CAAG,IAAEC,QAAQ,CAACH,IAAI,GAAG,CAAC;YAC3D,MAAM,CAACX,OAAO;QAChB,CAAC;IACH,CAAC;AACH,CAAC;AAED,EAIG,AAJH;;;;CAIG,AAJH,EAIG,UACMM,oCAAoC,CAC3CJ,WAAyB,EACX,CAAC;IACf,KAAK,CAACG,aAAa,GAAG,GAAG,CAACU,GAAG;IAC7B,GAAG,EAAE,KAAK,CAACC,UAAU,IAAId,WAAW,CAAE,CAAC;QACrC,EAAE,GAAGG,aAAa,CAACY,GAAG,CAACD,UAAU,CAACE,YAAY,GAAG,CAAC;YAChDb,aAAa,CAACc,GAAG,CAACH,UAAU,CAACE,YAAY,C;QAC3C,CAAC;IACH,CAAC;IACD,MAAM,CAACb,aAAa;AACtB,CAAC"}