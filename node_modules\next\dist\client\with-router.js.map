{"version": 3, "sources": ["../../client/with-router.tsx"], "names": ["with<PERSON><PERSON><PERSON>", "ComposedComponent", "WithRouterWrapper", "props", "router", "useRouter", "getInitialProps", "origGetInitialProps", "process", "env", "NODE_ENV", "name", "displayName"], "mappings": "Y;;;E;kBAawBA,UAAU,A;AAbhB,GAAO,CAAP,MAAO;AAEa,GAAU,CAAV,OAAU;SAWxBA,UAAU,CAIhCC,iBAA+C,EACH,CAAC;aACpCC,iBAAiB,CAACC,KAAU,EAAe,CAAC;QACnD,MAAM,4CAAEF,iBAAiB;YAACG,MAAM,MAAEC,OAAS;WAAQF,KAAK;IAC1D,CAAC;IAEDD,iBAAiB,CAACI,eAAe,GAAGL,iBAAiB,CAACK,eAAe,CAEpE;IAACJ,iBAAiB,CAASK,mBAAmB,GAC7CN,iBAAiB,CACjBM,mBAAmB,A;IACrB,EAAE,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,CAAY,aAAE,CAAC;QAC1C,KAAK,CAACC,IAAI,GACRV,iBAAiB,CAACW,WAAW,IAAIX,iBAAiB,CAACU,IAAI,IAAI,CAAS;QACtET,iBAAiB,CAACU,WAAW,IAAI,WAAW,EAAED,IAAI,CAAC,CAAC,C;IACtD,CAAC;IAED,MAAM,CAACT,iBAAiB;AAC1B,CAAC"}