{"name": "@typescript-eslint/types", "version": "8.35.1", "description": "Types for the TypeScript-ESTree AST spec", "files": ["dist", "!*.tsbuil<PERSON><PERSON>", "package.json", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/types"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ src/generated/ coverage/", "copy-ast-spec": "tsx ./tools/copy-ast-spec.mts", "format": "yarn run -T format", "generate-lib": "yarn run -BT nx run scope-manager:generate-lib", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "rimraf": "*", "tsx": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "nx": {"name": "types", "includedScripts": ["clean"], "targets": {"build": {"dependsOn": ["copy-ast-spec"]}, "copy-ast-spec": {"command": "tsx ./tools/copy-ast-spec.mts", "options": {"cwd": "{projectRoot}"}, "dependsOn": ["ast-spec:build"], "outputs": ["{projectRoot}/src/generated"], "cache": true}}}}