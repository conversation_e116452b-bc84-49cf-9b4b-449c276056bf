{"version": 3, "sources": ["../../../../../server/lib/squoosh/png/squoosh_png.js"], "names": ["encode", "decode", "cleanup", "wasm", "cachedTextDecoder", "TextDecoder", "ignoreBOM", "fatal", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "getStringFromWasm0", "ptr", "len", "subarray", "cachegetUint8ClampedMemory0", "getUint8ClampedMemory0", "Uint8ClampedArray", "getClampedArrayU8FromWasm0", "heap", "Array", "fill", "undefined", "push", "heap_next", "length", "addHeapObject", "obj", "idx", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "getArrayU8FromWasm0", "data", "width", "height", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "getObject", "dropObject", "takeObject", "ret", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "wbg", "__wbg_newwithownedu8clampedarrayandsh_787b2db8ea6bfd62", "arg0", "arg1", "arg2", "arg3", "v0", "ImageData", "__wbindgen_throw", "Error", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module"], "mappings": "Y;;;E;QAmFgBA,MAAM,GAANA,MAAM,A;QAmCNC,MAAM,GAANA,MAAM,A;QA2DNC,OAAO,GAAPA,OAAO,A;wB;AAjLvB,GAAG,CAACC,IAAI;AAER,GAAG,CAACC,iBAAiB,GAAG,GAAG,CAACC,WAAW,CAAC,CAAO,QAAE,CAAC;IAChDC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,IAAI;AACb,CAAC;AAEDH,iBAAiB,CAACH,MAAM,E;AAExB,GAAG,CAACO,oBAAoB,GAAG,IAAI;SACtBC,eAAe,GAAG,CAAC;IAC1B,EAAE,EACAD,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACE,MAAM,KAAKP,IAAI,CAACQ,MAAM,CAACD,MAAM,EAClD,CAAC;QACDF,oBAAoB,GAAG,GAAG,CAACI,UAAU,CAACT,IAAI,CAACQ,MAAM,CAACD,MAAM,C;IAC1D,CAAC;IACD,MAAM,CAACF,oBAAoB;AAC7B,CAAC;SAEQK,kBAAkB,CAACC,GAAG,EAAEC,GAAG,EAAE,CAAC;IACrC,MAAM,CAACX,iBAAiB,CAACH,MAAM,CAACQ,eAAe,GAAGO,QAAQ,CAACF,GAAG,EAAEA,GAAG,GAAGC,GAAG;AAC3E,CAAC;AAED,GAAG,CAACE,2BAA2B,GAAG,IAAI;SAC7BC,sBAAsB,GAAG,CAAC;IACjC,EAAE,EACAD,2BAA2B,KAAK,IAAI,IACpCA,2BAA2B,CAACP,MAAM,KAAKP,IAAI,CAACQ,MAAM,CAACD,MAAM,EACzD,CAAC;QACDO,2BAA2B,GAAG,GAAG,CAACE,iBAAiB,CAAChB,IAAI,CAACQ,MAAM,CAACD,MAAM,C;IACxE,CAAC;IACD,MAAM,CAACO,2BAA2B;AACpC,CAAC;SAEQG,0BAA0B,CAACN,GAAG,EAAEC,GAAG,EAAE,CAAC;IAC7C,MAAM,CAACG,sBAAsB,GAAGF,QAAQ,CAACF,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,GAAGC,GAAG;AACjE,CAAC;AAED,KAAK,CAACM,IAAI,GAAG,GAAG,CAACC,KAAK,CAAC,EAAE,EAAEC,IAAI,CAACC,SAAS;AAEzCH,IAAI,CAACI,IAAI,CAACD,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,C;AAEtC,GAAG,CAACE,SAAS,GAAGL,IAAI,CAACM,MAAM;SAElBC,aAAa,CAACC,GAAG,EAAE,CAAC;IAC3B,EAAE,EAAEH,SAAS,KAAKL,IAAI,CAACM,MAAM,EAAEN,IAAI,CAACI,IAAI,CAACJ,IAAI,CAACM,MAAM,GAAG,CAAC,C;IACxD,KAAK,CAACG,GAAG,GAAGJ,SAAS;IACrBA,SAAS,GAAGL,IAAI,CAACS,GAAG,C;IAEpBT,IAAI,CAACS,GAAG,IAAID,GAAG,A;IACf,MAAM,CAACC,GAAG;AACZ,CAAC;AAED,GAAG,CAACC,eAAe,GAAG,CAAC;SAEdC,iBAAiB,CAACC,GAAG,EAAEC,MAAM,EAAE,CAAC;IACvC,KAAK,CAACpB,GAAG,GAAGoB,MAAM,CAACD,GAAG,CAACN,MAAM,GAAG,CAAC;IACjClB,eAAe,GAAG0B,GAAG,CAACF,GAAG,EAAEnB,GAAG,GAAG,CAAC,C;IAClCiB,eAAe,GAAGE,GAAG,CAACN,MAAM,A;IAC5B,MAAM,CAACb,GAAG;AACZ,CAAC;AAED,GAAG,CAACsB,oBAAoB,GAAG,IAAI;SACtBC,eAAe,GAAG,CAAC;IAC1B,EAAE,EACAD,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAAC1B,MAAM,KAAKP,IAAI,CAACQ,MAAM,CAACD,MAAM,EAClD,CAAC;QACD0B,oBAAoB,GAAG,GAAG,CAACE,UAAU,CAACnC,IAAI,CAACQ,MAAM,CAACD,MAAM,C;IAC1D,CAAC;IACD,MAAM,CAAC0B,oBAAoB;AAC7B,CAAC;SAEQG,mBAAmB,CAACzB,GAAG,EAAEC,GAAG,EAAE,CAAC;IACtC,MAAM,CAACN,eAAe,GAAGO,QAAQ,CAACF,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,GAAGC,GAAG;AAC1D,CAAC;SAOef,MAAM,CAACwC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAE,CAAC;IAC3C,GAAG,CAAC,CAAC;QACH,KAAK,CAACC,MAAM,GAAGxC,IAAI,CAACyC,+BAA+B,EAAE,EAAE;QACvD,GAAG,CAACC,IAAI,GAAGb,iBAAiB,CAACQ,IAAI,EAAErC,IAAI,CAAC2C,iBAAiB;QACzD,GAAG,CAACC,IAAI,GAAGhB,eAAe;QAC1B5B,IAAI,CAACH,MAAM,CAAC2C,MAAM,EAAEE,IAAI,EAAEE,IAAI,EAAEN,KAAK,EAAEC,MAAM,C;QAC7C,GAAG,CAACM,EAAE,GAAGX,eAAe,GAAGM,MAAM,GAAG,CAAC,GAAG,CAAC;QACzC,GAAG,CAACM,EAAE,GAAGZ,eAAe,GAAGM,MAAM,GAAG,CAAC,GAAG,CAAC;QACzC,GAAG,CAACO,EAAE,GAAGX,mBAAmB,CAACS,EAAE,EAAEC,EAAE,EAAEE,KAAK;QAC1ChD,IAAI,CAACiD,eAAe,CAACJ,EAAE,EAAEC,EAAE,GAAG,CAAC,C;QAC/B,MAAM,CAACC,EAAE;IACX,CAAC,QAAS,CAAC;QACT/C,IAAI,CAACyC,+BAA+B,CAAC,EAAE,C;IACzC,CAAC;AACH,CAAC;SAEQS,SAAS,CAACvB,GAAG,EAAE,CAAC;IACvB,MAAM,CAACT,IAAI,CAACS,GAAG;AACjB,CAAC;SAEQwB,UAAU,CAACxB,GAAG,EAAE,CAAC;IACxB,EAAE,EAAEA,GAAG,GAAG,EAAE,EAAE,MAAM;IACpBT,IAAI,CAACS,GAAG,IAAIJ,SAAS,A;IACrBA,SAAS,GAAGI,GAAG,A;AACjB,CAAC;SAEQyB,UAAU,CAACzB,GAAG,EAAE,CAAC;IACxB,KAAK,CAAC0B,GAAG,GAAGH,SAAS,CAACvB,GAAG;IACzBwB,UAAU,CAACxB,GAAG,C;IACd,MAAM,CAAC0B,GAAG;AACZ,CAAC;SAKevD,MAAM,CAACuC,IAAI,EAAE,CAAC;IAC5B,GAAG,CAACK,IAAI,GAAGb,iBAAiB,CAACQ,IAAI,EAAErC,IAAI,CAAC2C,iBAAiB;IACzD,GAAG,CAACC,IAAI,GAAGhB,eAAe;IAC1B,GAAG,CAACyB,GAAG,GAAGrD,IAAI,CAACF,MAAM,CAAC4C,IAAI,EAAEE,IAAI;IAChC,MAAM,CAACQ,UAAU,CAACC,GAAG;AACvB,CAAC;eAEcC,IAAI,CAACC,MAAM,EAAEC,OAAO,EAAE,CAAC;IACpC,EAAE,EAAE,MAAM,CAACC,QAAQ,KAAK,CAAU,aAAIF,MAAM,YAAYE,QAAQ,EAAE,CAAC;QACjE,EAAE,EAAE,MAAM,CAACC,WAAW,CAACC,oBAAoB,KAAK,CAAU,WAAE,CAAC;YAC3D,MAAM,CAAC,KAAK,CAACD,WAAW,CAACC,oBAAoB,CAACJ,MAAM,EAAEC,OAAO;QAC/D,CAAC;QAED,KAAK,CAACI,KAAK,GAAG,KAAK,CAACL,MAAM,CAACM,WAAW;QACtC,MAAM,CAAC,KAAK,CAACH,WAAW,CAACI,WAAW,CAACF,KAAK,EAAEJ,OAAO;IACrD,CAAC,MAAM,CAAC;QACN,KAAK,CAACO,QAAQ,GAAG,KAAK,CAACL,WAAW,CAACI,WAAW,CAACP,MAAM,EAAEC,OAAO;QAE9D,EAAE,EAAEO,QAAQ,YAAYL,WAAW,CAACM,QAAQ,EAAE,CAAC;YAC7C,MAAM,CAAC,CAAC;gBAACD,QAAQ;gBAAER,MAAM;YAAC,CAAC;QAC7B,CAAC,MAAM,CAAC;YACN,MAAM,CAACQ,QAAQ;QACjB,CAAC;IACH,CAAC;AACH,CAAC;eAEcE,IAAI,CAACC,KAAK,EAAE,CAAC;IAC1B,KAAK,CAACV,OAAO,GAAG,CAAC,CAAC;IAClBA,OAAO,CAACW,GAAG,GAAG,CAAC,CAAC,A;IAChBX,OAAO,CAACW,GAAG,CAACC,sDAAsD,GAChE,QAAQ,CAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE,CAAC;QACjC,GAAG,CAACC,EAAE,GAAGxD,0BAA0B,CAACoD,IAAI,EAAEC,IAAI,EAAEtB,KAAK;QACrDhD,IAAI,CAACiD,eAAe,CAACoB,IAAI,EAAEC,IAAI,GAAG,CAAC,C;QACnC,GAAG,CAACjB,GAAG,GAAG,GAAG,CAACqB,SAAS,CAACD,EAAE,EAAEF,IAAI,KAAK,CAAC,EAAEC,IAAI,KAAK,CAAC;QAClD,MAAM,CAAC/C,aAAa,CAAC4B,GAAG;IAC1B,CAAC,A;IACHG,OAAO,CAACW,GAAG,CAACQ,gBAAgB,GAAG,QAAQ,CAAEN,IAAI,EAAEC,IAAI,EAAE,CAAC;QACpD,KAAK,CAAC,GAAG,CAACM,KAAK,CAAClE,kBAAkB,CAAC2D,IAAI,EAAEC,IAAI;IAC/C,CAAC,A;IAED,EAAE,EACA,MAAM,CAACJ,KAAK,KAAK,CAAQ,WACxB,MAAM,CAACW,OAAO,KAAK,CAAU,aAAIX,KAAK,YAAYW,OAAO,IACzD,MAAM,CAACC,GAAG,KAAK,CAAU,aAAIZ,KAAK,YAAYY,GAAG,EAClD,CAAC;QACDZ,KAAK,GAAGa,KAAK,CAACb,KAAK,C;IACrB,CAAC;IAED,KAAK,CAAC,CAAC,CAACH,QAAQ,GAAER,MAAM,EAAC,CAAC,GAAG,KAAK,CAACD,IAAI,CAAC,KAAK,CAACY,KAAK,EAAEV,OAAO;IAE5DxD,IAAI,GAAG+D,QAAQ,CAACiB,OAAO,A;IACvBf,IAAI,CAACgB,sBAAsB,GAAG1B,MAAM,A;IAEpC,MAAM,CAACvD,IAAI;AACb,CAAC;eAEciE,IAAI;0B;SAGHlE,OAAO,GAAG,CAAC;IACzBC,IAAI,GAAG,IAAI,A;IACXc,2BAA2B,GAAG,IAAI,A;IAClCT,oBAAoB,GAAG,IAAI,A;IAC3B4B,oBAAoB,GAAG,IAAI,A;AAC7B,CAAC"}