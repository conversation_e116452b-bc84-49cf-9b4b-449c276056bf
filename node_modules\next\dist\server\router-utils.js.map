{"version": 3, "sources": ["../../server/router-utils.ts"], "names": ["replace<PERSON>ase<PERSON><PERSON>", "has<PERSON>ase<PERSON><PERSON>", "pathname", "basePath", "slice", "length", "startsWith"], "mappings": "Y;;;E;QAAgBA,eAAe,GAAfA,eAAe,A;QAWfC,WAAW,GAAXA,WAAW,A;SAXXD,eAAe,CAACE,QAAgB,EAAEC,QAAgB,EAAU,CAAC;IAC3E,EAAyD,AAAzD,uDAAyD;IACzD,EAAsD,AAAtD,oDAAsD;IACtD,EAA4D,AAA5D,0DAA4D;IAC5D,EAAE,EAAEF,WAAW,CAACC,QAAQ,EAAEC,QAAQ,GAAG,CAAC;QACpCD,QAAQ,GAAGA,QAAQ,CAACE,KAAK,CAACD,QAAQ,CAACE,MAAM,C;QACzC,EAAE,GAAGH,QAAQ,CAACI,UAAU,CAAC,CAAG,KAAGJ,QAAQ,IAAI,CAAC,EAAEA,QAAQ,E;IACxD,CAAC;IACD,MAAM,CAACA,QAAQ;AACjB,CAAC;SAEeD,WAAW,CAACC,QAAgB,EAAEC,QAAgB,EAAW,CAAC;IACxE,MAAM,CACJ,MAAM,CAACD,QAAQ,KAAK,CAAQ,YAC3BA,QAAQ,KAAKC,QAAQ,IAAID,QAAQ,CAACI,UAAU,CAACH,QAAQ,GAAG,CAAG;AAEhE,CAAC"}