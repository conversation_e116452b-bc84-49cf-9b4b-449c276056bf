{"version": 3, "sources": ["../../server/web-server.ts"], "names": ["NextWebServer", "BaseServer", "options", "webServerConfig", "Object", "assign", "renderOpts", "extendRenderOpts", "generateRewrites", "beforeFiles", "afterFiles", "fallback", "handleCompression", "getRoutesManifest", "headers", "rewrites", "redirects", "getPagePath", "getPublicDir", "getBuildId", "globalThis", "__server_context", "buildId", "loadEnvConfig", "getHasStaticDir", "hasMiddleware", "generateImageRoutes", "generateStaticRoutes", "generateFsStaticRoutes", "generatePublicRoutes", "getMiddleware", "generateCatchAllMiddlewareRoute", "undefined", "getFontManifest", "getMiddlewareManifest", "getPagesManifest", "page", "getFilesystemPaths", "Set", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "previewModeSigningKey", "previewModeEncryptionKey", "getServerComponentManifest", "renderHTML", "req", "_res", "pathname", "query", "renderToHTML", "url", "cookies", "disableOptimizedLoading", "runtime", "sendRenderResult", "_req", "res", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "writer", "transformStream", "writable", "getWriter", "result", "isDynamic", "pipe", "write", "chunk", "end", "close", "destroy", "err", "abort", "cork", "uncork", "payload", "toUnchunkedString", "body", "send", "run<PERSON><PERSON>", "findPageComponents", "params", "loadComponent", "components"], "mappings": "Y;;;E;wB;AAQoC,GAAe,CAAf,WAAe;AACtB,GAAU,CAAV,OAAU;;;;;;MAOlBA,aAAa,SAASC,WAAU;gBAGvCC,OAAuD,CAAE,CAAC;QACpE,KAAK,CAACA,OAAO,C;QACb,IAAI,CAACC,eAAe,GAAGD,OAAO,CAACC,eAAe,A;QAC9CC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACC,UAAU,EAAEJ,OAAO,CAACC,eAAe,CAACI,gBAAgB,C;IACzE,CAAC;IACSC,gBAAgB,GAAG,CAAC;QAC5B,EAAyC,AAAzC,uCAAyC;QACzC,MAAM,CAAC,CAAC;YACNC,WAAW,EAAE,CAAC,CAAC;YACfC,UAAU,EAAE,CAAC,CAAC;YACdC,QAAQ,EAAE,CAAC,CAAC;QACd,CAAC;IACH,CAAC;IACSC,iBAAiB,GAAG,CAAC;IAC7B,EAAwE,AAAxE,sEAAwE;IACxE,EAA4E,AAA5E,0EAA4E;IAC9E,CAAC;IACSC,iBAAiB,GAAG,CAAC;QAC7B,MAAM,CAAC,CAAC;YACNC,OAAO,EAAE,CAAC,CAAC;YACXC,QAAQ,EAAE,CAAC;gBACTJ,QAAQ,EAAE,CAAC,CAAC;gBACZD,UAAU,EAAE,CAAC,CAAC;gBACdD,WAAW,EAAE,CAAC,CAAC;YACjB,CAAC;YACDO,SAAS,EAAE,CAAC,CAAC;QACf,CAAC;IACH,CAAC;IACSC,WAAW,GAAG,CAAC;QACvB,EAAQ,AAAR,MAAQ;QACR,MAAM,CAAC,CAAE;IACX,CAAC;IACSC,YAAY,GAAG,CAAC;QACxB,EAAkD,AAAlD,gDAAkD;QAClD,MAAM,CAAC,CAAE;IACX,CAAC;IACSC,UAAU,GAAG,CAAC;QACtB,MAAM,CAAEC,UAAU,CAASC,gBAAgB,CAACC,OAAO;IACrD,CAAC;IACSC,aAAa,GAAG,CAAC;IACzB,EAA2E,AAA3E,yEAA2E;IAC3E,EAAmB,AAAnB,iBAAmB;IACrB,CAAC;IACSC,eAAe,GAAG,CAAC;QAC3B,MAAM,CAAC,KAAK;IACd,CAAC;UACeC,aAAa,GAAG,CAAC;QAC/B,MAAM,CAAC,KAAK;IACd,CAAC;IACSC,mBAAmB,GAAG,CAAC;QAC/B,MAAM,CAAC,CAAC,CAAC;IACX,CAAC;IACSC,oBAAoB,GAAG,CAAC;QAChC,MAAM,CAAC,CAAC,CAAC;IACX,CAAC;IACSC,sBAAsB,GAAG,CAAC;QAClC,MAAM,CAAC,CAAC,CAAC;IACX,CAAC;IACSC,oBAAoB,GAAG,CAAC;QAChC,MAAM,CAAC,CAAC,CAAC;IACX,CAAC;IACSC,aAAa,GAAG,CAAC;QACzB,MAAM,CAAC,CAAC,CAAC;IACX,CAAC;IACSC,+BAA+B,GAAG,CAAC;QAC3C,MAAM,CAACC,SAAS;IAClB,CAAC;IACSC,eAAe,GAAG,CAAC;QAC3B,MAAM,CAACD,SAAS;IAClB,CAAC;IACSE,qBAAqB,GAAG,CAAC;QACjC,MAAM,CAACF,SAAS;IAClB,CAAC;IACSG,gBAAgB,GAAG,CAAC;QAC5B,MAAM,CAAC,CAAC;aACJf,UAAU,CAASC,gBAAgB,CAACe,IAAI,GAAG,CAAE;QACjD,CAAC;IACH,CAAC;IACSC,kBAAkB,GAAG,CAAC;QAC9B,MAAM,CAAC,GAAG,CAACC,GAAG;IAChB,CAAC;IACSC,oBAAoB,GAAG,CAAC;QAChC,MAAM,CAAC,CAAC;YACNC,OAAO,EAAE,CAAC;YACVC,MAAM,EAAE,CAAC,CAAC;YACVC,aAAa,EAAE,CAAC,CAAC;YACjBC,cAAc,EAAE,CAAC,CAAC;YAClBC,OAAO,EAAE,CAAC;gBACRC,aAAa,EAAE,CAAE;gBACjBC,qBAAqB,EAAE,CAAE;gBACzBC,wBAAwB,EAAE,CAAE;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IACSC,0BAA0B,GAAG,CAAC;QACtC,EAAyE,AAAzE,uEAAyE;QACzE,MAAM,CAAChB,SAAS;IAClB,CAAC;UACeiB,UAAU,CACxBC,GAAmB,EACnBC,IAAqB,EACrBC,QAAgB,EAChBC,KAAyB,EACzB/C,UAAsB,EACQ,CAAC;QAC/B,MAAM,KAACgD,OAAY,eACjB,CAAC;YACCC,GAAG,EAAEH,QAAQ;YACbI,OAAO,EAAEN,GAAG,CAACM,OAAO;YACpB1C,OAAO,EAAEoC,GAAG,CAACpC,OAAO;QACtB,CAAC,EACD,CAAC,CAAC,EACFsC,QAAQ,EACRC,KAAK,EACL,CAAC;eACI/C,UAAU;YACbmD,uBAAuB,EAAE,IAAI;YAC7BC,OAAO,EAAE,CAAM;QACjB,CAAC;IAEL,CAAC;UACeC,gBAAgB,CAC9BC,IAAoB,EACpBC,GAAoB,EACpB3D,OAMC,EACc,CAAC;QAChB,EAAyB,AAAzB,uBAAyB;QACzB,EAAiE,AAAjE,+DAAiE;QACjE,EAAE,EAAEA,OAAO,CAAC4D,eAAe,IAAI5D,OAAO,CAAC6D,IAAI,KAAK,CAAM,OAAE,CAAC;YACvDF,GAAG,CAACG,SAAS,CAAC,CAAc,eAAE,CAAS,S;QACzC,CAAC;QACD,EAAE,GAAGH,GAAG,CAACI,SAAS,CAAC,CAAc,gBAAG,CAAC;YACnCJ,GAAG,CAACG,SAAS,CACX,CAAc,eACd9D,OAAO,CAAC6D,IAAI,KAAK,CAAM,QACnB,CAAkB,oBAClB,CAA0B,0B;QAElC,CAAC;QAED,EAAQ,AAAR,MAAQ;QACR,KAAK,CAACG,MAAM,GAAGL,GAAG,CAACM,eAAe,CAACC,QAAQ,CAACC,SAAS;QAErD,EAAE,EAAEnE,OAAO,CAACoE,MAAM,CAACC,SAAS,IAAI,CAAC;YAC/BrE,OAAO,CAACoE,MAAM,CAACE,IAAI,CAAC,CAAC;gBACnBC,KAAK,GAAGC,KAAiB,GAAKR,MAAM,CAACO,KAAK,CAACC,KAAK;;gBAChDC,GAAG,MAAQT,MAAM,CAACU,KAAK;;gBACvBC,OAAO,GAAGC,GAAU,GAAKZ,MAAM,CAACa,KAAK,CAACD,GAAG;;gBACzCE,IAAI,MAAQ,CAAC,CAAC;gBACdC,MAAM,MAAQ,CAAC,CAAC;YAElB,CAAC,C;QACH,CAAC,MAAM,CAAC;YACN,EAAsB,AAAtB,oBAAsB;YACtB,KAAK,CAACC,OAAO,GAAG,KAAK,CAAChF,OAAO,CAACoE,MAAM,CAACa,iBAAiB;YACtDtB,GAAG,CAACuB,IAAI,CAACF,OAAO,C;QAClB,CAAC;QAEDrB,GAAG,CAACwB,IAAI,E;IACV,CAAC;UACeC,MAAM,GAAG,CAAC;QACxB,EAAQ,AAAR,MAAQ;QACR,MAAM,CAAC,IAAI;IACb,CAAC;UACeC,kBAAkB,CAChCnC,QAAgB,EAChBC,KAA0B,EAC1BmC,MAAsB,EACtB,CAAC;QACD,KAAK,CAAClB,MAAM,GAAG,KAAK,CAAC,IAAI,CAACnE,eAAe,CAACsF,aAAa,CAACrC,QAAQ;QAChE,EAAE,GAAGkB,MAAM,EAAE,MAAM,CAAC,IAAI;QAExB,MAAM,CAAC,CAAC;YACNjB,KAAK,EAAE,CAAC;mBACFA,KAAK,IAAI,CAAC,CAAC;mBACXmC,MAAM,IAAI,CAAC,CAAC;YAClB,CAAC;YACDE,UAAU,EAAEpB,MAAM;QACpB,CAAC;IACH,CAAC;;kBA5LkBtE,aAAa,A"}