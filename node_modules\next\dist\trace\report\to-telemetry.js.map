{"version": 3, "sources": ["../../../trace/report/to-telemetry.ts"], "names": ["TRACE_EVENT_ACCESSLIST", "Map", "Object", "entries", "reportToTelemetry", "spanName", "duration", "eventName", "get", "telemetry", "traceGlobals", "record", "payload", "durationInMicroseconds", "flushAll", "report"], "mappings": "Y;;;E;wB;AAA6B,GAAW,CAAX,OAAW;AAExC,KAAK,CAACA,sBAAsB,GAAG,GAAG,CAACC,GAAG,CACpCC,MAAM,CAACC,OAAO,CAAC,CAAC;IACd,CAAqB,sBAAE,CAAqB;AAC9C,CAAC;AAGH,KAAK,CAACC,iBAAiB,IAAIC,QAAgB,EAAEC,QAAgB,GAAK,CAAC;IACjE,KAAK,CAACC,SAAS,GAAGP,sBAAsB,CAACQ,GAAG,CAACH,QAAQ;IACrD,EAAE,GAAGE,SAAS,EAAE,CAAC;QACf,MAAM;IACR,CAAC;IACD,KAAK,CAACE,SAAS,GAAGC,OAAY,cAACF,GAAG,CAAC,CAAW;IAC9C,EAAE,GAAGC,SAAS,EAAE,CAAC;QACf,MAAM;IACR,CAAC;IAEDA,SAAS,CAACE,MAAM,CAAC,CAAC;QAChBJ,SAAS;QACTK,OAAO,EAAE,CAAC;YACRC,sBAAsB,EAAEP,QAAQ;QAClC,CAAC;IACH,CAAC,C;AACH,CAAC;eAEc,CAAC;IACdQ,QAAQ,MAAQ,CAAC,CAAC;IAClBC,MAAM,EAAEX,iBAAiB;AAC3B,CAAC;0B"}