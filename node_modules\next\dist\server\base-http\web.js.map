{"version": 3, "sources": ["../../../server/base-http/web.ts"], "names": ["WebNextRequest", "BaseNextRequest", "request", "url", "URL", "method", "href", "slice", "origin", "length", "clone", "body", "headers", "name", "value", "entries", "parseBody", "_limit", "Error", "WebNextResponse", "BaseNextResponse", "sent", "_sent", "transformStream", "TransformStream", "writable", "Headers", "textBody", "undefined", "sendPromise", "Promise", "resolve", "sendResolve", "response", "then", "Response", "readable", "status", "statusCode", "statusText", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "delete", "val", "Array", "isArray", "append", "getHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON>", "split", "map", "v", "trimStart", "get", "<PERSON><PERSON><PERSON><PERSON>", "has", "append<PERSON><PERSON>er", "send", "toResponse"], "mappings": "Y;;;E;AAEkD,GAAS,CAAT,MAAS;MAE9CA,cAAc,SAASC,MAAe;gBAIrCC,OAAgB,CAAE,CAAC;QAC7B,KAAK,CAACC,GAAG,GAAG,GAAG,CAACC,GAAG,CAACF,OAAO,CAACC,GAAG;QAE/B,KAAK,CACHD,OAAO,CAACG,MAAM,EACdF,GAAG,CAACG,IAAI,CAACC,KAAK,CAACJ,GAAG,CAACK,MAAM,CAACC,MAAM,GAChCP,OAAO,CAACQ,KAAK,GAAGC,IAAI,C;QAEtB,IAAI,CAACT,OAAO,GAAGA,OAAO,A;QAEtB,IAAI,CAACU,OAAO,GAAG,CAAC,CAAC,A;QACjB,GAAG,EAAE,KAAK,EAAEC,IAAI,EAAEC,KAAK,KAAKZ,OAAO,CAACU,OAAO,CAACG,OAAO,GAAI,CAAC;YACtD,IAAI,CAACH,OAAO,CAACC,IAAI,IAAIC,KAAK,A;QAC5B,CAAC;IACH,CAAC;UAEKE,SAAS,CAACC,MAAuB,EAAgB,CAAC;QACtD,KAAK,CAAC,GAAG,CAACC,KAAK,CAAC,CAAiD;IACnE,CAAC;;QAtBUlB,cAAc,GAAdA,cAAc,A;MAyBdmB,eAAe,SAASC,MAAgB;QAoB/CC,IAAI,GAAG,CAAC;QACV,MAAM,CAAC,IAAI,CAACC,KAAK;IACnB,CAAC;gBAEkBC,eAAe,GAAG,GAAG,CAACC,eAAe,GAAI,CAAC;QAC3D,KAAK,CAACD,eAAe,CAACE,QAAQ,C;aADbF,eAAe,GAAfA,eAAe,A;QAxB7B,IAqEN,CApESX,OAAO,GAAG,GAAG,CAACc,OAAO,EA9B/B,CA8BiC;QAD1B,IAqEN,CAnESC,QAAQ,GAAuBC,SAAS,AA/BlD,CA+BkD;QAF3C,IAqEN,CAlESN,KAAK,GAAG,KAAK,AAhCvB,CAgCuB;QAHhB,IAqEN,CAhESO,WAAW,GAAG,GAAG,CAACC,OAAO,EAAQC,OAAO,GAAK,CAAC;YACpD,IAAI,CAACC,WAAW,GAAGD,OAAO,A;QAC5B,CAAC,CApCH,CAoCI;QAPG,IAqEN,CA5DSE,QAAQ,GAAG,IAAI,CAACJ,WAAW,CAACK,IAAI,KAAO,CAAC;gBAC1B,SAAa;YAAjC,MAAM,CAAC,GAAG,CAACC,QAAQ,EAAC,SAAa,GAAb,IAAI,CAACR,QAAQ,cAAb,SAAa,cAAb,SAAa,GAAI,IAAI,CAACJ,eAAe,CAACa,QAAQ,EAAE,CAAC;gBACnExB,OAAO,EAAE,IAAI,CAACA,OAAO;gBACrByB,MAAM,EAAE,IAAI,CAACC,UAAU;gBACvBC,UAAU,EAAE,IAAI,CAACC,aAAa;YAChC,CAAC;QACH,CAAC,CA5CH,CA4CI;IAWF,CAAC;IAEDC,SAAS,CAAC5B,IAAY,EAAEC,KAAwB,EAAQ,CAAC;QACvD,IAAI,CAACF,OAAO,CAAC8B,MAAM,CAAC7B,IAAI,C;QACxB,GAAG,EAAE,KAAK,CAAC8B,GAAG,IAAIC,KAAK,CAACC,OAAO,CAAC/B,KAAK,IAAIA,KAAK,GAAG,CAACA;YAAAA,KAAK;QAAA,CAAC,CAAE,CAAC;YACzD,IAAI,CAACF,OAAO,CAACkC,MAAM,CAACjC,IAAI,EAAE8B,GAAG,C;QAC/B,CAAC;QACD,MAAM,CAAC,IAAI;IACb,CAAC;IAEDI,eAAe,CAAClC,IAAY,EAAwB,CAAC;YAE5C,GAAoB;QAD3B,EAAuE,AAAvE,qEAAuE;QACvE,MAAM,EAAC,GAAoB,GAApB,IAAI,CAACmC,SAAS,CAACnC,IAAI,eAAnB,GAAoB,cAApB,IAAI,CAAJ,CACE,GADF,GAAoB,CACvBoC,KAAK,CAAC,CAAG,IACVC,GAAG,EAAEC,CAAC,GAAKA,CAAC,CAACC,SAAS;;IAC3B,CAAC;IAEDJ,SAAS,CAACnC,IAAY,EAAsB,CAAC;YACpC,GAAsB;QAA7B,MAAM,EAAC,GAAsB,GAAtB,IAAI,CAACD,OAAO,CAACyC,GAAG,CAACxC,IAAI,eAArB,GAAsB,cAAtB,GAAsB,GAAIe,SAAS;IAC5C,CAAC;IAED0B,SAAS,CAACzC,IAAY,EAAW,CAAC;QAChC,MAAM,CAAC,IAAI,CAACD,OAAO,CAAC2C,GAAG,CAAC1C,IAAI;IAC9B,CAAC;IAED2C,YAAY,CAAC3C,IAAY,EAAEC,KAAa,EAAQ,CAAC;QAC/C,IAAI,CAACF,OAAO,CAACkC,MAAM,CAACjC,IAAI,EAAEC,KAAK,C;QAC/B,MAAM,CAAC,IAAI;IACb,CAAC;IAEDH,IAAI,CAACG,KAAa,EAAE,CAAC;QACnB,IAAI,CAACa,QAAQ,GAAGb,KAAK,A;QACrB,MAAM,CAAC,IAAI;IACb,CAAC;IAED2C,IAAI,GAAG,CAAC;YACN,IAAI,EAAJ,GAAgB;SAAhB,GAAgB,IAAhB,IAAI,GAAJ,IAAI,EAACzB,WAAW,cAAhB,GAAgB,cAAhB,IAAI,CAAJ,CAAoB,GAApB,GAAgB,CAAhB,IAAoB,CAApB,IAAI,CA3FR,CA2FwB;QACpB,IAAI,CAACV,KAAK,GAAG,IAAI,A;IACnB,CAAC;IAEDoC,UAAU,GAAG,CAAC;QACZ,MAAM,CAAC,IAAI,CAACzB,QAAQ;IACtB,CAAC;;QApEUd,eAAe,GAAfA,eAAe,A"}