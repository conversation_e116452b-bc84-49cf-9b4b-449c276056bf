{"version": 3, "sources": ["../../trace/trace.ts"], "names": ["NUM_OF_MICROSEC_IN_SEC", "BigInt", "count", "getId", "SpanStatus", "Started", "Stopped", "Span", "name", "parentId", "attrs", "startTime", "duration", "status", "id", "_start", "process", "hrtime", "bigint", "now", "Date", "stop", "stopTime", "end", "Number", "MAX_SAFE_INTEGER", "Error", "timestamp", "reporter", "report", "<PERSON><PERSON><PERSON><PERSON>", "manualTraceChild", "span", "setAttribute", "key", "value", "String", "traceFn", "fn", "traceAsyncFn", "trace", "flushAllTraces", "flushAll"], "mappings": "Y;;;E;oE;AACyB,GAAU,CAAV,OAAU;AAEnC,KAAK,CAACA,sBAAsB,GAAGC,MAAM,CAAC,CAAM;AAC5C,GAAG,CAACC,KAAK,GAAG,CAAC;AACb,KAAK,CAACC,KAAK,OAAS,CAAC;IACnBD,KAAK,E;IACL,MAAM,CAACA,KAAK;AACd,CAAC;;+B;UAIWE,UAAU;IAAVA,UAAU,CAAVA,UAAU,CACpBC,CAAO,YAAPA,CAAO,IAAPA,CAAO,Q;IADGD,UAAU,CAAVA,UAAU,CAEpBE,CAAO,YAAPA,CAAO,IAAPA,CAAO,Q;GAFGF,UAAU,0BAAVA,UAAU,O;MAKTG,IAAI;gBAWH,CAAC,CACXC,IAAI,GACJC,QAAQ,GACRC,KAAK,GACLC,SAAS,EAMX,CAAC,CAAE,CAAC;QACF,IAAI,CAACH,IAAI,GAAGA,IAAI,A;QAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,A;QACxB,IAAI,CAACG,QAAQ,GAAG,IAAI,A;QACpB,IAAI,CAACF,KAAK,GAAGA,KAAK,GAAG,CAAC;eAAIA,KAAK;QAAC,CAAC,GAAG,CAAC,CAAC,A;QACtC,IAAI,CAACG,MAAM,GAAGT,UAAU,CAACC,OAAO,A;QAChC,IAAI,CAACS,EAAE,GAAGX,KAAK,E;QACf,IAAI,CAACY,MAAM,GAAGJ,SAAS,IAAIK,OAAO,CAACC,MAAM,CAACC,MAAM,E;QAChD,EAAwE,AAAxE,sEAAwE;QACxE,EAAiD,AAAjD,+CAAiD;QACjD,EAA2I,AAA3I,yIAA2I;QAC3I,EAAwD,AAAxD,sDAAwD;QACxD,EAAiF,AAAjF,+EAAiF;QACjF,IAAI,CAACC,GAAG,GAAGC,IAAI,CAACD,GAAG,E;IACrB,CAAC;IAED,EAAyE,AAAzE,uEAAyE;IACzE,EAA+D,AAA/D,6DAA+D;IAC/D,EAAwE,AAAxE,sEAAwE;IACxE,EAAyC,AAAzC,uCAAyC;IACzCE,IAAI,CAACC,QAAiB,EAAE,CAAC;QACvB,KAAK,CAACC,GAAG,GAAWD,QAAQ,IAAIN,OAAO,CAACC,MAAM,CAACC,MAAM;QACrD,KAAK,CAACN,QAAQ,IAAIW,GAAG,GAAG,IAAI,CAACR,MAAM,IAAIf,sBAAsB;QAC7D,IAAI,CAACa,MAAM,GAAGT,UAAU,CAACE,OAAO,A;QAChC,EAAE,EAAEM,QAAQ,GAAGY,MAAM,CAACC,gBAAgB,EAAE,CAAC;YACvC,KAAK,CAAC,GAAG,CAACC,KAAK,EAAE,4CAA4C,EAAEd,QAAQ;QACzE,CAAC;QACD,KAAK,CAACe,SAAS,GAAG,IAAI,CAACZ,MAAM,GAAGf,sBAAsB;QACtD4B,OAAQ,UAACC,MAAM,CACb,IAAI,CAACrB,IAAI,EACTgB,MAAM,CAACZ,QAAQ,GACfY,MAAM,CAACG,SAAS,GAChB,IAAI,CAACb,EAAE,EACP,IAAI,CAACL,QAAQ,EACb,IAAI,CAACC,KAAK,EACV,IAAI,CAACS,GAAG,C;IAEZ,CAAC;IAEDW,UAAU,CAACtB,IAAY,EAAEE,KAAc,EAAE,CAAC;QACxC,MAAM,CAAC,GAAG,CAACH,IAAI,CAAC,CAAC;YAACC,IAAI;YAAEC,QAAQ,EAAE,IAAI,CAACK,EAAE;YAAEJ,KAAK;QAAC,CAAC;IACpD,CAAC;IAEDqB,gBAAgB,CACdvB,IAAY,EACZG,SAAiB,EACjBW,QAAgB,EAChBZ,KAAc,EACd,CAAC;QACD,KAAK,CAACsB,IAAI,GAAG,GAAG,CAACzB,IAAI,CAAC,CAAC;YAACC,IAAI;YAAEC,QAAQ,EAAE,IAAI,CAACK,EAAE;YAAEJ,KAAK;YAAEC,SAAS;QAAC,CAAC;QACnEqB,IAAI,CAACX,IAAI,CAACC,QAAQ,C;IACpB,CAAC;IAEDW,YAAY,CAACC,GAAW,EAAEC,KAAU,EAAE,CAAC;QACrC,IAAI,CAACzB,KAAK,CAACwB,GAAG,IAAIE,MAAM,CAACD,KAAK,C;IAChC,CAAC;IAEDE,OAAO,CAAIC,EAAW,EAAK,CAAC;QAC1B,GAAG,CAAC,CAAC;YACH,MAAM,CAACA,EAAE;QACX,CAAC,QAAS,CAAC;YACT,IAAI,CAACjB,IAAI,E;QACX,CAAC;IACH,CAAC;UAEKkB,YAAY,CAAID,EAAwB,EAAc,CAAC;QAC3D,GAAG,CAAC,CAAC;YACH,MAAM,CAAC,KAAK,CAACA,EAAE;QACjB,CAAC,QAAS,CAAC;YACT,IAAI,CAACjB,IAAI,E;QACX,CAAC;IACH,CAAC;;QA5FUd,IAAI,GAAJA,IAAI,A;AA+FV,KAAK,CAACiC,KAAK,IAChBhC,IAAY,EACZC,QAAiB,EACjBC,KAAiC,GAC9B,CAAC;IACJ,MAAM,CAAC,GAAG,CAACH,IAAI,CAAC,CAAC;QAACC,IAAI;QAAEC,QAAQ;QAAEC,KAAK;IAAC,CAAC;AAC3C,CAAC;QANY8B,KAAK,GAALA,KAAK,A;AAQX,KAAK,CAACC,cAAc,OAASb,OAAQ,UAACc,QAAQ;;QAAxCD,cAAc,GAAdA,cAAc,A"}