{"version": 3, "sources": ["../../../trace/report/to-json.ts"], "names": ["batcher", "localEndpoint", "serviceName", "ipv4", "port", "reportEvents", "events", "queue", "Set", "flushAll", "Promise", "all", "length", "report", "event", "push", "evts", "slice", "add", "then", "delete", "writeStream", "traceId", "batch", "writeStreamOptions", "flags", "encoding", "RotatingWriteStream", "file", "sizeLimit", "size", "createWriteStream", "fs", "rotate", "end", "unlinkSync", "err", "code", "rotatePromise", "undefined", "write", "data", "drainPromise", "resolve", "_reject", "once", "reportToLocalHost", "name", "duration", "timestamp", "id", "parentId", "attrs", "startTime", "distDir", "traceGlobals", "get", "phase", "process", "env", "TRACE_ID", "randomBytes", "toString", "promises", "mkdir", "recursive", "path", "join", "PHASE_DEVELOPMENT_SERVER", "Infinity", "eventsJson", "JSON", "stringify", "console", "log", "tags"], "mappings": "Y;;;E;QAyBgBA,OAAO,GAAPA,OAAO,A;wB;AAzBK,GAAQ,CAAR,OAAQ;AACP,GAAW,CAAX,OAAW;AACzB,GAAI,CAAJ,GAAI;AACF,GAAM,CAAN,KAAM;AACkB,GAA4B,CAA5B,UAA4B;;;;;;AAErE,KAAK,CAACC,aAAa,GAAG,CAAC;IACrBC,WAAW,EAAE,CAAQ;IACrBC,IAAI,EAAE,CAAW;IACjBC,IAAI,EAAE,IAAI;AACZ,CAAC;SAeeJ,OAAO,CAACK,YAA8C,EAAE,CAAC;IACvE,KAAK,CAACC,MAAM,GAAY,CAAC,CAAC;IAC1B,EAA6D,AAA7D,2DAA6D;IAC7D,KAAK,CAACC,KAAK,GAAG,GAAG,CAACC,GAAG;IACrB,MAAM,CAAC,CAAC;QACNC,QAAQ,YAAc,CAAC;YACrB,KAAK,CAACC,OAAO,CAACC,GAAG,CAACJ,KAAK,C;YACvB,EAAE,EAAED,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,KAAK,CAACP,YAAY,CAACC,MAAM,C;gBACzBA,MAAM,CAACM,MAAM,GAAG,CAAC,A;YACnB,CAAC;QACH,CAAC;QACDC,MAAM,GAAGC,KAAY,GAAK,CAAC;YACzBR,MAAM,CAACS,IAAI,CAACD,KAAK,C;YAEjB,EAAE,EAAER,MAAM,CAACM,MAAM,GAAG,GAAG,EAAE,CAAC;gBACxB,KAAK,CAACI,IAAI,GAAGV,MAAM,CAACW,KAAK;gBACzBX,MAAM,CAACM,MAAM,GAAG,CAAC,A;gBACjB,KAAK,CAACC,MAAM,GAAGR,YAAY,CAACW,IAAI;gBAChCT,KAAK,CAACW,GAAG,CAACL,MAAM,C;gBAChBA,MAAM,CAACM,IAAI,KAAOZ,KAAK,CAACa,MAAM,CAACP,MAAM;iB;YACvC,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAED,GAAG,CAACQ,WAAW;AACf,GAAG,CAACC,OAAO;AACX,GAAG,CAACC,KAAK;AAET,KAAK,CAACC,kBAAkB,GAAG,CAAC;IAC1BC,KAAK,EAAE,CAAG;IACVC,QAAQ,EAAE,CAAM;AAClB,CAAC;MACKC,mBAAmB;gBAOXC,IAAY,EAAEC,SAAiB,CAAE,CAAC;QAC5C,IAAI,CAACD,IAAI,GAAGA,IAAI,A;QAChB,IAAI,CAACE,IAAI,GAAG,CAAC,A;QACb,IAAI,CAACD,SAAS,GAAGA,SAAS,A;QAC1B,IAAI,CAACE,iBAAiB,E;IACxB,CAAC;IACOA,iBAAiB,GAAG,CAAC;QAC3B,IAAI,CAACV,WAAW,GAAGW,GAAE,SAACD,iBAAiB,CAAC,IAAI,CAACH,IAAI,EAAEJ,kBAAkB,C;IACvE,CAAC;IACD,EAAoB,AAApB,kBAAoB;UACNS,MAAM,GAAG,CAAC;QACtB,KAAK,CAAC,IAAI,CAACC,GAAG,E;QACd,GAAG,CAAC,CAAC;YACHF,GAAE,SAACG,UAAU,CAAC,IAAI,CAACP,IAAI,C;QACzB,CAAC,CAAC,KAAK,EAAEQ,GAAG,EAAO,CAAC;YAClB,EAA2C,AAA3C,yCAA2C;YAC3C,EAAE,EAAEA,GAAG,CAACC,IAAI,KAAK,CAAQ,SAAE,CAAC;gBAC1B,KAAK,CAACD,GAAG;YACX,CAAC;QACH,CAAC;QACD,IAAI,CAACN,IAAI,GAAG,CAAC,A;QACb,IAAI,CAACC,iBAAiB,E;QACtB,IAAI,CAACO,aAAa,GAAGC,SAAS,A;IAChC,CAAC;UACKC,KAAK,CAACC,IAAY,EAAiB,CAAC;QACxC,EAAE,EAAE,IAAI,CAACH,aAAa,EAAE,KAAK,CAAC,IAAI,CAACA,aAAa,A;QAEhD,IAAI,CAACR,IAAI,IAAIW,IAAI,CAAC7B,MAAM,A;QACxB,EAAE,EAAE,IAAI,CAACkB,IAAI,GAAG,IAAI,CAACD,SAAS,EAAE,CAAC;YAC/B,KAAK,EAAE,IAAI,CAACS,aAAa,GAAG,IAAI,CAACL,MAAM,G;QACzC,CAAC;QAED,EAAE,GAAG,IAAI,CAACZ,WAAW,CAACmB,KAAK,CAACC,IAAI,EAAE,CAAM,QAAG,CAAC;YAC1C,EAAE,EAAE,IAAI,CAACC,YAAY,KAAKH,SAAS,EAAE,CAAC;gBACpC,IAAI,CAACG,YAAY,GAAG,GAAG,CAAChC,OAAO,EAAQiC,OAAO,EAAEC,OAAO,GAAK,CAAC;oBAC3D,IAAI,CAACvB,WAAW,CAACwB,IAAI,CAAC,CAAO,YAAQ,CAAC;wBACpC,IAAI,CAACH,YAAY,GAAGH,SAAS,A;wBAC7BI,OAAO,E;oBACT,CAAC,C;gBACH,CAAC,C;YACH,CAAC;YACD,KAAK,CAAC,IAAI,CAACD,YAAY,A;QACzB,CAAC;IACH,CAAC;IAEDR,GAAG,GAAkB,CAAC;QACpB,MAAM,CAAC,GAAG,CAACxB,OAAO,EAAEiC,OAAO,GAAK,CAAC;YAC/B,IAAI,CAACtB,WAAW,CAACa,GAAG,CAACS,OAAO,C;QAC9B,CAAC;IACH,CAAC;;AAGH,KAAK,CAACG,iBAAiB,IACrBC,IAAY,EACZC,QAAgB,EAChBC,SAAiB,EACjBC,EAAU,EACVC,QAAiB,EACjBC,KAAc,EACdC,SAAkB,GACf,CAAC;IACJ,KAAK,CAACC,OAAO,GAAGC,OAAY,cAACC,GAAG,CAAC,CAAS;IAC1C,KAAK,CAACC,KAAK,GAAGF,OAAY,cAACC,GAAG,CAAC,CAAO;IACtC,EAAE,GAAGF,OAAO,KAAKG,KAAK,EAAE,CAAC;QACvB,MAAM;IACR,CAAC;IAED,EAAE,GAAGnC,OAAO,EAAE,CAAC;QACbA,OAAO,GAAGoC,OAAO,CAACC,GAAG,CAACC,QAAQ,QAAIC,OAAW,cAAC,CAAC,EAAEC,QAAQ,CAAC,CAAK,K;IACjE,CAAC;IAED,EAAE,GAAGvC,KAAK,EAAE,CAAC;QACXA,KAAK,GAAGvB,OAAO,QAAQM,MAAM,GAAK,CAAC;YACjC,EAAE,GAAGe,WAAW,EAAE,CAAC;gBACjB,KAAK,CAACW,GAAE,SAAC+B,QAAQ,CAACC,KAAK,CAACV,OAAO,EAAE,CAAC;oBAACW,SAAS,EAAE,IAAI;gBAAC,CAAC,C;gBACpD,KAAK,CAACrC,IAAI,GAAGsC,KAAI,SAACC,IAAI,CAACb,OAAO,EAAE,CAAO;gBACvCjC,WAAW,GAAG,GAAG,CAACM,mBAAmB,CACnCC,IAAI,EACJ,EAA0D,AAA1D,wDAA0D;gBAC1D6B,KAAK,KAAKW,UAAwB,4BAAG,QAAQ,GAAGC,QAAQ,C;YAE5D,CAAC;YACD,KAAK,CAACC,UAAU,GAAGC,IAAI,CAACC,SAAS,CAAClE,MAAM;YACxC,GAAG,CAAC,CAAC;gBACH,KAAK,CAACe,WAAW,CAACmB,KAAK,CAAC8B,UAAU,GAAG,CAAI,I;YAC3C,CAAC,CAAC,KAAK,EAAElC,GAAG,EAAE,CAAC;gBACbqC,OAAO,CAACC,GAAG,CAACtC,GAAG,C;YACjB,CAAC;QACH,CAAC,C;IACH,CAAC;IAEDb,KAAK,CAACV,MAAM,CAAC,CAAC;QACZS,OAAO;QACP6B,QAAQ;QACRJ,IAAI;QACJG,EAAE;QACFD,SAAS;QACTD,QAAQ;QACR2B,IAAI,EAAEvB,KAAK;QACXC,SAAS;IACX,CAAC,C;AACH,CAAC;eAEc,CAAC;IACd5C,QAAQ,MACNc,KAAK,GACDA,KAAK,CAACd,QAAQ,GAAGU,IAAI,KAAO,CAAC;YAC3B,KAAK,CAACsC,KAAK,GAAGF,OAAY,cAACC,GAAG,CAAC,CAAO;YACtC,EAA4D,AAA5D,0DAA4D;YAC5D,EAAE,EAAEC,KAAK,KAAKW,UAAwB,2BAAE,CAAC;gBACvC/C,WAAW,CAACa,GAAG,E;YACjB,CAAC;QACH,CAAC,IACDK,SAAS;;IACf1B,MAAM,EAAEiC,iBAAiB;AAC3B,CAAC;0B"}