{"version": 3, "sources": ["../../../server/lib/utils.ts"], "names": ["printAndExit", "getNodeOptionsWithoutInspect", "message", "code", "console", "log", "error", "process", "exit", "NODE_INSPECT_RE", "env", "NODE_OPTIONS", "replace"], "mappings": "Y;;;E;QAAgBA,YAAY,GAAZA,YAAY,A;QAUZC,4BAA4B,GAA5BA,4BAA4B,A;SAV5BD,YAAY,CAACE,OAAe,EAAEC,IAAI,GAAG,CAAC,EAAE,CAAC;IACvD,EAAE,EAAEA,IAAI,KAAK,CAAC,EAAE,CAAC;QACfC,OAAO,CAACC,GAAG,CAACH,OAAO,C;IACrB,CAAC,MAAM,CAAC;QACNE,OAAO,CAACE,KAAK,CAACJ,OAAO,C;IACvB,CAAC;IAEDK,OAAO,CAACC,IAAI,CAACL,IAAI,C;AACnB,CAAC;SAEeF,4BAA4B,GAAG,CAAC;IAC9C,KAAK,CAACQ,eAAe;IACrB,MAAM,EAAEF,OAAO,CAACG,GAAG,CAACC,YAAY,IAAI,CAAE,GAAEC,OAAO,CAACH,eAAe,EAAE,CAAE;AACrE,CAAC"}