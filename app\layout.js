import { Fragment } from "react";
import PreLoader from "../src/layouts/PreLoader";
import "../styles/globals.css";

export const metadata = {
  title: "Kinco - Kindergarten React NextJs Template",
  description: "Kinco - Kindergarten React NextJs Template",
  icons: {
    icon: "/assets/images/Favicon.png",
    apple: "/assets/images/Favicon.png",
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Salsa&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className="main">
        {children}
      </body>
    </html>
  );
}
