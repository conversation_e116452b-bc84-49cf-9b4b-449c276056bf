{"version": 3, "sources": ["../../../shared/lib/side-effect.tsx"], "names": ["isServer", "window", "Component", "props", "emitChange", "_hasHeadManager", "headManager", "updateHead", "reduceComponentsToState", "mountedInstances", "add", "componentDidMount", "componentDidUpdate", "componentWillUnmount", "delete", "render"], "mappings": "Y;;;E;wB;AAAiC,GAAO,CAAP,MAAO;;;;;;;;;;;gE;;8C;;;;;4B;;;;AAExC,KAAK,CAACA,QAAQ,GAAG,MAAM,CAACC,MAAM,KAAK,CAAW;qBAcjBC,MAAS;gBAcxBC,KAAU,CAAE,CAAC;QACvB,KAAK,CAACA,KAAK,C;QAfA,IA2Cd,CAxCCC,UAAU,OAAe,CAAC;YACxB,EAAE,EAAE,IAAI,CAACC,eAAe,EAAE,CAAC;gBACzB,IAAI,CAACF,KAAK,CAACG,WAAW,CAACC,UAAU,CAC/B,IAAI,CAACJ,KAAK,CAACK,uBAAuB,CAChC,CAAC;uBAAG,IAAI,CAACL,KAAK,CAACG,WAAW,CAACG,gBAAgB;gBAAA,CAAC,EAC5C,IAAI,CAACN,KAAK,E;YAGhB,CAAC;QACH,CAAC,AA5BH,CA4BG;QAIC,IAAI,CAACE,eAAe,GAClB,IAAI,CAACF,KAAK,CAACG,WAAW,IAAI,IAAI,CAACH,KAAK,CAACG,WAAW,CAACG,gBAAgB,A;QAEnE,EAAE,EAAET,QAAQ,IAAI,IAAI,CAACK,eAAe,EAAE,CAAC;YACrC,IAAI,CAACF,KAAK,CAACG,WAAW,CAACG,gBAAgB,CAACC,GAAG,CAAC,IAAI,C;YAChD,IAAI,CAACN,UAAU,E;QACjB,CAAC;IACH,CAAC;IACDO,iBAAiB,GAAG,CAAC;QACnB,EAAE,EAAE,IAAI,CAACN,eAAe,EAAE,CAAC;YACzB,IAAI,CAACF,KAAK,CAACG,WAAW,CAACG,gBAAgB,CAACC,GAAG,CAAC,IAAI,C;QAClD,CAAC;QACD,IAAI,CAACN,UAAU,E;IACjB,CAAC;IACDQ,kBAAkB,GAAG,CAAC;QACpB,IAAI,CAACR,UAAU,E;IACjB,CAAC;IACDS,oBAAoB,GAAG,CAAC;QACtB,EAAE,EAAE,IAAI,CAACR,eAAe,EAAE,CAAC;YACzB,IAAI,CAACF,KAAK,CAACG,WAAW,CAACG,gBAAgB,CAACK,MAAM,CAAC,IAAI,C;QACrD,CAAC;QACD,IAAI,CAACV,UAAU,E;IACjB,CAAC;IAEDW,MAAM,GAAG,CAAC;QACR,MAAM,CAAC,IAAI;IACb,CAAC;;wB"}