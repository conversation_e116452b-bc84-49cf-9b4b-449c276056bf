/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/client/head-manager.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/head-manager.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = initHeadManager;\nexports.isEqualNode = isEqualNode;\nexports.DOMAttributeNames = void 0;\nfunction initHeadManager() {\n    let updatePromise = null;\n    return {\n        mountedInstances: new Set(),\n        updateHead: (head)=>{\n            const promise = updatePromise = Promise.resolve().then(()=>{\n                if (promise !== updatePromise) return;\n                updatePromise = null;\n                const tags = {};\n                head.forEach((h)=>{\n                    if (// it won't be inlined. In this case revert to the original behavior\n                    h.type === 'link' && h.props['data-optimized-fonts']) {\n                        if (document.querySelector(`style[data-href=\"${h.props['data-href']}\"]`)) {\n                            return;\n                        } else {\n                            h.props.href = h.props['data-href'];\n                            h.props['data-href'] = undefined;\n                        }\n                    }\n                    const components = tags[h.type] || [];\n                    components.push(h);\n                    tags[h.type] = components;\n                });\n                const titleComponent = tags.title ? tags.title[0] : null;\n                let title = '';\n                if (titleComponent) {\n                    const { children  } = titleComponent.props;\n                    title = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n                }\n                if (title !== document.title) document.title = title;\n                [\n                    'meta',\n                    'base',\n                    'link',\n                    'style',\n                    'script'\n                ].forEach((type)=>{\n                    updateElements(type, tags[type] || []);\n                });\n            });\n        }\n    };\n}\nconst DOMAttributeNames = {\n    acceptCharset: 'accept-charset',\n    className: 'class',\n    htmlFor: 'for',\n    httpEquiv: 'http-equiv',\n    noModule: 'noModule'\n};\nexports.DOMAttributeNames = DOMAttributeNames;\nfunction reactElementToDOM({ type , props  }) {\n    const el = document.createElement(type);\n    for(const p in props){\n        if (!props.hasOwnProperty(p)) continue;\n        if (p === 'children' || p === 'dangerouslySetInnerHTML') continue;\n        // we don't render undefined props to the DOM\n        if (props[p] === undefined) continue;\n        const attr = DOMAttributeNames[p] || p.toLowerCase();\n        if (type === 'script' && (attr === 'async' || attr === 'defer' || attr === 'noModule')) {\n            el[attr] = !!props[p];\n        } else {\n            el.setAttribute(attr, props[p]);\n        }\n    }\n    const { children , dangerouslySetInnerHTML  } = props;\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || '';\n    } else if (children) {\n        el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n    }\n    return el;\n}\nfunction isEqualNode(oldTag, newTag) {\n    if (oldTag instanceof HTMLElement && newTag instanceof HTMLElement) {\n        const nonce = newTag.getAttribute('nonce');\n        // Only strip the nonce if `oldTag` has had it stripped. An element's nonce attribute will not\n        // be stripped if there is no content security policy response header that includes a nonce.\n        if (nonce && !oldTag.getAttribute('nonce')) {\n            const cloneTag = newTag.cloneNode(true);\n            cloneTag.setAttribute('nonce', '');\n            cloneTag.nonce = nonce;\n            return nonce === oldTag.nonce && oldTag.isEqualNode(cloneTag);\n        }\n    }\n    return oldTag.isEqualNode(newTag);\n}\nfunction updateElements(type, components) {\n    const headEl = document.getElementsByTagName('head')[0];\n    const headCountEl = headEl.querySelector('meta[name=next-head-count]');\n    if (true) {\n        if (!headCountEl) {\n            console.error('Warning: next-head-count is missing. https://nextjs.org/docs/messages/next-head-count-missing');\n            return;\n        }\n    }\n    const headCount = Number(headCountEl.content);\n    const oldTags = [];\n    for(let i = 0, j = headCountEl.previousElementSibling; i < headCount; i++, j = (j === null || j === void 0 ? void 0 : j.previousElementSibling) || null){\n        var ref;\n        if ((j === null || j === void 0 ? void 0 : (ref = j.tagName) === null || ref === void 0 ? void 0 : ref.toLowerCase()) === type) {\n            oldTags.push(j);\n        }\n    }\n    const newTags = components.map(reactElementToDOM).filter((newTag)=>{\n        for(let k = 0, len = oldTags.length; k < len; k++){\n            const oldTag = oldTags[k];\n            if (isEqualNode(oldTag, newTag)) {\n                oldTags.splice(k, 1);\n                return false;\n            }\n        }\n        return true;\n    });\n    oldTags.forEach((t)=>{\n        var ref;\n        return (ref = t.parentNode) === null || ref === void 0 ? void 0 : ref.removeChild(t);\n    });\n    newTags.forEach((t)=>headEl.insertBefore(t, headCountEl)\n    );\n    headCountEl.content = (headCount - oldTags.length + newTags.length).toString();\n} //# sourceMappingURL=head-manager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/head-manager.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.cancelIdleCallback = exports.requestIdleCallback = void 0;\nconst requestIdleCallback = typeof self !== 'undefined' && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nexports.requestIdleCallback = requestIdleCallback;\nconst cancelIdleCallback = typeof self !== 'undefined' && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nexports.cancelIdleCallback = cancelIdleCallback; //# sourceMappingURL=request-idle-callback.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9yZXF1ZXN0LWlkbGUtY2FsbGJhY2suanMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDLENBQUM7SUFDMUNHLEtBQUssRUFBRSxJQUFJO0FBQ2YsQ0FBQyxFQUFDLENBQUM7QUFDSEQsMEJBQTBCLEdBQUdBLDJCQUEyQixHQUFHLElBQUksQ0FBQyxDQUFDLENBQUM7QUFDbEUsS0FBSyxDQUFDRyxtQkFBbUIsR0FBRyxNQUFNLENBQUNDLElBQUksS0FBSyxDQUFXLGNBQUlBLElBQUksQ0FBQ0QsbUJBQW1CLElBQUlDLElBQUksQ0FBQ0QsbUJBQW1CLENBQUNFLElBQUksQ0FBQ0MsTUFBTSxLQUFLLFFBQVEsQ0FBQ0MsRUFBRSxFQUFFLENBQUM7SUFDMUksR0FBRyxDQUFDQyxLQUFLLEdBQUdDLElBQUksQ0FBQ0MsR0FBRztJQUNwQixNQUFNLENBQUNDLFVBQVUsQ0FBQyxRQUFRLEdBQUcsQ0FBQztRQUMxQkosRUFBRSxDQUFDLENBQUM7WUFDQUssVUFBVSxFQUFFLEtBQUs7WUFDakJDLGFBQWEsRUFBRSxRQUFRLEdBQUcsQ0FBQztnQkFDdkIsTUFBTSxDQUFDQyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsRUFBRSxJQUFJTixJQUFJLENBQUNDLEdBQUcsS0FBS0YsS0FBSztZQUMvQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDLEVBQUUsQ0FBQztBQUNSLENBQUM7QUFDRFIsMkJBQTJCLEdBQUdHLG1CQUFtQixDQUFDO0FBQ2xELEtBQUssQ0FBQ0Qsa0JBQWtCLEdBQUcsTUFBTSxDQUFDRSxJQUFJLEtBQUssQ0FBVyxjQUFJQSxJQUFJLENBQUNGLGtCQUFrQixJQUFJRSxJQUFJLENBQUNGLGtCQUFrQixDQUFDRyxJQUFJLENBQUNDLE1BQU0sS0FBSyxRQUFRLENBQUNVLEVBQUUsRUFBRSxDQUFDO0lBQ3ZJLE1BQU0sQ0FBQ0MsWUFBWSxDQUFDRCxFQUFFO0FBQzFCLENBQUM7QUFDRGhCLDBCQUEwQixHQUFHRSxrQkFBa0IsQ0FBQyxDQUVoRCxFQUFpRCIsInNvdXJjZXMiOlsid2VicGFjazovL2tpbmNvLXJlYWN0Ly4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvcmVxdWVzdC1pZGxlLWNhbGxiYWNrLmpzPzFlMjAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmNhbmNlbElkbGVDYWxsYmFjayA9IGV4cG9ydHMucmVxdWVzdElkbGVDYWxsYmFjayA9IHZvaWQgMDtcbmNvbnN0IHJlcXVlc3RJZGxlQ2FsbGJhY2sgPSB0eXBlb2Ygc2VsZiAhPT0gJ3VuZGVmaW5lZCcgJiYgc2VsZi5yZXF1ZXN0SWRsZUNhbGxiYWNrICYmIHNlbGYucmVxdWVzdElkbGVDYWxsYmFjay5iaW5kKHdpbmRvdykgfHwgZnVuY3Rpb24oY2IpIHtcbiAgICBsZXQgc3RhcnQgPSBEYXRlLm5vdygpO1xuICAgIHJldHVybiBzZXRUaW1lb3V0KGZ1bmN0aW9uKCkge1xuICAgICAgICBjYih7XG4gICAgICAgICAgICBkaWRUaW1lb3V0OiBmYWxzZSxcbiAgICAgICAgICAgIHRpbWVSZW1haW5pbmc6IGZ1bmN0aW9uKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBNYXRoLm1heCgwLCA1MCAtIChEYXRlLm5vdygpIC0gc3RhcnQpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfSwgMSk7XG59O1xuZXhwb3J0cy5yZXF1ZXN0SWRsZUNhbGxiYWNrID0gcmVxdWVzdElkbGVDYWxsYmFjaztcbmNvbnN0IGNhbmNlbElkbGVDYWxsYmFjayA9IHR5cGVvZiBzZWxmICE9PSAndW5kZWZpbmVkJyAmJiBzZWxmLmNhbmNlbElkbGVDYWxsYmFjayAmJiBzZWxmLmNhbmNlbElkbGVDYWxsYmFjay5iaW5kKHdpbmRvdykgfHwgZnVuY3Rpb24oaWQpIHtcbiAgICByZXR1cm4gY2xlYXJUaW1lb3V0KGlkKTtcbn07XG5leHBvcnRzLmNhbmNlbElkbGVDYWxsYmFjayA9IGNhbmNlbElkbGVDYWxsYmFjaztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVxdWVzdC1pZGxlLWNhbGxiYWNrLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImNhbmNlbElkbGVDYWxsYmFjayIsInJlcXVlc3RJZGxlQ2FsbGJhY2siLCJzZWxmIiwiYmluZCIsIndpbmRvdyIsImNiIiwic3RhcnQiLCJEYXRlIiwibm93Iiwic2V0VGltZW91dCIsImRpZFRpbWVvdXQiLCJ0aW1lUmVtYWluaW5nIiwiTWF0aCIsIm1heCIsImlkIiwiY2xlYXJUaW1lb3V0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/request-idle-callback.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/script.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/client/script.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.initScriptLoader = initScriptLoader;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _headManagerContext = __webpack_require__(/*! ../shared/lib/head-manager-context */ \"../shared/lib/head-manager-context\");\nvar _headManager = __webpack_require__(/*! ./head-manager */ \"./node_modules/next/dist/client/head-manager.js\");\nvar _requestIdleCallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    } else {\n        var newObj = {};\n        if (obj != null) {\n            for(var key in obj){\n                if (Object.prototype.hasOwnProperty.call(obj, key)) {\n                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};\n                    if (desc.get || desc.set) {\n                        Object.defineProperty(newObj, key, desc);\n                    } else {\n                        newObj[key] = obj[key];\n                    }\n                }\n            }\n        }\n        newObj.default = obj;\n        return newObj;\n    }\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === \"function\") {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nconst ScriptCache = new Map();\nconst LoadCache = new Set();\nconst ignoreProps = [\n    'onLoad',\n    'dangerouslySetInnerHTML',\n    'children',\n    'onError',\n    'strategy', \n];\nconst loadScript = (props)=>{\n    const { src , id , onLoad =()=>{} , dangerouslySetInnerHTML , children ='' , strategy ='afterInteractive' , onError ,  } = props;\n    const cacheKey = id || src;\n    // Script has already loaded\n    if (cacheKey && LoadCache.has(cacheKey)) {\n        return;\n    }\n    // Contents of this script are already loading/loaded\n    if (ScriptCache.has(src)) {\n        LoadCache.add(cacheKey);\n        // Execute onLoad since the script loading has begun\n        ScriptCache.get(src).then(onLoad, onError);\n        return;\n    }\n    const el = document.createElement('script');\n    const loadPromise = new Promise((resolve, reject)=>{\n        el.addEventListener('load', function(e) {\n            resolve();\n            if (onLoad) {\n                onLoad.call(this, e);\n            }\n        });\n        el.addEventListener('error', function(e) {\n            reject(e);\n        });\n    }).catch(function(e) {\n        if (onError) {\n            onError(e);\n        }\n    });\n    if (src) {\n        ScriptCache.set(src, loadPromise);\n    }\n    LoadCache.add(cacheKey);\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || '';\n    } else if (children) {\n        el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n    } else if (src) {\n        el.src = src;\n    }\n    for (const [k, value] of Object.entries(props)){\n        if (value === undefined || ignoreProps.includes(k)) {\n            continue;\n        }\n        const attr = _headManager.DOMAttributeNames[k] || k.toLowerCase();\n        el.setAttribute(attr, value);\n    }\n    if (strategy === 'worker') {\n        el.setAttribute('type', 'text/partytown');\n    }\n    el.setAttribute('data-nscript', strategy);\n    document.body.appendChild(el);\n};\nfunction handleClientScriptLoad(props) {\n    const { strategy ='afterInteractive'  } = props;\n    if (strategy === 'afterInteractive') {\n        loadScript(props);\n    } else if (strategy === 'lazyOnload') {\n        window.addEventListener('load', ()=>{\n            (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props)\n            );\n        });\n    }\n}\nfunction loadLazyScript(props) {\n    if (document.readyState === 'complete') {\n        (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props)\n        );\n    } else {\n        window.addEventListener('load', ()=>{\n            (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props)\n            );\n        });\n    }\n}\nfunction initScriptLoader(scriptLoaderItems) {\n    scriptLoaderItems.forEach(handleClientScriptLoad);\n}\nfunction Script(props) {\n    const { src ='' , onLoad =()=>{} , dangerouslySetInnerHTML , strategy ='afterInteractive' , onError  } = props, restProps = _objectWithoutProperties(props, [\n        \"src\",\n        \"onLoad\",\n        \"dangerouslySetInnerHTML\",\n        \"strategy\",\n        \"onError\"\n    ]);\n    // Context is available only during SSR\n    const { updateScripts , scripts , getIsSsr  } = (0, _react).useContext(_headManagerContext.HeadManagerContext);\n    (0, _react).useEffect(()=>{\n        if (strategy === 'afterInteractive') {\n            loadScript(props);\n        } else if (strategy === 'lazyOnload') {\n            loadLazyScript(props);\n        }\n    }, [\n        props,\n        strategy\n    ]);\n    if (strategy === 'beforeInteractive' || strategy === 'worker') {\n        if (updateScripts) {\n            scripts[strategy] = (scripts[strategy] || []).concat([\n                _objectSpread({\n                    src,\n                    onLoad,\n                    onError\n                }, restProps), \n            ]);\n            updateScripts(scripts);\n        } else if (getIsSsr && getIsSsr()) {\n            // Script has already loaded during SSR\n            LoadCache.add(restProps.id || src);\n        } else if (getIsSsr && !getIsSsr()) {\n            loadScript(props);\n        }\n    }\n    return null;\n}\nvar _default = Script;\nexports[\"default\"] = _default; //# sourceMappingURL=script.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/script.js\n");

/***/ }),

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Html = Html;\nexports.Main = Main;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nvar _getPageFiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nvar _utils = __webpack_require__(/*! ../server/utils */ \"../server/utils\");\nvar _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nvar _script = _interopRequireDefault(__webpack_require__(/*! ../client/script */ \"./node_modules/next/dist/client/script.js\"));\nvar _isError = _interopRequireDefault(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nvar _htmlContext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    } else {\n        var newObj = {};\n        if (obj != null) {\n            for(var key in obj){\n                if (Object.prototype.hasOwnProperty.call(obj, key)) {\n                    var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};\n                    if (desc.get || desc.set) {\n                        Object.defineProperty(newObj, key, desc);\n                    } else {\n                        newObj[key] = obj[key];\n                    }\n                }\n            }\n        }\n        newObj.default = obj;\n        return newObj;\n    }\n}\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getPageFiles).getPageFiles(buildManifest, '/_app');\n    const pageFiles = inAmpMode ? [] : (0, _getPageFiles).getPageFiles(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix , buildManifest , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n    ).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`\n        })\n    );\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix , scriptLoader , crossOrigin , nextScriptWorkers  } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || false) return null;\n    try {\n        let { partytownSnippet ,  } = require(/* webpackIgnore: true */ '@builder.io/partytown/integration');\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var ref, ref1;\n            return hasComponentProps(child) && (child === null || child === void 0 ? void 0 : (ref = child.props) === null || ref === void 0 ? void 0 : (ref1 = ref.dangerouslySetInnerHTML) === null || ref1 === void 0 ? void 0 : ref1.__html.length) && 'data-partytown-config' in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy , ...scriptProps } = file;\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n                type: \"text/partytown\",\n                key: scriptProps.src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            }));\n        }));\n    } catch (err) {\n        console.warn(`Warning: Partytown could not be instantiated in your application due to an error. ${err}`);\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader , disableOptimizedLoading , crossOrigin  } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).map((file, index)=>{\n        const { strategy , ...scriptProps } = file;\n        var _defer;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n            key: scriptProps.src || index,\n            defer: (_defer = scriptProps.defer) !== null && _defer !== void 0 ? _defer : !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        }));\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports , assetPrefix , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith('.js') || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var ref;\n    const { assetPrefix , buildManifest , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith('.js')\n    );\n    const lowPriorityScripts = (ref = buildManifest.lowPriorityFiles) === null || ref === void 0 ? void 0 : ref.filter((file)=>file.endsWith('.js')\n    );\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nclass Document extends _react.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\nexports[\"default\"] = Document;\nDocument.__next_internal_document = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nfunction Html(props) {\n    const { inAmpMode , docComponentsRendered , locale  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Html = true;\n    return /*#__PURE__*/ _react.default.createElement(\"html\", Object.assign({}, props, {\n        lang: props.lang || locale || undefined,\n        amp: inAmpMode ? '' : undefined,\n        \"data-ampdevmode\": inAmpMode && \"development\" !== 'production' ? '' : undefined\n    }));\n}\nfunction AmpStyles({ styles  }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var ref, ref2;\n            return el === null || el === void 0 ? void 0 : (ref = el.props) === null || ref === void 0 ? void 0 : (ref2 = ref.dangerouslySetInnerHTML) === null || ref2 === void 0 ? void 0 : ref2.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el)\n                );\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html\n            ).join('').replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, '').replace(/\\/\\*@ sourceURL=.*?\\*\\//g, '')\n        }\n    });\n}\nclass Head extends _react.Component {\n    getCssLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , dynamicImports , crossOrigin , optimizeCss , optimizeFonts ,  } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith('.css')\n        );\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith('.css')\n        )));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f))\n            );\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? '' : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : ''\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports , assetPrefix , devOnlyCacheBusterQueryString , crossOrigin ,  } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith('.js')) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , scriptLoader , crossOrigin ,  } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith('.js');\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })\n            ),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })\n            ), \n        ];\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    handleDocumentScriptLoaderItems(children) {\n        const { scriptLoader  } = this.context;\n        const scriptLoaderItems = [];\n        const filteredChildren = [];\n        _react.default.Children.forEach(children, (child)=>{\n            if (child.type === _script.default) {\n                if (child.props.strategy === 'beforeInteractive') {\n                    scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                        {\n                            ...child.props\n                        }, \n                    ]);\n                    return;\n                } else if ([\n                    'lazyOnload',\n                    'afterInteractive',\n                    'worker'\n                ].includes(child.props.strategy)) {\n                    scriptLoaderItems.push(child.props);\n                    return;\n                }\n            }\n            filteredChildren.push(child);\n        });\n        this.context.__NEXT_DATA__.scriptLoader = scriptLoaderItems;\n        return filteredChildren;\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var ref5, ref3;\n            if ((c === null || c === void 0 ? void 0 : c.type) === 'link' && (c === null || c === void 0 ? void 0 : (ref5 = c.props) === null || ref5 === void 0 ? void 0 : ref5.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url  })=>{\n                var ref, ref4;\n                return c === null || c === void 0 ? void 0 : (ref = c.props) === null || ref === void 0 ? void 0 : (ref4 = ref.href) === null || ref4 === void 0 ? void 0 : ref4.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    'data-href': c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c === null || c === void 0 ? void 0 : (ref3 = c.props) === null || ref3 === void 0 ? void 0 : ref3.children) {\n                const newProps = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            }\n            return c;\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles , ampPath , inAmpMode , hybridAmp , canonicalBase , __NEXT_DATA__ , dangerousAsPath , headTags , unstable_runtimeJS , unstable_JsPreload , disableOptimizedLoading , optimizeCss , optimizeFonts ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head  } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                if (c && c.type === 'link' && c.props['rel'] === 'preload' && c.props['as'] === 'style') {\n                    cssPreloads.push(c);\n                } else {\n                    c && otherHeadElements.push(c);\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var ref;\n                const isReactHelmet = child === null || child === void 0 ? void 0 : (ref = child.props) === null || ref === void 0 ? void 0 : ref['data-react-helmet'];\n                if (!isReactHelmet) {\n                    var ref6;\n                    if ((child === null || child === void 0 ? void 0 : child.type) === 'title') {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child === null || child === void 0 ? void 0 : child.type) === 'meta' && (child === null || child === void 0 ? void 0 : (ref6 = child.props) === null || ref6 === void 0 ? void 0 : ref6.name) === 'viewport') {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            });\n            if (this.props.crossOrigin) console.warn('Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');\n        }\n        if (false) {}\n        children = this.handleDocumentScriptLoaderItems(children);\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type , props  } = child;\n            if (inAmpMode) {\n                let badProp = '';\n                if (type === 'meta' && props.name === 'viewport') {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === 'link' && props.rel === 'canonical') {\n                    hasCanonicalRel = true;\n                } else if (type === 'script') {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf('ampproject') < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === 'text/javascript')) {\n                        badProp = '<script';\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += '/>';\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === 'link' && props.rel === 'amphtml') {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page, inAmpMode);\n        var _nonce, _nonce1;\n        return /*#__PURE__*/ _react.default.createElement(\"head\", Object.assign({}, this.props), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\": inAmpMode ? 'true' : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\": inAmpMode ? 'true' : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }), inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (0, _utils).cleanAmpPath(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce = this.props.nonce) !== null && _nonce !== void 0 ? _nonce : ''\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce1 = this.props.nonce) !== null && _nonce1 !== void 0 ? _nonce1 : ''\n        }), this.context.isDevelopment && // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nexports.Head = Head;\nHead.contextType = _htmlContext.HtmlContext;\nfunction Main() {\n    const { docComponentsRendered  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\nclass NextScript extends _react.Component {\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__  } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (true) {\n                const bytes = Buffer.from(data).byteLength;\n                const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n                if (bytes > 128 * 1000) {\n                    console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\" is ${prettyBytes(bytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n                }\n            }\n            return (0, _htmlescape).htmlEscapeJsonString(data);\n        } catch (err) {\n            if ((0, _isError).default(err) && err.message.indexOf('circular structure') !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix , inAmpMode , buildManifest , unstable_runtimeJS , docComponentsRendered , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if (inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles, \n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })\n            ));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn('Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page, inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })\n        ) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nexports.NextScript = NextScript;\nNextScript.contextType = _htmlContext.HtmlContext;\nNextScript.safariNomoduleFix = '!function(){var e=document,t=e.createElement(\"script\");if(!(\"noModule\"in t)&&\"onbeforeload\"in t){var n=!1;e.addEventListener(\"beforeload\",function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute(\"nomodule\")||!n)return;e.preventDefault()},!0),t.type=\"module\",t.src=\".\",e.head.appendChild(t),t.remove()}}();';\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes('?') ? '&' : '?'}amp=1`;\n} //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3BhZ2VzL19kb2N1bWVudC5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSw4Q0FBNkMsQ0FBQztJQUMxQ0csS0FBSyxFQUFFLElBQUk7QUFDZixDQUFDLEVBQUMsQ0FBQztBQUNIRCxZQUFZLEdBQUdFLElBQUksQ0FBQztBQUNwQkYsWUFBWSxHQUFHRyxJQUFJLENBQUM7QUFDcEJILGtCQUFlLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQztBQUN6QixHQUFHLENBQUNLLE1BQU0sR0FBR0MsdUJBQXVCLENBQUNDLG1CQUFPLENBQUMsb0JBQU87QUFDcEQsR0FBRyxDQUFDQyxVQUFVLEdBQUdELG1CQUFPLENBQUMsd0RBQXlCO0FBQ2xELEdBQUcsQ0FBQ0UsYUFBYSxHQUFHRixtQkFBTyxDQUFDLDBEQUEwQjtBQUN0RCxHQUFHLENBQUNHLE1BQU0sR0FBR0gsbUJBQU8sQ0FBQyx3Q0FBaUI7QUFDdEMsR0FBRyxDQUFDSSxXQUFXLEdBQUdKLG1CQUFPLENBQUMsa0RBQXNCO0FBQ2hELEdBQUcsQ0FBQ0ssT0FBTyxHQUFHQyxzQkFBc0IsQ0FBQ04sbUJBQU8sQ0FBQyxtRUFBa0I7QUFDL0QsR0FBRyxDQUFDTyxRQUFRLEdBQUdELHNCQUFzQixDQUFDTixtQkFBTyxDQUFDLGlFQUFpQjtBQUMvRCxHQUFHLENBQUNRLFlBQVksR0FBR1IsbUJBQU8sQ0FBQyw4REFBNEI7U0FDOUNNLHNCQUFzQixDQUFDRyxHQUFHLEVBQUUsQ0FBQztJQUNsQyxNQUFNLENBQUNBLEdBQUcsSUFBSUEsR0FBRyxDQUFDQyxVQUFVLEdBQUdELEdBQUcsR0FBRyxDQUFDO1FBQ2xDWixPQUFPLEVBQUVZLEdBQUc7SUFDaEIsQ0FBQztBQUNMLENBQUM7U0FDUVYsdUJBQXVCLENBQUNVLEdBQUcsRUFBRSxDQUFDO0lBQ25DLEVBQUUsRUFBRUEsR0FBRyxJQUFJQSxHQUFHLENBQUNDLFVBQVUsRUFBRSxDQUFDO1FBQ3hCLE1BQU0sQ0FBQ0QsR0FBRztJQUNkLENBQUMsTUFBTSxDQUFDO1FBQ0osR0FBRyxDQUFDRSxNQUFNLEdBQUcsQ0FBQyxDQUFDO1FBQ2YsRUFBRSxFQUFFRixHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7WUFDZCxHQUFHLENBQUMsR0FBRyxDQUFDRyxHQUFHLElBQUlILEdBQUcsQ0FBQyxDQUFDO2dCQUNoQixFQUFFLEVBQUVsQixNQUFNLENBQUNzQixTQUFTLENBQUNDLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDTixHQUFHLEVBQUVHLEdBQUcsR0FBRyxDQUFDO29CQUNqRCxHQUFHLENBQUNJLElBQUksR0FBR3pCLE1BQU0sQ0FBQ0MsY0FBYyxJQUFJRCxNQUFNLENBQUMwQix3QkFBd0IsR0FBRzFCLE1BQU0sQ0FBQzBCLHdCQUF3QixDQUFDUixHQUFHLEVBQUVHLEdBQUcsSUFBSSxDQUFDLENBQUM7b0JBQ3BILEVBQUUsRUFBRUksSUFBSSxDQUFDRSxHQUFHLElBQUlGLElBQUksQ0FBQ0csR0FBRyxFQUFFLENBQUM7d0JBQ3ZCNUIsTUFBTSxDQUFDQyxjQUFjLENBQUNtQixNQUFNLEVBQUVDLEdBQUcsRUFBRUksSUFBSSxDQUFDLENBQUM7b0JBQzdDLENBQUMsTUFBTSxDQUFDO3dCQUNKTCxNQUFNLENBQUNDLEdBQUcsSUFBSUgsR0FBRyxDQUFDRyxHQUFHLENBQUMsQ0FBQztvQkFDM0IsQ0FBQztnQkFDTCxDQUFDO1lBQ0wsQ0FBQztRQUNMLENBQUM7UUFDREQsTUFBTSxDQUFDZCxPQUFPLEdBQUdZLEdBQUcsQ0FBQztRQUNyQixNQUFNLENBQUNFLE1BQU07SUFDakIsQ0FBQztBQUNMLENBQUM7U0FDUVMsZ0JBQWdCLENBQUNDLGFBQWEsRUFBRUMsUUFBUSxFQUFFQyxTQUFTLEVBQUUsQ0FBQztJQUMzRCxLQUFLLENBQUNDLFdBQVcsSUFBSSxDQUFDLEVBQUV0QixhQUFhLEVBQUV1QixZQUFZLENBQUNKLGFBQWEsRUFBRSxDQUFPO0lBQzFFLEtBQUssQ0FBQ0ssU0FBUyxHQUFHSCxTQUFTLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFckIsYUFBYSxFQUFFdUIsWUFBWSxDQUFDSixhQUFhLEVBQUVDLFFBQVE7SUFDMUYsTUFBTSxDQUFDLENBQUM7UUFDSkUsV0FBVztRQUNYRSxTQUFTO1FBQ1RDLFFBQVEsRUFBRSxDQUFDO2VBQ0osR0FBRyxDQUFDQyxHQUFHLENBQUMsQ0FBQzttQkFDTEosV0FBVzttQkFDWEUsU0FBUztZQUNoQixDQUFDO1FBQ0wsQ0FBQztJQUNMLENBQUM7QUFDTCxDQUFDO1NBQ1FHLGtCQUFrQixDQUFDQyxPQUFPLEVBQUVDLEtBQUssRUFBRSxDQUFDO0lBQ3pDLEVBQTREO0lBQzVELEVBQTZDO0lBQzdDLEtBQUssQ0FBQyxDQUFDLENBQUNDLFdBQVcsR0FBR1gsYUFBYSxHQUFHWSw2QkFBNkIsR0FBR0MsdUJBQXVCLEdBQUdDLFdBQVcsSUFBSSxDQUFDLEdBQUdMLE9BQU87SUFDMUgsTUFBTSxDQUFDVCxhQUFhLENBQUNlLGFBQWEsQ0FBQ0MsTUFBTSxFQUFFQyxRQUFRLEdBQUdBLFFBQVEsQ0FBQ0MsUUFBUSxDQUFDLENBQUssVUFBTUQsUUFBUSxDQUFDQyxRQUFRLENBQUMsQ0FBWTtNQUMvR0MsR0FBRyxFQUFFRixRQUFRLEdBQUcsRUFBYSxZQUFDeEMsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBUSxTQUFFLENBQUM7WUFDaEU3QixHQUFHLEVBQUUwQixRQUFRO1lBQ2JJLEtBQUssR0FBR1IsdUJBQXVCO1lBQy9CUyxLQUFLLEVBQUVaLEtBQUssQ0FBQ1ksS0FBSztZQUNsQlIsV0FBVyxFQUFFSixLQUFLLENBQUNJLFdBQVcsSUFBSUEsV0FBVztZQUM3Q1MsUUFBUSxFQUFFLElBQUk7WUFDZEMsR0FBRyxLQUFLYixXQUFXLENBQUMsT0FBTyxFQUFFTSxRQUFRLEdBQUdMLDZCQUE2QjtRQUN6RSxDQUFDOztBQUVULENBQUM7U0FDUWEsaUJBQWlCLENBQUNDLEtBQUssRUFBRSxDQUFDO0lBQy9CLE1BQU0sR0FBR0EsS0FBSyxNQUFNQSxLQUFLLENBQUNoQixLQUFLO0FBQ25DLENBQUM7U0FDUWlCLHVCQUF1QixDQUFDbEIsT0FBTyxFQUFFQyxLQUFLLEVBQUUsQ0FBQztJQUM5QyxLQUFLLENBQUMsQ0FBQyxDQUFDQyxXQUFXLEdBQUdpQixZQUFZLEdBQUdkLFdBQVcsR0FBR2UsaUJBQWlCLEVBQUUsQ0FBQyxHQUFHcEIsT0FBTztJQUNqRixFQUE4QztJQUM5QyxFQUFFLEdBQUdvQixpQkFBaUIsSUFBSUMsS0FBZSxFQUFFLE1BQU0sQ0FBQyxJQUFJO0lBQ3RELEdBQUcsQ0FBQyxDQUFDO1FBQ0QsR0FBRyxDQUFDLENBQUMsQ0FBQ0UsZ0JBQWdCLElBQUksQ0FBQyxHQUFHckQsT0FBTyxDQUFDLEVBQXlCLHdCQUFDLENBQW1DO1FBQ25HLEtBQUssQ0FBQ3NELFFBQVEsR0FBR0MsS0FBSyxDQUFDQyxPQUFPLENBQUN6QixLQUFLLENBQUN1QixRQUFRLElBQUl2QixLQUFLLENBQUN1QixRQUFRLEdBQUcsQ0FBQztZQUMvRHZCLEtBQUssQ0FBQ3VCLFFBQVE7UUFDbEIsQ0FBQztRQUNELEVBQXlFO1FBQ3pFLEtBQUssQ0FBQ0csaUJBQWlCLEdBQUdILFFBQVEsQ0FBQ0ksSUFBSSxFQUFFWCxLQUFLLEdBQUcsQ0FBQztZQUM5QyxHQUFHLENBQUNZLEdBQUcsRUFBRUMsSUFBSTtZQUNiLE1BQU0sQ0FBQ2QsaUJBQWlCLENBQUNDLEtBQUssTUFBTUEsS0FBSyxLQUFLLElBQUksSUFBSUEsS0FBSyxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsSUFBSVksR0FBRyxHQUFHWixLQUFLLENBQUNoQixLQUFLLE1BQU0sSUFBSSxJQUFJNEIsR0FBRyxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsSUFBSUMsSUFBSSxHQUFHRCxHQUFHLENBQUNFLHVCQUF1QixNQUFNLElBQUksSUFBSUQsSUFBSSxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsR0FBR0EsSUFBSSxDQUFDRSxNQUFNLENBQUNDLE1BQU0sS0FBSyxDQUF1QiwwQkFBSWhCLEtBQUssQ0FBQ2hCLEtBQUs7UUFDelIsQ0FBQztRQUNELE1BQU0sQ0FBQyxFQUFhLFlBQUNqQyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQzNDLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDbUUsUUFBUSxFQUFFLElBQUksR0FBR1AsaUJBQWlCLElBQUksRUFBYSxZQUFDM0QsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBUSxTQUFFLENBQUM7WUFDekosQ0FBdUIsd0JBQUUsQ0FBRTtZQUMzQm9CLHVCQUF1QixFQUFFLENBQUM7Z0JBQ3RCQyxNQUFNLEdBQUc7O29CQUVMLEVBQUU5QixXQUFXLENBQUM7O1VBRXhCO1lBQ0UsQ0FBQztRQUNMLENBQUMsR0FBRyxFQUFhLFlBQUNsQyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFRLFNBQUUsQ0FBQztZQUN0RCxDQUFnQixpQkFBRSxDQUFFO1lBQ3BCb0IsdUJBQXVCLEVBQUUsQ0FBQztnQkFDdEJDLE1BQU0sRUFBRVQsZ0JBQWdCO1lBQzVCLENBQUM7UUFDTCxDQUFDLElBQUlKLFlBQVksQ0FBQ2dCLE1BQU0sSUFBSSxDQUFDLENBQUMsRUFBRXpCLEdBQUcsRUFBRTBCLElBQUksRUFBRUMsS0FBSyxHQUFHLENBQUM7WUFDaEQsS0FBSyxDQUFDLENBQUMsQ0FBQ0MsUUFBUSxNQUFNQyxXQUFXLENBQUMsQ0FBQyxHQUFHSCxJQUFJO1lBQzFDLE1BQU0sQ0FBQyxFQUFhLFlBQUNwRSxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFRLFNBQUVsRCxNQUFNLENBQUMrRSxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUVELFdBQVcsRUFBRSxDQUFDO2dCQUN4RkUsSUFBSSxFQUFFLENBQWdCO2dCQUN0QjNELEdBQUcsRUFBRXlELFdBQVcsQ0FBQ3hCLEdBQUcsSUFBSXNCLEtBQUs7Z0JBQzdCeEIsS0FBSyxFQUFFWixLQUFLLENBQUNZLEtBQUs7Z0JBQ2xCLENBQWMsZUFBRSxDQUFRO2dCQUN4QlIsV0FBVyxFQUFFSixLQUFLLENBQUNJLFdBQVcsSUFBSUEsV0FBVztZQUNqRCxDQUFDO1FBQ0wsQ0FBQztJQUNMLENBQUMsQ0FBQyxLQUFLLEVBQUVxQyxHQUFHLEVBQUUsQ0FBQztRQUNYQyxPQUFPLENBQUNDLElBQUksRUFBRSxrRkFBa0YsRUFBRUYsR0FBRyxHQUFHLENBQUM7UUFDekcsTUFBTSxDQUFDLElBQUk7SUFDZixDQUFDO0FBQ0wsQ0FBQztTQUNRRyxpQkFBaUIsQ0FBQzdDLE9BQU8sRUFBRUMsS0FBSyxFQUFFLENBQUM7SUFDeEMsS0FBSyxDQUFDLENBQUMsQ0FBQ2tCLFlBQVksR0FBR2YsdUJBQXVCLEdBQUdDLFdBQVcsRUFBRSxDQUFDLEdBQUdMLE9BQU87SUFDekUsS0FBSyxDQUFDOEMsZ0JBQWdCLEdBQUc1Qix1QkFBdUIsQ0FBQ2xCLE9BQU8sRUFBRUMsS0FBSztJQUMvRCxLQUFLLENBQUM4Qyx3QkFBd0IsSUFBSTVCLFlBQVksQ0FBQzZCLGlCQUFpQixJQUFJLENBQUMsQ0FBQyxFQUFFdEMsR0FBRyxFQUFFMEIsSUFBSSxFQUFFQyxLQUFLLEdBQUcsQ0FBQztRQUN4RixLQUFLLENBQUMsQ0FBQyxDQUFDQyxRQUFRLE1BQU1DLFdBQVcsQ0FBQyxDQUFDLEdBQUdILElBQUk7UUFDMUMsR0FBRyxDQUFDYSxNQUFNO1FBQ1YsTUFBTSxDQUFDLEVBQWEsWUFBQ2pGLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDLENBQVEsU0FBRWxELE1BQU0sQ0FBQytFLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRUQsV0FBVyxFQUFFLENBQUM7WUFDeEZ6RCxHQUFHLEVBQUV5RCxXQUFXLENBQUN4QixHQUFHLElBQUlzQixLQUFLO1lBQzdCekIsS0FBSyxHQUFHcUMsTUFBTSxHQUFHVixXQUFXLENBQUMzQixLQUFLLE1BQU0sSUFBSSxJQUFJcUMsTUFBTSxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUdBLE1BQU0sSUFBSTdDLHVCQUF1QjtZQUNyR1MsS0FBSyxFQUFFWixLQUFLLENBQUNZLEtBQUs7WUFDbEIsQ0FBYyxlQUFFLENBQW1CO1lBQ25DUixXQUFXLEVBQUVKLEtBQUssQ0FBQ0ksV0FBVyxJQUFJQSxXQUFXO1FBQ2pELENBQUM7SUFDTCxDQUFDO0lBQ0QsTUFBTSxDQUFDLEVBQWEsWUFBQ3JDLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDM0MsTUFBTSxDQUFDRCxPQUFPLENBQUNtRSxRQUFRLEVBQUUsSUFBSSxFQUFFWSxnQkFBZ0IsRUFBRUMsd0JBQXdCO0FBQy9ILENBQUM7U0FDUUcsZ0JBQWdCLENBQUNsRCxPQUFPLEVBQUVDLEtBQUssRUFBRWtELEtBQUssRUFBRSxDQUFDO0lBQzlDLEtBQUssQ0FBQyxDQUFDLENBQUNDLGNBQWMsR0FBR2xELFdBQVcsR0FBR21ELGFBQWEsR0FBR2xELDZCQUE2QixHQUFHQyx1QkFBdUIsR0FBR0MsV0FBVyxJQUFJLENBQUMsR0FBR0wsT0FBTztJQUMzSSxNQUFNLENBQUNvRCxjQUFjLENBQUMxQyxHQUFHLEVBQUUwQixJQUFJLEdBQUcsQ0FBQztRQUMvQixFQUFFLEdBQUdBLElBQUksQ0FBQzNCLFFBQVEsQ0FBQyxDQUFLLFNBQUswQyxLQUFLLENBQUN0RCxRQUFRLENBQUN5RCxRQUFRLENBQUNsQixJQUFJLEdBQUcsTUFBTSxDQUFDLElBQUk7UUFDdkUsTUFBTSxDQUFDLEVBQWEsWUFBQ3BFLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDLENBQVEsU0FBRSxDQUFDO1lBQ3pENEMsS0FBSyxHQUFHRixhQUFhLElBQUlqRCx1QkFBdUI7WUFDaERRLEtBQUssR0FBR1IsdUJBQXVCO1lBQy9CdEIsR0FBRyxFQUFFc0QsSUFBSTtZQUNUckIsR0FBRyxLQUFLYixXQUFXLENBQUMsT0FBTyxFQUFFc0QsU0FBUyxDQUFDcEIsSUFBSSxJQUFJakMsNkJBQTZCO1lBQzVFVSxLQUFLLEVBQUVaLEtBQUssQ0FBQ1ksS0FBSztZQUNsQlIsV0FBVyxFQUFFSixLQUFLLENBQUNJLFdBQVcsSUFBSUEsV0FBVztRQUNqRCxDQUFDO0lBQ0wsQ0FBQztBQUNMLENBQUM7U0FDUW9ELFVBQVUsQ0FBQ3pELE9BQU8sRUFBRUMsS0FBSyxFQUFFa0QsS0FBSyxFQUFFLENBQUM7SUFDeEMsR0FBRyxDQUFDdEIsR0FBRztJQUNQLEtBQUssQ0FBQyxDQUFDLENBQUMzQixXQUFXLEdBQUdYLGFBQWEsR0FBRzhELGFBQWEsR0FBR2xELDZCQUE2QixHQUFHQyx1QkFBdUIsR0FBR0MsV0FBVyxJQUFJLENBQUMsR0FBR0wsT0FBTztJQUMxSSxLQUFLLENBQUMwRCxhQUFhLEdBQUdQLEtBQUssQ0FBQ3RELFFBQVEsQ0FBQ1UsTUFBTSxFQUFFNkIsSUFBSSxHQUFHQSxJQUFJLENBQUMzQixRQUFRLENBQUMsQ0FBSzs7SUFFdkUsS0FBSyxDQUFDa0Qsa0JBQWtCLElBQUk5QixHQUFHLEdBQUd0QyxhQUFhLENBQUNxRSxnQkFBZ0IsTUFBTSxJQUFJLElBQUkvQixHQUFHLEtBQUssSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsQ0FBQyxHQUFHQSxHQUFHLENBQUN0QixNQUFNLEVBQUU2QixJQUFJLEdBQUdBLElBQUksQ0FBQzNCLFFBQVEsQ0FBQyxDQUFLOztJQUU5SSxNQUFNLENBQUMsQ0FBQztXQUNEaUQsYUFBYTtXQUNiQyxrQkFBa0I7SUFDekIsQ0FBQyxDQUFDakQsR0FBRyxFQUFFMEIsSUFBSSxHQUFHLENBQUM7UUFDWCxNQUFNLENBQUMsRUFBYSxZQUFDcEUsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBUSxTQUFFLENBQUM7WUFDekQ3QixHQUFHLEVBQUVzRCxJQUFJO1lBQ1RyQixHQUFHLEtBQUtiLFdBQVcsQ0FBQyxPQUFPLEVBQUVzRCxTQUFTLENBQUNwQixJQUFJLElBQUlqQyw2QkFBNkI7WUFDNUVVLEtBQUssRUFBRVosS0FBSyxDQUFDWSxLQUFLO1lBQ2xCMEMsS0FBSyxHQUFHRixhQUFhLElBQUlqRCx1QkFBdUI7WUFDaERRLEtBQUssR0FBR1IsdUJBQXVCO1lBQy9CQyxXQUFXLEVBQUVKLEtBQUssQ0FBQ0ksV0FBVyxJQUFJQSxXQUFXO1FBQ2pELENBQUM7SUFDTCxDQUFDO0FBQ0wsQ0FBQztNQUNLd0QsUUFBUSxTQUFTN0YsTUFBTSxDQUFDOEYsU0FBUztJQUNuQyxFQUdDOzs7R0FBQSxVQUFRQyxlQUFlLENBQUNDLEdBQUcsRUFBRSxDQUFDO1FBQzNCLE1BQU0sQ0FBQ0EsR0FBRyxDQUFDQyxzQkFBc0IsQ0FBQ0QsR0FBRztJQUN6QyxDQUFDO0lBQ0RFLE1BQU0sR0FBRyxDQUFDO1FBQ04sTUFBTSxDQUFDLEVBQWEsWUFBQ2xHLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDOUMsSUFBSSxFQUFFLElBQUksRUFBRSxFQUFhLFlBQUNHLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDd0QsSUFBSSxFQUFFLElBQUksR0FBRyxFQUFhLFlBQUNuRyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFNLE9BQUUsSUFBSSxFQUFFLEVBQWEsWUFBQzNDLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDN0MsSUFBSSxFQUFFLElBQUksR0FBRyxFQUFhLFlBQUNFLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDeUQsVUFBVSxFQUFFLElBQUk7SUFDdFMsQ0FBQzs7QUFFTHpHLGtCQUFlLEdBQUdrRyxRQUFRLENBQUM7QUFDM0JBLFFBQVEsQ0FBQ1Esd0JBQXdCLEdBQUcsUUFBUSxDQUFDQyx3QkFBd0IsR0FBRyxDQUFDO0lBQ3JFLE1BQU0sQ0FBQyxFQUFhLFlBQUN0RyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQzlDLElBQUksRUFBRSxJQUFJLEVBQUUsRUFBYSxZQUFDRyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQ3dELElBQUksRUFBRSxJQUFJLEdBQUcsRUFBYSxZQUFDbkcsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTSxPQUFFLElBQUksRUFBRSxFQUFhLFlBQUMzQyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQzdDLElBQUksRUFBRSxJQUFJLEdBQUcsRUFBYSxZQUFDRSxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQ3lELFVBQVUsRUFBRSxJQUFJO0FBQ3RTLENBQUMsQ0FBQztTQUNPdkcsSUFBSSxDQUFDb0MsS0FBSyxFQUFFLENBQUM7SUFDbEIsS0FBSyxDQUFDLENBQUMsQ0FBQ1IsU0FBUyxHQUFHOEUscUJBQXFCLEdBQUdDLE1BQU0sRUFBRSxDQUFDLElBQUksQ0FBQyxFQUFFeEcsTUFBTSxFQUFFeUcsVUFBVSxDQUFDL0YsWUFBWSxDQUFDZ0csV0FBVztJQUN2R0gscUJBQXFCLENBQUMxRyxJQUFJLEdBQUcsSUFBSSxDQUFDO0lBQ2xDLE1BQU0sQ0FBQyxFQUFhLFlBQUNHLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDLENBQU0sT0FBRWxELE1BQU0sQ0FBQytFLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRXZDLEtBQUssRUFBRSxDQUFDO1FBQ2hGMEUsSUFBSSxFQUFFMUUsS0FBSyxDQUFDMEUsSUFBSSxJQUFJSCxNQUFNLElBQUlJLFNBQVM7UUFDdkNDLEdBQUcsRUFBRXBGLFNBQVMsR0FBRyxDQUFFLElBQUdtRixTQUFTO1FBQy9CLENBQWlCLGtCQUFFbkYsU0FBUyxJQTVMcEMsQ0FBYSxpQkE0TG9ELENBQVksY0FBRyxDQUFFLElBQUdtRixTQUFTO0lBQzFGLENBQUM7QUFDTCxDQUFDO1NBQ1FFLFNBQVMsQ0FBQyxDQUFDLENBQUNDLE1BQU0sRUFBRSxDQUFDLEVBQUUsQ0FBQztJQUM3QixFQUFFLEdBQUdBLE1BQU0sRUFBRSxNQUFNLENBQUMsSUFBSTtJQUN4QixFQUF5RDtJQUN6RCxLQUFLLENBQUNDLFNBQVMsR0FBR3ZELEtBQUssQ0FBQ0MsT0FBTyxDQUFDcUQsTUFBTSxJQUFJQSxNQUFNLEdBQUcsQ0FBQyxDQUFDO0lBQ3JELEVBQUUsRUFDRkEsTUFBTSxDQUFDOUUsS0FBSyxJQUNad0IsS0FBSyxDQUFDQyxPQUFPLENBQUNxRCxNQUFNLENBQUM5RSxLQUFLLENBQUN1QixRQUFRLEdBQUcsQ0FBQztRQUNuQyxLQUFLLENBQUN5RCxTQUFTLElBQUlDLEVBQUUsR0FBRyxDQUFDO1lBQ3JCLEdBQUcsQ0FBQ3JELEdBQUcsRUFBRXNELElBQUk7WUFDYixNQUFNLENBQUNELEVBQUUsS0FBSyxJQUFJLElBQUlBLEVBQUUsS0FBSyxJQUFJLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFDLElBQUlyRCxHQUFHLEdBQUdxRCxFQUFFLENBQUNqRixLQUFLLE1BQU0sSUFBSSxJQUFJNEIsR0FBRyxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsSUFBSXNELElBQUksR0FBR3RELEdBQUcsQ0FBQ0UsdUJBQXVCLE1BQU0sSUFBSSxJQUFJb0QsSUFBSSxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsR0FBR0EsSUFBSSxDQUFDbkQsTUFBTTtRQUNqTSxDQUFDO1FBQ0QsRUFBa0U7UUFDbEUrQyxNQUFNLENBQUM5RSxLQUFLLENBQUN1QixRQUFRLENBQUM0RCxPQUFPLEVBQUVuRSxLQUFLLEdBQUcsQ0FBQztZQUNwQyxFQUFFLEVBQUVRLEtBQUssQ0FBQ0MsT0FBTyxDQUFDVCxLQUFLLEdBQUcsQ0FBQztnQkFDdkJBLEtBQUssQ0FBQ21FLE9BQU8sRUFBRUYsRUFBRSxHQUFHRCxTQUFTLENBQUNDLEVBQUUsS0FBS0YsU0FBUyxDQUFDSyxJQUFJLENBQUNILEVBQUU7aUJBQ3JELENBQUM7WUFDTixDQUFDLE1BQU0sRUFBRSxFQUFFRCxTQUFTLENBQUNoRSxLQUFLLEdBQUcsQ0FBQztnQkFDMUIrRCxTQUFTLENBQUNLLElBQUksQ0FBQ3BFLEtBQUssQ0FBQyxDQUFDO1lBQzFCLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNQLENBQUM7SUFDRCxFQUF5RSx3RUFBQyxNQUFNLENBQUMsRUFBYSxZQUFDakQsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTyxRQUFFLENBQUM7UUFDbEksQ0FBWSxhQUFFLENBQUU7UUFDaEJvQix1QkFBdUIsRUFBRSxDQUFDO1lBQ3RCQyxNQUFNLEVBQUVnRCxTQUFTLENBQUN0RSxHQUFHLEVBQUU0RSxLQUFLLEdBQUdBLEtBQUssQ0FBQ3JGLEtBQUssQ0FBQzhCLHVCQUF1QixDQUFDQyxNQUFNO2NBQ3ZFdUQsSUFBSSxDQUFDLENBQUUsR0FBRUMsT0FBTyxtQ0FBbUMsQ0FBRSxHQUFFQSxPQUFPLDZCQUE2QixDQUFFO1FBQ25HLENBQUM7SUFDTCxDQUFDO0FBQ0wsQ0FBQztNQUNLckIsSUFBSSxTQUFTbkcsTUFBTSxDQUFDOEYsU0FBUztJQUMvQjJCLFdBQVcsQ0FBQ3RDLEtBQUssRUFBRSxDQUFDO1FBQ2hCLEtBQUssQ0FBQyxDQUFDLENBQUNqRCxXQUFXLEdBQUdDLDZCQUE2QixHQUFHaUQsY0FBYyxHQUFHL0MsV0FBVyxHQUFHcUYsV0FBVyxHQUFHQyxhQUFhLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQzNGLE9BQU87UUFDcEksS0FBSyxDQUFDNEYsUUFBUSxHQUFHekMsS0FBSyxDQUFDdEQsUUFBUSxDQUFDVSxNQUFNLEVBQUVzRixDQUFDLEdBQUdBLENBQUMsQ0FBQ3BGLFFBQVEsQ0FBQyxDQUFNOztRQUU3RCxLQUFLLENBQUNmLFdBQVcsR0FBRyxHQUFHLENBQUNJLEdBQUcsQ0FBQ3FELEtBQUssQ0FBQ3pELFdBQVc7UUFDN0MsRUFBcUU7UUFDckUsRUFBK0M7UUFDL0MsR0FBRyxDQUFDb0csYUFBYSxHQUFHLEdBQUcsQ0FBQ2hHLEdBQUcsQ0FBQyxDQUFDLENBQUM7UUFDOUIsR0FBRyxDQUFDaUcsZUFBZSxHQUFHdEUsS0FBSyxDQUFDdUUsSUFBSSxDQUFDLEdBQUcsQ0FBQ2xHLEdBQUcsQ0FBQ3NELGNBQWMsQ0FBQzdDLE1BQU0sRUFBRTZCLElBQUksR0FBR0EsSUFBSSxDQUFDM0IsUUFBUSxDQUFDLENBQU07O1FBRTNGLEVBQUUsRUFBRXNGLGVBQWUsQ0FBQzlELE1BQU0sRUFBRSxDQUFDO1lBQ3pCLEtBQUssQ0FBQ2dFLFFBQVEsR0FBRyxHQUFHLENBQUNuRyxHQUFHLENBQUM4RixRQUFRO1lBQ2pDRyxlQUFlLEdBQUdBLGVBQWUsQ0FBQ3hGLE1BQU0sRUFBRXNGLENBQUMsS0FBS0ksUUFBUSxDQUFDQyxHQUFHLENBQUNMLENBQUMsS0FBS25HLFdBQVcsQ0FBQ3dHLEdBQUcsQ0FBQ0wsQ0FBQzthQUNuRixDQUFDO1lBQ0ZDLGFBQWEsR0FBRyxHQUFHLENBQUNoRyxHQUFHLENBQUNpRyxlQUFlLENBQUMsQ0FBQztZQUN6Q0gsUUFBUSxDQUFDUCxJQUFJLElBQUlVLGVBQWUsQ0FBQyxDQUFDO1FBQ3RDLENBQUM7UUFDRCxHQUFHLENBQUNJLGVBQWUsR0FBRyxDQUFDLENBQUM7UUFDeEJQLFFBQVEsQ0FBQ1IsT0FBTyxFQUFFaEQsSUFBSSxHQUFHLENBQUM7WUFDdEIsS0FBSyxDQUFDZ0UsWUFBWSxHQUFHMUcsV0FBVyxDQUFDd0csR0FBRyxDQUFDOUQsSUFBSTtZQUN6QyxFQUFFLEdBQUdzRCxXQUFXLEVBQUUsQ0FBQztnQkFDZlMsZUFBZSxDQUFDZCxJQUFJLENBQUMsRUFBYSxZQUFDckgsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTSxPQUFFLENBQUM7b0JBQ3JFN0IsR0FBRyxLQUFLc0QsSUFBSSxDQUFDLFFBQVE7b0JBQ3JCdkIsS0FBSyxFQUFFLElBQUksQ0FBQ1osS0FBSyxDQUFDWSxLQUFLO29CQUN2QndGLEdBQUcsRUFBRSxDQUFTO29CQUNkQyxJQUFJLEtBQUtwRyxXQUFXLENBQUMsT0FBTyxFQUFFc0QsU0FBUyxDQUFDcEIsSUFBSSxJQUFJakMsNkJBQTZCO29CQUM3RW9HLEVBQUUsRUFBRSxDQUFPO29CQUNYbEcsV0FBVyxFQUFFLElBQUksQ0FBQ0osS0FBSyxDQUFDSSxXQUFXLElBQUlBLFdBQVc7Z0JBQ3RELENBQUMsRUFBRSxDQUFDO1lBQ1IsQ0FBQztZQUNELEtBQUssQ0FBQ21HLGVBQWUsR0FBR1YsYUFBYSxDQUFDSSxHQUFHLENBQUM5RCxJQUFJO1lBQzlDK0QsZUFBZSxDQUFDZCxJQUFJLENBQUMsRUFBYSxZQUFDckgsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTSxPQUFFLENBQUM7Z0JBQ3JFN0IsR0FBRyxFQUFFc0QsSUFBSTtnQkFDVHZCLEtBQUssRUFBRSxJQUFJLENBQUNaLEtBQUssQ0FBQ1ksS0FBSztnQkFDdkJ3RixHQUFHLEVBQUUsQ0FBWTtnQkFDakJDLElBQUksS0FBS3BHLFdBQVcsQ0FBQyxPQUFPLEVBQUVzRCxTQUFTLENBQUNwQixJQUFJLElBQUlqQyw2QkFBNkI7Z0JBQzdFRSxXQUFXLEVBQUUsSUFBSSxDQUFDSixLQUFLLENBQUNJLFdBQVcsSUFBSUEsV0FBVztnQkFDbEQsQ0FBVSxXQUFFbUcsZUFBZSxHQUFHNUIsU0FBUyxHQUFHd0IsWUFBWSxHQUFHLENBQUUsSUFBR3hCLFNBQVM7Z0JBQ3ZFLENBQVUsV0FBRTRCLGVBQWUsR0FBRzVCLFNBQVMsR0FBR3dCLFlBQVksR0FBR3hCLFNBQVMsR0FBRyxDQUFFO1lBQzNFLENBQUMsRUFBRSxDQUFDO1FBQ1IsQ0FBQyxDQUFDLENBQUM7UUFDSCxFQUFFLEVBdFFWLEtBc1FtRSxFQUFFLEVBRTVEO1FBQ0QsTUFBTSxDQUFDdUIsZUFBZSxDQUFDbEUsTUFBTSxLQUFLLENBQUMsR0FBRyxJQUFJLEdBQUdrRSxlQUFlO0lBQ2hFLENBQUM7SUFDRE8sdUJBQXVCLEdBQUcsQ0FBQztRQUN2QixLQUFLLENBQUMsQ0FBQyxDQUFDdEQsY0FBYyxHQUFHbEQsV0FBVyxHQUFHQyw2QkFBNkIsR0FBR0UsV0FBVyxJQUFJLENBQUMsR0FBRyxJQUFJLENBQUNMLE9BQU87UUFDdEcsTUFBTSxDQUFDb0QsY0FBYyxDQUFDMUMsR0FBRyxFQUFFMEIsSUFBSSxHQUFHLENBQUM7WUFDL0IsRUFBRSxHQUFHQSxJQUFJLENBQUMzQixRQUFRLENBQUMsQ0FBSyxPQUFHLENBQUM7Z0JBQ3hCLE1BQU0sQ0FBQyxJQUFJO1lBQ2YsQ0FBQztZQUNELE1BQU0sQ0FBQyxFQUFhLFlBQUN6QyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFNLE9BQUUsQ0FBQztnQkFDdkQwRixHQUFHLEVBQUUsQ0FBUztnQkFDZHZILEdBQUcsRUFBRXNELElBQUk7Z0JBQ1RrRSxJQUFJLEtBQUtwRyxXQUFXLENBQUMsT0FBTyxFQUFFc0QsU0FBUyxDQUFDcEIsSUFBSSxJQUFJakMsNkJBQTZCO2dCQUM3RW9HLEVBQUUsRUFBRSxDQUFRO2dCQUNaMUYsS0FBSyxFQUFFLElBQUksQ0FBQ1osS0FBSyxDQUFDWSxLQUFLO2dCQUN2QlIsV0FBVyxFQUFFLElBQUksQ0FBQ0osS0FBSyxDQUFDSSxXQUFXLElBQUlBLFdBQVc7WUFDdEQsQ0FBQztRQUNMLENBQUMsRUFBQyxFQUE0QjtTQUM3QkUsTUFBTSxDQUFDb0csT0FBTztJQUNuQixDQUFDO0lBQ0RDLG1CQUFtQixDQUFDekQsS0FBSyxFQUFFLENBQUM7UUFDeEIsS0FBSyxDQUFDLENBQUMsQ0FBQ2pELFdBQVcsR0FBR0MsNkJBQTZCLEdBQUdnQixZQUFZLEdBQUdkLFdBQVcsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDTCxPQUFPO1FBQ3BHLEtBQUssQ0FBQzZHLFlBQVksR0FBRzFELEtBQUssQ0FBQ3RELFFBQVEsQ0FBQ1UsTUFBTSxFQUFFNkIsSUFBSSxHQUFHLENBQUM7WUFDaEQsTUFBTSxDQUFDQSxJQUFJLENBQUMzQixRQUFRLENBQUMsQ0FBSztRQUM5QixDQUFDO1FBQ0QsTUFBTSxDQUFDLENBQUM7Z0JBQ0FVLFlBQVksQ0FBQzZCLGlCQUFpQixJQUFJLENBQUMsQ0FBQyxFQUFFdEMsR0FBRyxFQUFFMEIsSUFBSSxHQUFHLEVBQWEsWUFBQ3BFLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDLENBQU0sT0FBRSxDQUFDO29CQUNsRzdCLEdBQUcsRUFBRXNELElBQUksQ0FBQ3JCLEdBQUc7b0JBQ2JGLEtBQUssRUFBRSxJQUFJLENBQUNaLEtBQUssQ0FBQ1ksS0FBSztvQkFDdkJ3RixHQUFHLEVBQUUsQ0FBUztvQkFDZEMsSUFBSSxFQUFFbEUsSUFBSSxDQUFDckIsR0FBRztvQkFDZHdGLEVBQUUsRUFBRSxDQUFRO29CQUNabEcsV0FBVyxFQUFFLElBQUksQ0FBQ0osS0FBSyxDQUFDSSxXQUFXLElBQUlBLFdBQVc7Z0JBQ3RELENBQUM7O2VBRUZ3RyxZQUFZLENBQUNuRyxHQUFHLEVBQUUwQixJQUFJLEdBQUcsRUFBYSxZQUFDcEUsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTSxPQUFFLENBQUM7b0JBQ3hFN0IsR0FBRyxFQUFFc0QsSUFBSTtvQkFDVHZCLEtBQUssRUFBRSxJQUFJLENBQUNaLEtBQUssQ0FBQ1ksS0FBSztvQkFDdkJ3RixHQUFHLEVBQUUsQ0FBUztvQkFDZEMsSUFBSSxLQUFLcEcsV0FBVyxDQUFDLE9BQU8sRUFBRXNELFNBQVMsQ0FBQ3BCLElBQUksSUFBSWpDLDZCQUE2QjtvQkFDN0VvRyxFQUFFLEVBQUUsQ0FBUTtvQkFDWmxHLFdBQVcsRUFBRSxJQUFJLENBQUNKLEtBQUssQ0FBQ0ksV0FBVyxJQUFJQSxXQUFXO2dCQUN0RCxDQUFDOztRQUVULENBQUM7SUFDTCxDQUFDO0lBQ0Q2QyxnQkFBZ0IsQ0FBQ0MsS0FBSyxFQUFFLENBQUM7UUFDckIsTUFBTSxDQUFDRCxnQkFBZ0IsQ0FBQyxJQUFJLENBQUNsRCxPQUFPLEVBQUUsSUFBSSxDQUFDQyxLQUFLLEVBQUVrRCxLQUFLO0lBQzNELENBQUM7SUFDRE4saUJBQWlCLEdBQUcsQ0FBQztRQUNqQixNQUFNLENBQUNBLGlCQUFpQixDQUFDLElBQUksQ0FBQzdDLE9BQU8sRUFBRSxJQUFJLENBQUNDLEtBQUs7SUFDckQsQ0FBQztJQUNEd0QsVUFBVSxDQUFDTixLQUFLLEVBQUUsQ0FBQztRQUNmLE1BQU0sQ0FBQ00sVUFBVSxDQUFDLElBQUksQ0FBQ3pELE9BQU8sRUFBRSxJQUFJLENBQUNDLEtBQUssRUFBRWtELEtBQUs7SUFDckQsQ0FBQztJQUNEcEQsa0JBQWtCLEdBQUcsQ0FBQztRQUNsQixNQUFNLENBQUNBLGtCQUFrQixDQUFDLElBQUksQ0FBQ0MsT0FBTyxFQUFFLElBQUksQ0FBQ0MsS0FBSztJQUN0RCxDQUFDO0lBQ0Q2RywrQkFBK0IsQ0FBQ3RGLFFBQVEsRUFBRSxDQUFDO1FBQ3ZDLEtBQUssQ0FBQyxDQUFDLENBQUNMLFlBQVksRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDbkIsT0FBTztRQUN0QyxLQUFLLENBQUMrRyxpQkFBaUIsR0FBRyxDQUFDLENBQUM7UUFDNUIsS0FBSyxDQUFDQyxnQkFBZ0IsR0FBRyxDQUFDLENBQUM7UUFDM0JoSixNQUFNLENBQUNELE9BQU8sQ0FBQ2tKLFFBQVEsQ0FBQzdCLE9BQU8sQ0FBQzVELFFBQVEsR0FBR1AsS0FBSyxHQUFHLENBQUM7WUFDaEQsRUFBRSxFQUFFQSxLQUFLLENBQUN3QixJQUFJLEtBQUtsRSxPQUFPLENBQUNSLE9BQU8sRUFBRSxDQUFDO2dCQUNqQyxFQUFFLEVBQUVrRCxLQUFLLENBQUNoQixLQUFLLENBQUNxQyxRQUFRLEtBQUssQ0FBbUIsb0JBQUUsQ0FBQztvQkFDL0NuQixZQUFZLENBQUM2QixpQkFBaUIsSUFBSTdCLFlBQVksQ0FBQzZCLGlCQUFpQixJQUFJLENBQUMsQ0FBQyxFQUFFa0UsTUFBTSxDQUFDLENBQUM7d0JBQzVFLENBQUM7K0JBQ01qRyxLQUFLLENBQUNoQixLQUFLO3dCQUNsQixDQUFDO29CQUNMLENBQUMsQ0FBQyxDQUFDO29CQUNILE1BQU07Z0JBQ1YsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDO29CQUNSLENBQVk7b0JBQ1osQ0FBa0I7b0JBQ2xCLENBQVE7Z0JBQ1osQ0FBQyxDQUFDcUQsUUFBUSxDQUFDckMsS0FBSyxDQUFDaEIsS0FBSyxDQUFDcUMsUUFBUSxHQUFHLENBQUM7b0JBQy9CeUUsaUJBQWlCLENBQUMxQixJQUFJLENBQUNwRSxLQUFLLENBQUNoQixLQUFLLENBQUMsQ0FBQztvQkFDcEMsTUFBTTtnQkFDVixDQUFDO1lBQ0wsQ0FBQztZQUNEK0csZ0JBQWdCLENBQUMzQixJQUFJLENBQUNwRSxLQUFLLENBQUMsQ0FBQztRQUNqQyxDQUFDLENBQUMsQ0FBQztRQUNILElBQUksQ0FBQ2pCLE9BQU8sQ0FBQ21ILGFBQWEsQ0FBQ2hHLFlBQVksR0FBRzRGLGlCQUFpQixDQUFDO1FBQzVELE1BQU0sQ0FBQ0MsZ0JBQWdCO0lBQzNCLENBQUM7SUFDRFAsbUJBQW1CLENBQUNXLElBQUksRUFBRSxDQUFDO1FBQ3ZCLE1BQU0sQ0FBQ3BKLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDa0osUUFBUSxDQUFDdkcsR0FBRyxDQUFDMEcsSUFBSSxHQUFHQyxDQUFDLEdBQUcsQ0FBQztZQUMzQyxHQUFHLENBQUNDLElBQUksRUFBRUMsSUFBSTtZQUNkLEVBQUUsR0FBR0YsQ0FBQyxLQUFLLElBQUksSUFBSUEsQ0FBQyxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsR0FBR0EsQ0FBQyxDQUFDNUUsSUFBSSxNQUFNLENBQU0sVUFBSzRFLENBQUMsS0FBSyxJQUFJLElBQUlBLENBQUMsS0FBSyxJQUFJLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFDLElBQUlDLElBQUksR0FBR0QsQ0FBQyxDQUFDcEgsS0FBSyxNQUFNLElBQUksSUFBSXFILElBQUksS0FBSyxJQUFJLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFDLEdBQUdBLElBQUksQ0FBQ2hCLElBQUksS0FBS25JLFVBQVUsQ0FBQ3FKLHdCQUF3QixDQUFDQyxJQUFJLEVBQUUsQ0FBQyxDQUFDQyxHQUFHLEVBQUUsQ0FBQyxHQUFHLENBQUM7Z0JBQ2hPLEdBQUcsQ0FBQzdGLEdBQUcsRUFBRThGLElBQUk7Z0JBQ2IsTUFBTSxDQUFDTixDQUFDLEtBQUssSUFBSSxJQUFJQSxDQUFDLEtBQUssSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsQ0FBQyxJQUFJeEYsR0FBRyxHQUFHd0YsQ0FBQyxDQUFDcEgsS0FBSyxNQUFNLElBQUksSUFBSTRCLEdBQUcsS0FBSyxJQUFJLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFDLElBQUk4RixJQUFJLEdBQUc5RixHQUFHLENBQUN5RSxJQUFJLE1BQU0sSUFBSSxJQUFJcUIsSUFBSSxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsR0FBR0EsSUFBSSxDQUFDQyxVQUFVLENBQUNGLEdBQUc7WUFDbkwsQ0FBQyxHQUFHLENBQUM7Z0JBQ0QsS0FBSyxDQUFDRyxRQUFRLEdBQUcsQ0FBQzt1QkFDWFIsQ0FBQyxDQUFDcEgsS0FBSyxJQUFJLENBQUMsQ0FBQztvQkFDaEIsQ0FBVyxZQUFFb0gsQ0FBQyxDQUFDcEgsS0FBSyxDQUFDcUcsSUFBSTtvQkFDekJBLElBQUksRUFBRTFCLFNBQVM7Z0JBQ25CLENBQUM7Z0JBQ0QsTUFBTSxDQUFDLEVBQWEsWUFBQzVHLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDK0osWUFBWSxDQUFDVCxDQUFDLEVBQUVRLFFBQVE7WUFDaEUsQ0FBQyxNQUFNLEVBQUUsRUFBRVIsQ0FBQyxLQUFLLElBQUksSUFBSUEsQ0FBQyxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsSUFBSUUsSUFBSSxHQUFHRixDQUFDLENBQUNwSCxLQUFLLE1BQU0sSUFBSSxJQUFJc0gsSUFBSSxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsR0FBR0EsSUFBSSxDQUFDL0YsUUFBUSxFQUFFLENBQUM7Z0JBQ3JILEtBQUssQ0FBQ3FHLFFBQVEsR0FBRyxDQUFDO3VCQUNYUixDQUFDLENBQUNwSCxLQUFLLElBQUksQ0FBQyxDQUFDO29CQUNoQnVCLFFBQVEsRUFBRSxJQUFJLENBQUNpRixtQkFBbUIsQ0FBQ1ksQ0FBQyxDQUFDcEgsS0FBSyxDQUFDdUIsUUFBUTtnQkFDdkQsQ0FBQztnQkFDRCxNQUFNLENBQUMsRUFBYSxZQUFDeEQsTUFBTSxDQUFDRCxPQUFPLENBQUMrSixZQUFZLENBQUNULENBQUMsRUFBRVEsUUFBUTtZQUNoRSxDQUFDO1lBQ0QsTUFBTSxDQUFDUixDQUFDO1FBQ1osQ0FBQyxFQUFFOUcsTUFBTSxDQUFDb0csT0FBTztJQUNyQixDQUFDO0lBQ0R6QyxNQUFNLEdBQUcsQ0FBQztRQUNOLEtBQUssQ0FBQyxDQUFDLENBQUNhLE1BQU0sR0FBR2dELE9BQU8sR0FBR3RJLFNBQVMsR0FBR3VJLFNBQVMsR0FBR0MsYUFBYSxHQUFHZCxhQUFhLEdBQUdlLGVBQWUsR0FBR0MsUUFBUSxHQUFHQyxrQkFBa0IsR0FBR0Msa0JBQWtCLEdBQUdqSSx1QkFBdUIsR0FBR3NGLFdBQVcsR0FBR0MsYUFBYSxJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMzRixPQUFPO1FBQ25PLEtBQUssQ0FBQ3NJLGdCQUFnQixHQUFHRixrQkFBa0IsS0FBSyxLQUFLO1FBQ3JELEtBQUssQ0FBQ0csZ0JBQWdCLEdBQUdGLGtCQUFrQixLQUFLLEtBQUssS0FBS2pJLHVCQUF1QjtRQUNqRixJQUFJLENBQUNKLE9BQU8sQ0FBQ3VFLHFCQUFxQixDQUFDSixJQUFJLEdBQUcsSUFBSSxDQUFDO1FBQy9DLEdBQUcsQ0FBQyxDQUFDLENBQUNxRSxJQUFJLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQ3hJLE9BQU87UUFDNUIsR0FBRyxDQUFDeUksV0FBVyxHQUFHLENBQUMsQ0FBQztRQUNwQixHQUFHLENBQUNDLGlCQUFpQixHQUFHLENBQUMsQ0FBQztRQUMxQixFQUFFLEVBQUVGLElBQUksRUFBRSxDQUFDO1lBQ1BBLElBQUksQ0FBQ3BELE9BQU8sRUFBRWlDLENBQUMsR0FBRyxDQUFDO2dCQUNmLEVBQUUsRUFBRUEsQ0FBQyxJQUFJQSxDQUFDLENBQUM1RSxJQUFJLEtBQUssQ0FBTSxTQUFJNEUsQ0FBQyxDQUFDcEgsS0FBSyxDQUFDLENBQUssVUFBTSxDQUFTLFlBQUlvSCxDQUFDLENBQUNwSCxLQUFLLENBQUMsQ0FBSSxTQUFNLENBQU8sUUFBRSxDQUFDO29CQUN0RndJLFdBQVcsQ0FBQ3BELElBQUksQ0FBQ2dDLENBQUMsQ0FBQyxDQUFDO2dCQUN4QixDQUFDLE1BQU0sQ0FBQztvQkFDSkEsQ0FBQyxJQUFJcUIsaUJBQWlCLENBQUNyRCxJQUFJLENBQUNnQyxDQUFDLENBQUMsQ0FBQztnQkFDbkMsQ0FBQztZQUNMLENBQUMsQ0FBQyxDQUFDO1lBQ0htQixJQUFJLEdBQUdDLFdBQVcsQ0FBQ3ZCLE1BQU0sQ0FBQ3dCLGlCQUFpQixDQUFDLENBQUM7UUFDakQsQ0FBQztRQUNELEdBQUcsQ0FBQ2xILFFBQVEsR0FBR3hELE1BQU0sQ0FBQ0QsT0FBTyxDQUFDa0osUUFBUSxDQUFDMEIsT0FBTyxDQUFDLElBQUksQ0FBQzFJLEtBQUssQ0FBQ3VCLFFBQVEsRUFBRWpCLE1BQU0sQ0FBQ29HLE9BQU87UUFDbEYsRUFBZ0U7UUFDaEUsRUFBRSxFQXhZVixJQXdZaUQsRUFBRSxDQUFDO1lBQ3hDbkYsUUFBUSxHQUFHeEQsTUFBTSxDQUFDRCxPQUFPLENBQUNrSixRQUFRLENBQUN2RyxHQUFHLENBQUNjLFFBQVEsR0FBR1AsS0FBSyxHQUFHLENBQUM7Z0JBQ3ZELEdBQUcsQ0FBQ1ksR0FBRztnQkFDUCxLQUFLLENBQUMrRyxhQUFhLEdBQUczSCxLQUFLLEtBQUssSUFBSSxJQUFJQSxLQUFLLEtBQUssSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsQ0FBQyxJQUFJWSxHQUFHLEdBQUdaLEtBQUssQ0FBQ2hCLEtBQUssTUFBTSxJQUFJLElBQUk0QixHQUFHLEtBQUssSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsQ0FBQyxHQUFHQSxHQUFHLENBQUMsQ0FBbUI7Z0JBQ3JKLEVBQUUsR0FBRytHLGFBQWEsRUFBRSxDQUFDO29CQUNqQixHQUFHLENBQUNDLElBQUk7b0JBQ1IsRUFBRSxHQUFHNUgsS0FBSyxLQUFLLElBQUksSUFBSUEsS0FBSyxLQUFLLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsR0FBR0EsS0FBSyxDQUFDd0IsSUFBSSxNQUFNLENBQU8sUUFBRSxDQUFDO3dCQUN6RUUsT0FBTyxDQUFDQyxJQUFJLENBQUMsQ0FBa0gsa0hBQUMsQ0FBQztvQkFDckksQ0FBQyxNQUFNLEVBQUUsR0FBRzNCLEtBQUssS0FBSyxJQUFJLElBQUlBLEtBQUssS0FBSyxJQUFJLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFDLEdBQUdBLEtBQUssQ0FBQ3dCLElBQUksTUFBTSxDQUFNLFVBQUt4QixLQUFLLEtBQUssSUFBSSxJQUFJQSxLQUFLLEtBQUssSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsQ0FBQyxJQUFJNEgsSUFBSSxHQUFHNUgsS0FBSyxDQUFDaEIsS0FBSyxNQUFNLElBQUksSUFBSTRJLElBQUksS0FBSyxJQUFJLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFDLEdBQUdBLElBQUksQ0FBQ0MsSUFBSSxNQUFNLENBQVUsV0FBRSxDQUFDO3dCQUN2Tm5HLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLENBQXFJLHFJQUFDLENBQUM7b0JBQ3hKLENBQUM7Z0JBQ0wsQ0FBQztnQkFDRCxNQUFNLENBQUMzQixLQUFLO1lBQ2hCLENBQUMsQ0FBQyxDQUFDO1lBQ0gsRUFBRSxFQUFFLElBQUksQ0FBQ2hCLEtBQUssQ0FBQ0ksV0FBVyxFQUFFc0MsT0FBTyxDQUFDQyxJQUFJLENBQUMsQ0FBb0gsb0hBQUMsQ0FBQztRQUNuSyxDQUFDO1FBQ0QsRUFBRSxFQXhaVixLQXdaaUYsRUFBRSxFQUUxRTtRQUNEcEIsUUFBUSxHQUFHLElBQUksQ0FBQ3NGLCtCQUErQixDQUFDdEYsUUFBUSxDQUFDLENBQUM7UUFDMUQsR0FBRyxDQUFDdUgsYUFBYSxHQUFHLEtBQUs7UUFDekIsR0FBRyxDQUFDQyxlQUFlLEdBQUcsS0FBSztRQUMzQixFQUFvRDtRQUNwRFIsSUFBSSxHQUFHeEssTUFBTSxDQUFDRCxPQUFPLENBQUNrSixRQUFRLENBQUN2RyxHQUFHLENBQUM4SCxJQUFJLElBQUksQ0FBQyxDQUFDLEdBQUd2SCxLQUFLLEdBQUcsQ0FBQztZQUNyRCxFQUFFLEdBQUdBLEtBQUssRUFBRSxNQUFNLENBQUNBLEtBQUs7WUFDeEIsS0FBSyxDQUFDLENBQUMsQ0FBQ3dCLElBQUksR0FBR3hDLEtBQUssRUFBRSxDQUFDLEdBQUdnQixLQUFLO1lBQy9CLEVBQUUsRUFBRXhCLFNBQVMsRUFBRSxDQUFDO2dCQUNaLEdBQUcsQ0FBQ3dKLE9BQU8sR0FBRyxDQUFFO2dCQUNoQixFQUFFLEVBQUV4RyxJQUFJLEtBQUssQ0FBTSxTQUFJeEMsS0FBSyxDQUFDNkksSUFBSSxLQUFLLENBQVUsV0FBRSxDQUFDO29CQUMvQ0csT0FBTyxHQUFHLENBQWlCLGlCQUFDO2dCQUNoQyxDQUFDLE1BQU0sRUFBRSxFQUFFeEcsSUFBSSxLQUFLLENBQU0sU0FBSXhDLEtBQUssQ0FBQ29HLEdBQUcsS0FBSyxDQUFXLFlBQUUsQ0FBQztvQkFDdEQyQyxlQUFlLEdBQUcsSUFBSSxDQUFDO2dCQUMzQixDQUFDLE1BQU0sRUFBRSxFQUFFdkcsSUFBSSxLQUFLLENBQVEsU0FBRSxDQUFDO29CQUMzQixFQUFnQjtvQkFDaEIsRUFBeUQ7b0JBQ3pELEVBQTJEO29CQUMzRCxFQUE0QjtvQkFDNUIsRUFBRSxFQUFFeEMsS0FBSyxDQUFDYyxHQUFHLElBQUlkLEtBQUssQ0FBQ2MsR0FBRyxDQUFDbUksT0FBTyxDQUFDLENBQVksZ0JBQUssQ0FBQyxJQUFJakosS0FBSyxDQUFDOEIsdUJBQXVCLE1BQU05QixLQUFLLENBQUN3QyxJQUFJLElBQUl4QyxLQUFLLENBQUN3QyxJQUFJLEtBQUssQ0FBaUIsbUJBQUcsQ0FBQzt3QkFDMUl3RyxPQUFPLEdBQUcsQ0FBUyxTQUFDO3dCQUNwQnhMLE1BQU0sQ0FBQzBMLElBQUksQ0FBQ2xKLEtBQUssRUFBRW1GLE9BQU8sRUFBRWdFLElBQUksR0FBRyxDQUFDOzRCQUNoQ0gsT0FBTyxLQUFLLENBQUMsRUFBRUcsSUFBSSxDQUFDLEVBQUUsRUFBRW5KLEtBQUssQ0FBQ21KLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQzt3QkFDM0MsQ0FBQyxDQUFDLENBQUM7d0JBQ0hILE9BQU8sSUFBSSxDQUFJLElBQUM7b0JBQ3BCLENBQUM7Z0JBQ0wsQ0FBQztnQkFDRCxFQUFFLEVBQUVBLE9BQU8sRUFBRSxDQUFDO29CQUNWdEcsT0FBTyxDQUFDQyxJQUFJLEVBQUUsMkJBQTJCLEVBQUUzQixLQUFLLENBQUN3QixJQUFJLENBQUMsd0JBQXdCLEVBQUV3RyxPQUFPLENBQUMsSUFBSSxFQUFFOUIsYUFBYSxDQUFDa0MsSUFBSSxDQUFDLHNEQUFzRCxFQUFFLENBQUM7b0JBQzFLLE1BQU0sQ0FBQyxJQUFJO2dCQUNmLENBQUM7WUFDTCxDQUFDLE1BQU0sQ0FBQztnQkFDSixFQUFlO2dCQUNmLEVBQUUsRUFBRTVHLElBQUksS0FBSyxDQUFNLFNBQUl4QyxLQUFLLENBQUNvRyxHQUFHLEtBQUssQ0FBUyxVQUFFLENBQUM7b0JBQzdDMEMsYUFBYSxHQUFHLElBQUksQ0FBQztnQkFDekIsQ0FBQztZQUNMLENBQUM7WUFDRCxNQUFNLENBQUM5SCxLQUFLO1FBQ2hCLENBQUMsQ0FBQyxDQUFDO1FBQ0gsS0FBSyxDQUFDa0MsS0FBSyxHQUFHN0QsZ0JBQWdCLENBQUMsSUFBSSxDQUFDVSxPQUFPLENBQUNULGFBQWEsRUFBRSxJQUFJLENBQUNTLE9BQU8sQ0FBQ21ILGFBQWEsQ0FBQ2tDLElBQUksRUFBRTVKLFNBQVM7UUFDckcsR0FBRyxDQUFDNkosTUFBTSxFQUFFQyxPQUFPO1FBQ25CLE1BQU0sQ0FBQyxFQUFhLFlBQUN2TCxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFNLE9BQUVsRCxNQUFNLENBQUMrRSxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDdkMsS0FBSyxHQUFHLElBQUksQ0FBQ0QsT0FBTyxDQUFDcUQsYUFBYSxJQUFJLEVBQWEsWUFBQ3JGLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDM0MsTUFBTSxDQUFDRCxPQUFPLENBQUNtRSxRQUFRLEVBQUUsSUFBSSxFQUFFLEVBQWEsWUFBQ2xFLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDLENBQU8sUUFBRSxDQUFDO1lBQ2xQLENBQXFCLHNCQUFFLElBQUk7WUFDM0IsQ0FBaUIsa0JBQUVsQixTQUFTLEdBQUcsQ0FBTSxRQUFHbUYsU0FBUztZQUNqRDdDLHVCQUF1QixFQUFFLENBQUM7Z0JBQ3RCQyxNQUFNLEdBQUcsa0JBQWtCO1lBQy9CLENBQUM7UUFDTCxDQUFDLEdBQUcsRUFBYSxZQUFDaEUsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBVSxXQUFFLENBQUM7WUFDeEQsQ0FBcUIsc0JBQUUsSUFBSTtZQUMzQixDQUFpQixrQkFBRWxCLFNBQVMsR0FBRyxDQUFNLFFBQUdtRixTQUFTO1FBQ3JELENBQUMsRUFBRSxFQUFhLFlBQUM1RyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFPLFFBQUUsQ0FBQztZQUNwRG9CLHVCQUF1QixFQUFFLENBQUM7Z0JBQ3RCQyxNQUFNLEdBQUcsbUJBQW1CO1lBQ2hDLENBQUM7UUFDTCxDQUFDLEtBQUt3RyxJQUFJLEVBQUUsRUFBYSxZQUFDeEssTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTSxPQUFFLENBQUM7WUFDNURtSSxJQUFJLEVBQUUsQ0FBaUI7WUFDdkJVLE9BQU8sRUFBRXhMLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDa0osUUFBUSxDQUFDd0MsS0FBSyxDQUFDakIsSUFBSSxJQUFJLENBQUMsQ0FBQyxFQUFFa0IsUUFBUTtRQUMvRCxDQUFDLEdBQUdsSSxRQUFRLEVBQUVtRSxhQUFhLElBQUksRUFBYSxZQUFDM0gsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTSxPQUFFLENBQUM7WUFDL0VtSSxJQUFJLEVBQUUsQ0FBc0I7UUFDaEMsQ0FBQyxHQUFHckosU0FBUyxJQUFJLEVBQWEsWUFBQ3pCLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDM0MsTUFBTSxDQUFDRCxPQUFPLENBQUNtRSxRQUFRLEVBQUUsSUFBSSxFQUFFLEVBQWEsWUFBQ2xFLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDLENBQU0sT0FBRSxDQUFDO1lBQzNJbUksSUFBSSxFQUFFLENBQVU7WUFDaEJVLE9BQU8sRUFBRSxDQUFvRDtRQUNqRSxDQUFDLElBQUlSLGVBQWUsSUFBSSxFQUFhLFlBQUNoTCxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFNLE9BQUUsQ0FBQztZQUN4RTBGLEdBQUcsRUFBRSxDQUFXO1lBQ2hCQyxJQUFJLEVBQUUyQixhQUFhLElBQUksQ0FBQyxFQUFFNUosTUFBTSxFQUFFc0wsWUFBWSxDQUFDekIsZUFBZTtRQUNsRSxDQUFDLEdBQUcsRUFBYSxZQUFDbEssTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTSxPQUFFLENBQUM7WUFDcEQwRixHQUFHLEVBQUUsQ0FBUztZQUNkRSxFQUFFLEVBQUUsQ0FBUTtZQUNaRCxJQUFJLEVBQUUsQ0FBa0M7UUFDNUMsQ0FBQyxHQUFHLEVBQWEsWUFBQ3RJLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDbUUsU0FBUyxFQUFFLENBQUM7WUFDdkRDLE1BQU0sRUFBRUEsTUFBTTtRQUNsQixDQUFDLEdBQUcsRUFBYSxZQUFDL0csTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTyxRQUFFLENBQUM7WUFDckQsQ0FBaUIsa0JBQUUsQ0FBRTtZQUNyQm9CLHVCQUF1QixFQUFFLENBQUM7Z0JBQ3RCQyxNQUFNLEdBQUcsc2xCQUFzbEI7WUFDbm1CLENBQUM7UUFDTCxDQUFDLEdBQUcsRUFBYSxZQUFDaEUsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBVSxXQUFFLElBQUksRUFBRSxFQUFhLFlBQUMzQyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFPLFFBQUUsQ0FBQztZQUNsSCxDQUFpQixrQkFBRSxDQUFFO1lBQ3JCb0IsdUJBQXVCLEVBQUUsQ0FBQztnQkFDdEJDLE1BQU0sR0FBRyxrRkFBa0Y7WUFDL0YsQ0FBQztRQUNMLENBQUMsSUFBSSxFQUFhLFlBQUNoRSxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFRLFNBQUUsQ0FBQztZQUN2RDRDLEtBQUssRUFBRSxJQUFJO1lBQ1h4QyxHQUFHLEVBQUUsQ0FBa0M7UUFDM0MsQ0FBQyxLQUFLdEIsU0FBUyxJQUFJLEVBQWEsWUFBQ3pCLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDM0MsTUFBTSxDQUFDRCxPQUFPLENBQUNtRSxRQUFRLEVBQUUsSUFBSSxHQUFHNkcsYUFBYSxJQUFJZixTQUFTLElBQUksRUFBYSxZQUFDaEssTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBTSxPQUFFLENBQUM7WUFDNUswRixHQUFHLEVBQUUsQ0FBUztZQUNkQyxJQUFJLEVBQUUyQixhQUFhLEdBQUcyQixVQUFVLENBQUM3QixPQUFPLEVBQUVHLGVBQWU7UUFDN0QsQ0FBQyxJQUFJeEMsV0FBVyxJQUFJLElBQUksQ0FBQ0QsV0FBVyxDQUFDdEMsS0FBSyxJQUFJdUMsV0FBVyxJQUFJLEVBQWEsWUFBQzFILE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDLENBQVUsV0FBRSxDQUFDO1lBQ2pILENBQVksY0FBRzJJLE1BQU0sR0FBRyxJQUFJLENBQUNySixLQUFLLENBQUNZLEtBQUssTUFBTSxJQUFJLElBQUl5SSxNQUFNLEtBQUssSUFBSSxDQUFDLENBQUMsR0FBR0EsTUFBTSxHQUFHLENBQUU7UUFDekYsQ0FBQyxJQUFJaEIsZ0JBQWdCLEtBQUtDLGdCQUFnQixJQUFJLElBQUksQ0FBQzdCLHVCQUF1QixLQUFLNEIsZ0JBQWdCLEtBQUtDLGdCQUFnQixJQUFJLElBQUksQ0FBQzNCLG1CQUFtQixDQUFDekQsS0FBSyxJQUFJL0MsdUJBQXVCLEtBQUtrSSxnQkFBZ0IsSUFBSSxJQUFJLENBQUN2SSxrQkFBa0IsS0FBS0ssdUJBQXVCLEtBQUtrSSxnQkFBZ0IsSUFBSSxJQUFJLENBQUN6RixpQkFBaUIsS0FBS3pDLHVCQUF1QixLQUFLa0ksZ0JBQWdCLElBQUksSUFBSSxDQUFDcEYsZ0JBQWdCLENBQUNDLEtBQUssSUFBSS9DLHVCQUF1QixLQUFLa0ksZ0JBQWdCLElBQUksSUFBSSxDQUFDN0UsVUFBVSxDQUFDTixLQUFLLEdBQUd1QyxXQUFXLElBQUksSUFBSSxDQUFDRCxXQUFXLENBQUN0QyxLQUFLLEdBQUd1QyxXQUFXLElBQUksRUFBYSxZQUFDMUgsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBVSxXQUFFLENBQUM7WUFDbmpCLENBQVksY0FBRzRJLE9BQU8sR0FBRyxJQUFJLENBQUN0SixLQUFLLENBQUNZLEtBQUssTUFBTSxJQUFJLElBQUkwSSxPQUFPLEtBQUssSUFBSSxDQUFDLENBQUMsR0FBR0EsT0FBTyxHQUFHLENBQUU7UUFDNUYsQ0FBQyxHQUFHLElBQUksQ0FBQ3ZKLE9BQU8sQ0FBQ3FELGFBQWEsSUFDOUIsRUFBOEI7UUFDOUIsRUFBK0Q7UUFDL0QsRUFBYSxZQUFDckYsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMsQ0FBVSxXQUFFLENBQUM7WUFDcERrSixFQUFFLEVBQUUsQ0FBMEI7UUFDbEMsQ0FBQyxHQUFHOUUsTUFBTSxJQUFJLElBQUksR0FBRyxFQUFhLFlBQUMvRyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQzNDLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDbUUsUUFBUSxFQUFFLENBQUMsQ0FBQyxLQUFLaUcsUUFBUSxJQUFJLENBQUMsQ0FBQztJQUNsSCxDQUFDOztBQUVMeEssWUFBWSxHQUFHd0csSUFBSSxDQUFDO0FBQ3BCQSxJQUFJLENBQUMyRixXQUFXLEdBQUdwTCxZQUFZLENBQUNnRyxXQUFXLENBQUM7U0FDbkM1RyxJQUFJLEdBQUcsQ0FBQztJQUNiLEtBQUssQ0FBQyxDQUFDLENBQUN5RyxxQkFBcUIsRUFBRSxDQUFDLElBQUksQ0FBQyxFQUFFdkcsTUFBTSxFQUFFeUcsVUFBVSxDQUFDL0YsWUFBWSxDQUFDZ0csV0FBVztJQUNsRkgscUJBQXFCLENBQUN6RyxJQUFJLEdBQUcsSUFBSSxDQUFDO0lBQ2xDLEVBQWE7SUFDYixNQUFNLENBQUMsRUFBYSxZQUFDRSxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFxQyxzQ0FBRSxJQUFJO0FBQ2pHLENBQUM7TUFDS3lELFVBQVUsU0FBU3BHLE1BQU0sQ0FBQzhGLFNBQVM7SUFDckNaLGdCQUFnQixDQUFDQyxLQUFLLEVBQUUsQ0FBQztRQUNyQixNQUFNLENBQUNELGdCQUFnQixDQUFDLElBQUksQ0FBQ2xELE9BQU8sRUFBRSxJQUFJLENBQUNDLEtBQUssRUFBRWtELEtBQUs7SUFDM0QsQ0FBQztJQUNETixpQkFBaUIsR0FBRyxDQUFDO1FBQ2pCLE1BQU0sQ0FBQ0EsaUJBQWlCLENBQUMsSUFBSSxDQUFDN0MsT0FBTyxFQUFFLElBQUksQ0FBQ0MsS0FBSztJQUNyRCxDQUFDO0lBQ0R3RCxVQUFVLENBQUNOLEtBQUssRUFBRSxDQUFDO1FBQ2YsTUFBTSxDQUFDTSxVQUFVLENBQUMsSUFBSSxDQUFDekQsT0FBTyxFQUFFLElBQUksQ0FBQ0MsS0FBSyxFQUFFa0QsS0FBSztJQUNyRCxDQUFDO0lBQ0RwRCxrQkFBa0IsR0FBRyxDQUFDO1FBQ2xCLE1BQU0sQ0FBQ0Esa0JBQWtCLENBQUMsSUFBSSxDQUFDQyxPQUFPLEVBQUUsSUFBSSxDQUFDQyxLQUFLO0lBQ3RELENBQUM7V0FDTThKLHFCQUFxQixDQUFDL0osT0FBTyxFQUFFLENBQUM7UUFDbkMsS0FBSyxDQUFDLENBQUMsQ0FBQ21ILGFBQWEsRUFBRSxDQUFDLEdBQUduSCxPQUFPO1FBQ2xDLEdBQUcsQ0FBQyxDQUFDO1lBQ0QsS0FBSyxDQUFDZ0ssSUFBSSxHQUFHQyxJQUFJLENBQUNDLFNBQVMsQ0FBQy9DLGFBQWE7WUFDekMsRUFBRSxFQXRoQmQsSUFzaEJzRCxFQUFFLENBQUM7Z0JBQ3pDLEtBQUssQ0FBQ2dELEtBQUssR0FBR0MsTUFBTSxDQUFDcEUsSUFBSSxDQUFDZ0UsSUFBSSxFQUFFSyxVQUFVO2dCQUMxQyxLQUFLLENBQUNDLFdBQVcsR0FBR3BNLDJHQUFzQztnQkFDMUQsRUFBRSxFQUFFaU0sS0FBSyxHQUFHLEdBQUcsR0FBRyxJQUFJLEVBQUUsQ0FBQztvQkFDckJ4SCxPQUFPLENBQUNDLElBQUksRUFBRSx3QkFBd0IsRUFBRXVFLGFBQWEsQ0FBQ2tDLElBQUksQ0FBQyxLQUFLLEVBQUVpQixXQUFXLENBQUNILEtBQUssRUFBRSxtSEFBbUgsRUFBRSxDQUFDO2dCQUMvTSxDQUFDO1lBQ0wsQ0FBQztZQUNELE1BQU0sRUFBRSxDQUFDLEVBQUU3TCxXQUFXLEVBQUVpTSxvQkFBb0IsQ0FBQ1AsSUFBSTtRQUNyRCxDQUFDLENBQUMsS0FBSyxFQUFFdEgsR0FBRyxFQUFFLENBQUM7WUFDWCxFQUFFLEdBQUcsQ0FBQyxFQUFFakUsUUFBUSxFQUFFVixPQUFPLENBQUMyRSxHQUFHLEtBQUtBLEdBQUcsQ0FBQzhILE9BQU8sQ0FBQ3RCLE9BQU8sQ0FBQyxDQUFvQiwwQkFBTyxDQUFDLEVBQUUsQ0FBQztnQkFDakYsS0FBSyxDQUFDLEdBQUcsQ0FBQ3VCLEtBQUssRUFBRSx3REFBd0QsRUFBRXRELGFBQWEsQ0FBQ2tDLElBQUksQ0FBQyxzREFBc0Q7WUFDeEosQ0FBQztZQUNELEtBQUssQ0FBQzNHLEdBQUc7UUFDYixDQUFDO0lBQ0wsQ0FBQztJQUNEd0IsTUFBTSxHQUFHLENBQUM7UUFDTixLQUFLLENBQUMsQ0FBQyxDQUFDaEUsV0FBVyxHQUFHVCxTQUFTLEdBQUdGLGFBQWEsR0FBRzZJLGtCQUFrQixHQUFHN0QscUJBQXFCLEdBQUdwRSw2QkFBNkIsR0FBR0MsdUJBQXVCLEdBQUdDLFdBQVcsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDTCxPQUFPO1FBQ3hMLEtBQUssQ0FBQ3NJLGdCQUFnQixHQUFHRixrQkFBa0IsS0FBSyxLQUFLO1FBQ3JEN0QscUJBQXFCLENBQUNILFVBQVUsR0FBRyxJQUFJLENBQUM7UUFDeEMsRUFBRSxFQUFFM0UsU0FBUyxFQUFFLENBQUM7WUFDWixFQUFFLEVBMWlCZCxLQTBpQnFELEVBQUUsRUFFMUM7WUFDRCxLQUFLLENBQUNpTCxXQUFXLEdBQUcsQ0FBQzttQkFDZG5MLGFBQWEsQ0FBQ29MLFFBQVE7bUJBQ3RCcEwsYUFBYSxDQUFDZSxhQUFhO21CQUMzQmYsYUFBYSxDQUFDbUwsV0FBVztZQUNoQyxDQUFDO1lBQ0QsTUFBTSxDQUFDLEVBQWEsWUFBQzFNLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDM0MsTUFBTSxDQUFDRCxPQUFPLENBQUNtRSxRQUFRLEVBQUUsSUFBSSxFQUFFb0csZ0JBQWdCLEdBQUcsSUFBSSxHQUFHLEVBQWEsWUFBQ3RLLE1BQU0sQ0FBQ0QsT0FBTyxDQUFDNEMsYUFBYSxDQUFDLENBQVEsU0FBRSxDQUFDO2dCQUM3SmtKLEVBQUUsRUFBRSxDQUFlO2dCQUNuQnBILElBQUksRUFBRSxDQUFrQjtnQkFDeEI1QixLQUFLLEVBQUUsSUFBSSxDQUFDWixLQUFLLENBQUNZLEtBQUs7Z0JBQ3ZCUixXQUFXLEVBQUUsSUFBSSxDQUFDSixLQUFLLENBQUNJLFdBQVcsSUFBSUEsV0FBVztnQkFDbEQwQix1QkFBdUIsRUFBRSxDQUFDO29CQUN0QkMsTUFBTSxFQUFFb0MsVUFBVSxDQUFDMkYscUJBQXFCLENBQUMsSUFBSSxDQUFDL0osT0FBTztnQkFDekQsQ0FBQztnQkFDRCxDQUFpQixrQkFBRSxJQUFJO1lBQzNCLENBQUMsR0FBRzBLLFdBQVcsQ0FBQ2hLLEdBQUcsRUFBRTBCLElBQUksR0FBRyxFQUFhLFlBQUNwRSxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFRLFNBQUUsQ0FBQztvQkFDMUU3QixHQUFHLEVBQUVzRCxJQUFJO29CQUNUckIsR0FBRyxLQUFLYixXQUFXLENBQUMsT0FBTyxFQUFFa0MsSUFBSSxHQUFHakMsNkJBQTZCO29CQUNqRVUsS0FBSyxFQUFFLElBQUksQ0FBQ1osS0FBSyxDQUFDWSxLQUFLO29CQUN2QlIsV0FBVyxFQUFFLElBQUksQ0FBQ0osS0FBSyxDQUFDSSxXQUFXLElBQUlBLFdBQVc7b0JBQ2xELENBQWlCLGtCQUFFLElBQUk7Z0JBQzNCLENBQUM7O1FBRVQsQ0FBQztRQUNELEVBQUUsRUFwa0JWLElBb2tCaUQsRUFBRSxDQUFDO1lBQ3hDLEVBQUUsRUFBRSxJQUFJLENBQUNKLEtBQUssQ0FBQ0ksV0FBVyxFQUFFc0MsT0FBTyxDQUFDQyxJQUFJLENBQUMsQ0FBMEgsMEhBQUMsQ0FBQztRQUN6SyxDQUFDO1FBQ0QsS0FBSyxDQUFDTyxLQUFLLEdBQUc3RCxnQkFBZ0IsQ0FBQyxJQUFJLENBQUNVLE9BQU8sQ0FBQ1QsYUFBYSxFQUFFLElBQUksQ0FBQ1MsT0FBTyxDQUFDbUgsYUFBYSxDQUFDa0MsSUFBSSxFQUFFNUosU0FBUztRQUNyRyxNQUFNLENBQUMsRUFBYSxZQUFDekIsTUFBTSxDQUFDRCxPQUFPLENBQUM0QyxhQUFhLENBQUMzQyxNQUFNLENBQUNELE9BQU8sQ0FBQ21FLFFBQVEsRUFBRSxJQUFJLEdBQUdvRyxnQkFBZ0IsSUFBSS9JLGFBQWEsQ0FBQ29MLFFBQVEsR0FBR3BMLGFBQWEsQ0FBQ29MLFFBQVEsQ0FBQ2pLLEdBQUcsRUFBRTBCLElBQUksR0FBRyxFQUFhLFlBQUNwRSxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFRLFNBQUUsQ0FBQztnQkFDaE43QixHQUFHLEVBQUVzRCxJQUFJO2dCQUNUckIsR0FBRyxLQUFLYixXQUFXLENBQUMsT0FBTyxFQUFFc0QsU0FBUyxDQUFDcEIsSUFBSSxJQUFJakMsNkJBQTZCO2dCQUM1RVUsS0FBSyxFQUFFLElBQUksQ0FBQ1osS0FBSyxDQUFDWSxLQUFLO2dCQUN2QlIsV0FBVyxFQUFFLElBQUksQ0FBQ0osS0FBSyxDQUFDSSxXQUFXLElBQUlBLFdBQVc7WUFDdEQsQ0FBQztZQUNELElBQUksRUFBRWlJLGdCQUFnQixHQUFHLElBQUksR0FBRyxFQUFhLFlBQUN0SyxNQUFNLENBQUNELE9BQU8sQ0FBQzRDLGFBQWEsQ0FBQyxDQUFRLFNBQUUsQ0FBQztZQUN0RmtKLEVBQUUsRUFBRSxDQUFlO1lBQ25CcEgsSUFBSSxFQUFFLENBQWtCO1lBQ3hCNUIsS0FBSyxFQUFFLElBQUksQ0FBQ1osS0FBSyxDQUFDWSxLQUFLO1lBQ3ZCUixXQUFXLEVBQUUsSUFBSSxDQUFDSixLQUFLLENBQUNJLFdBQVcsSUFBSUEsV0FBVztZQUNsRDBCLHVCQUF1QixFQUFFLENBQUM7Z0JBQ3RCQyxNQUFNLEVBQUVvQyxVQUFVLENBQUMyRixxQkFBcUIsQ0FBQyxJQUFJLENBQUMvSixPQUFPO1lBQ3pELENBQUM7UUFDTCxDQUFDLEdBQUdJLHVCQUF1QixLQUFLa0ksZ0JBQWdCLElBQUksSUFBSSxDQUFDdkksa0JBQWtCLElBQUlLLHVCQUF1QixLQUFLa0ksZ0JBQWdCLElBQUksSUFBSSxDQUFDekYsaUJBQWlCLElBQUl6Qyx1QkFBdUIsS0FBS2tJLGdCQUFnQixJQUFJLElBQUksQ0FBQ3BGLGdCQUFnQixDQUFDQyxLQUFLLEdBQUcvQyx1QkFBdUIsS0FBS2tJLGdCQUFnQixJQUFJLElBQUksQ0FBQzdFLFVBQVUsQ0FBQ04sS0FBSztJQUNoVCxDQUFDOztBQUVMeEYsa0JBQWtCLEdBQUd5RyxVQUFVLENBQUM7QUFDaENBLFVBQVUsQ0FBQzBGLFdBQVcsR0FBR3BMLFlBQVksQ0FBQ2dHLFdBQVcsQ0FBQztBQUNsRE4sVUFBVSxDQUFDd0csaUJBQWlCLEdBQUcsQ0FBMFQsMFRBQUM7U0FDalZoQixVQUFVLENBQUM3QixPQUFPLEVBQUU4QyxNQUFNLEVBQUUsQ0FBQztJQUNsQyxNQUFNLENBQUM5QyxPQUFPLE9BQU84QyxNQUFNLEdBQUdBLE1BQU0sQ0FBQ3ZILFFBQVEsQ0FBQyxDQUFHLE1BQUksQ0FBRyxLQUFHLENBQUcsR0FBQyxLQUFLO0FBQ3hFLENBQUMsQ0FFRCxFQUFxQyIsInNvdXJjZXMiOlsid2VicGFjazovL2tpbmNvLXJlYWN0Ly4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9wYWdlcy9fZG9jdW1lbnQuanM/M2I4YyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuSHRtbCA9IEh0bWw7XG5leHBvcnRzLk1haW4gPSBNYWluO1xuZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwO1xudmFyIF9yZWFjdCA9IF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKHJlcXVpcmUoXCJyZWFjdFwiKSk7XG52YXIgX2NvbnN0YW50cyA9IHJlcXVpcmUoXCIuLi9zaGFyZWQvbGliL2NvbnN0YW50c1wiKTtcbnZhciBfZ2V0UGFnZUZpbGVzID0gcmVxdWlyZShcIi4uL3NlcnZlci9nZXQtcGFnZS1maWxlc1wiKTtcbnZhciBfdXRpbHMgPSByZXF1aXJlKFwiLi4vc2VydmVyL3V0aWxzXCIpO1xudmFyIF9odG1sZXNjYXBlID0gcmVxdWlyZShcIi4uL3NlcnZlci9odG1sZXNjYXBlXCIpO1xudmFyIF9zY3JpcHQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi9jbGllbnQvc2NyaXB0XCIpKTtcbnZhciBfaXNFcnJvciA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uL2xpYi9pcy1lcnJvclwiKSk7XG52YXIgX2h0bWxDb250ZXh0ID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvaHRtbC1jb250ZXh0XCIpO1xuZnVuY3Rpb24gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChvYmopIHtcbiAgICByZXR1cm4gb2JqICYmIG9iai5fX2VzTW9kdWxlID8gb2JqIDoge1xuICAgICAgICBkZWZhdWx0OiBvYmpcbiAgICB9O1xufVxuZnVuY3Rpb24gX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQob2JqKSB7XG4gICAgaWYgKG9iaiAmJiBvYmouX19lc01vZHVsZSkge1xuICAgICAgICByZXR1cm4gb2JqO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIHZhciBuZXdPYmogPSB7fTtcbiAgICAgICAgaWYgKG9iaiAhPSBudWxsKSB7XG4gICAgICAgICAgICBmb3IodmFyIGtleSBpbiBvYmope1xuICAgICAgICAgICAgICAgIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwob2JqLCBrZXkpKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhciBkZXNjID0gT2JqZWN0LmRlZmluZVByb3BlcnR5ICYmIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IgPyBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG9iaiwga2V5KSA6IHt9O1xuICAgICAgICAgICAgICAgICAgICBpZiAoZGVzYy5nZXQgfHwgZGVzYy5zZXQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXdPYmosIGtleSwgZGVzYyk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBuZXdPYmpba2V5XSA9IG9ialtrZXldO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIG5ld09iai5kZWZhdWx0ID0gb2JqO1xuICAgICAgICByZXR1cm4gbmV3T2JqO1xuICAgIH1cbn1cbmZ1bmN0aW9uIGdldERvY3VtZW50RmlsZXMoYnVpbGRNYW5pZmVzdCwgcGF0aG5hbWUsIGluQW1wTW9kZSkge1xuICAgIGNvbnN0IHNoYXJlZEZpbGVzID0gKDAsIF9nZXRQYWdlRmlsZXMpLmdldFBhZ2VGaWxlcyhidWlsZE1hbmlmZXN0LCAnL19hcHAnKTtcbiAgICBjb25zdCBwYWdlRmlsZXMgPSBpbkFtcE1vZGUgPyBbXSA6ICgwLCBfZ2V0UGFnZUZpbGVzKS5nZXRQYWdlRmlsZXMoYnVpbGRNYW5pZmVzdCwgcGF0aG5hbWUpO1xuICAgIHJldHVybiB7XG4gICAgICAgIHNoYXJlZEZpbGVzLFxuICAgICAgICBwYWdlRmlsZXMsXG4gICAgICAgIGFsbEZpbGVzOiBbXG4gICAgICAgICAgICAuLi5uZXcgU2V0KFtcbiAgICAgICAgICAgICAgICAuLi5zaGFyZWRGaWxlcyxcbiAgICAgICAgICAgICAgICAuLi5wYWdlRmlsZXNcbiAgICAgICAgICAgIF0pXG4gICAgICAgIF1cbiAgICB9O1xufVxuZnVuY3Rpb24gZ2V0UG9seWZpbGxTY3JpcHRzKGNvbnRleHQsIHByb3BzKSB7XG4gICAgLy8gcG9seWZpbGxzLmpzIGhhcyB0byBiZSByZW5kZXJlZCBhcyBub21vZHVsZSB3aXRob3V0IGFzeW5jXG4gICAgLy8gSXQgYWxzbyBoYXMgdG8gYmUgdGhlIGZpcnN0IHNjcmlwdCB0byBsb2FkXG4gICAgY29uc3QgeyBhc3NldFByZWZpeCAsIGJ1aWxkTWFuaWZlc3QgLCBkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZyAsIGRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nICwgY3Jvc3NPcmlnaW4gLCAgfSA9IGNvbnRleHQ7XG4gICAgcmV0dXJuIGJ1aWxkTWFuaWZlc3QucG9seWZpbGxGaWxlcy5maWx0ZXIoKHBvbHlmaWxsKT0+cG9seWZpbGwuZW5kc1dpdGgoJy5qcycpICYmICFwb2x5ZmlsbC5lbmRzV2l0aCgnLm1vZHVsZS5qcycpXG4gICAgKS5tYXAoKHBvbHlmaWxsKT0+LyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIsIHtcbiAgICAgICAgICAgIGtleTogcG9seWZpbGwsXG4gICAgICAgICAgICBkZWZlcjogIWRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nLFxuICAgICAgICAgICAgbm9uY2U6IHByb3BzLm5vbmNlLFxuICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luLFxuICAgICAgICAgICAgbm9Nb2R1bGU6IHRydWUsXG4gICAgICAgICAgICBzcmM6IGAke2Fzc2V0UHJlZml4fS9fbmV4dC8ke3BvbHlmaWxsfSR7ZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmd9YFxuICAgICAgICB9KVxuICAgICk7XG59XG5mdW5jdGlvbiBoYXNDb21wb25lbnRQcm9wcyhjaGlsZCkge1xuICAgIHJldHVybiAhIWNoaWxkICYmICEhY2hpbGQucHJvcHM7XG59XG5mdW5jdGlvbiBnZXRQcmVOZXh0V29ya2VyU2NyaXB0cyhjb250ZXh0LCBwcm9wcykge1xuICAgIGNvbnN0IHsgYXNzZXRQcmVmaXggLCBzY3JpcHRMb2FkZXIgLCBjcm9zc09yaWdpbiAsIG5leHRTY3JpcHRXb3JrZXJzICB9ID0gY29udGV4dDtcbiAgICAvLyBkaXNhYmxlIGBuZXh0U2NyaXB0V29ya2Vyc2AgaW4gZWRnZSBydW50aW1lXG4gICAgaWYgKCFuZXh0U2NyaXB0V29ya2VycyB8fCBwcm9jZXNzLmJyb3dzZXIpIHJldHVybiBudWxsO1xuICAgIHRyeSB7XG4gICAgICAgIGxldCB7IHBhcnR5dG93blNuaXBwZXQgLCAgfSA9IHJlcXVpcmUoLyogd2VicGFja0lnbm9yZTogdHJ1ZSAqLyAnQGJ1aWxkZXIuaW8vcGFydHl0b3duL2ludGVncmF0aW9uJyk7XG4gICAgICAgIGNvbnN0IGNoaWxkcmVuID0gQXJyYXkuaXNBcnJheShwcm9wcy5jaGlsZHJlbikgPyBwcm9wcy5jaGlsZHJlbiA6IFtcbiAgICAgICAgICAgIHByb3BzLmNoaWxkcmVuXG4gICAgICAgIF07XG4gICAgICAgIC8vIENoZWNrIHRvIHNlZSBpZiB0aGUgdXNlciBoYXMgZGVmaW5lZCB0aGVpciBvd24gUGFydHl0b3duIGNvbmZpZ3VyYXRpb25cbiAgICAgICAgY29uc3QgdXNlckRlZmluZWRDb25maWcgPSBjaGlsZHJlbi5maW5kKChjaGlsZCk9PntcbiAgICAgICAgICAgIHZhciByZWYsIHJlZjE7XG4gICAgICAgICAgICByZXR1cm4gaGFzQ29tcG9uZW50UHJvcHMoY2hpbGQpICYmIChjaGlsZCA9PT0gbnVsbCB8fCBjaGlsZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogKHJlZiA9IGNoaWxkLnByb3BzKSA9PT0gbnVsbCB8fCByZWYgPT09IHZvaWQgMCA/IHZvaWQgMCA6IChyZWYxID0gcmVmLmRhbmdlcm91c2x5U2V0SW5uZXJIVE1MKSA9PT0gbnVsbCB8fCByZWYxID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZWYxLl9faHRtbC5sZW5ndGgpICYmICdkYXRhLXBhcnR5dG93bi1jb25maWcnIGluIGNoaWxkLnByb3BzO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuKC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcmVhY3QuZGVmYXVsdC5GcmFnbWVudCwgbnVsbCwgIXVzZXJEZWZpbmVkQ29uZmlnICYmIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCB7XG4gICAgICAgICAgICBcImRhdGEtcGFydHl0b3duLWNvbmZpZ1wiOiBcIlwiLFxuICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgICAgICBfX2h0bWw6IGBcbiAgICAgICAgICAgIHBhcnR5dG93biA9IHtcbiAgICAgICAgICAgICAgbGliOiBcIiR7YXNzZXRQcmVmaXh9L19uZXh0L3N0YXRpYy9+cGFydHl0b3duL1wiXG4gICAgICAgICAgICB9O1xuICAgICAgICAgIGBcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSksIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCB7XG4gICAgICAgICAgICBcImRhdGEtcGFydHl0b3duXCI6IFwiXCIsXG4gICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTDoge1xuICAgICAgICAgICAgICAgIF9faHRtbDogcGFydHl0b3duU25pcHBldCgpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pLCAoc2NyaXB0TG9hZGVyLndvcmtlciB8fCBbXSkubWFwKChmaWxlLCBpbmRleCk9PntcbiAgICAgICAgICAgIGNvbnN0IHsgc3RyYXRlZ3kgLCAuLi5zY3JpcHRQcm9wcyB9ID0gZmlsZTtcbiAgICAgICAgICAgIHJldHVybigvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJzY3JpcHRcIiwgT2JqZWN0LmFzc2lnbih7fSwgc2NyaXB0UHJvcHMsIHtcbiAgICAgICAgICAgICAgICB0eXBlOiBcInRleHQvcGFydHl0b3duXCIsXG4gICAgICAgICAgICAgICAga2V5OiBzY3JpcHRQcm9wcy5zcmMgfHwgaW5kZXgsXG4gICAgICAgICAgICAgICAgbm9uY2U6IHByb3BzLm5vbmNlLFxuICAgICAgICAgICAgICAgIFwiZGF0YS1uc2NyaXB0XCI6IFwid29ya2VyXCIsXG4gICAgICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luXG4gICAgICAgICAgICB9KSkpO1xuICAgICAgICB9KSkpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICBjb25zb2xlLndhcm4oYFdhcm5pbmc6IFBhcnR5dG93biBjb3VsZCBub3QgYmUgaW5zdGFudGlhdGVkIGluIHlvdXIgYXBwbGljYXRpb24gZHVlIHRvIGFuIGVycm9yLiAke2Vycn1gKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxufVxuZnVuY3Rpb24gZ2V0UHJlTmV4dFNjcmlwdHMoY29udGV4dCwgcHJvcHMpIHtcbiAgICBjb25zdCB7IHNjcmlwdExvYWRlciAsIGRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nICwgY3Jvc3NPcmlnaW4gIH0gPSBjb250ZXh0O1xuICAgIGNvbnN0IHdlYldvcmtlclNjcmlwdHMgPSBnZXRQcmVOZXh0V29ya2VyU2NyaXB0cyhjb250ZXh0LCBwcm9wcyk7XG4gICAgY29uc3QgYmVmb3JlSW50ZXJhY3RpdmVTY3JpcHRzID0gKHNjcmlwdExvYWRlci5iZWZvcmVJbnRlcmFjdGl2ZSB8fCBbXSkubWFwKChmaWxlLCBpbmRleCk9PntcbiAgICAgICAgY29uc3QgeyBzdHJhdGVneSAsIC4uLnNjcmlwdFByb3BzIH0gPSBmaWxlO1xuICAgICAgICB2YXIgX2RlZmVyO1xuICAgICAgICByZXR1cm4oLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIsIE9iamVjdC5hc3NpZ24oe30sIHNjcmlwdFByb3BzLCB7XG4gICAgICAgICAgICBrZXk6IHNjcmlwdFByb3BzLnNyYyB8fCBpbmRleCxcbiAgICAgICAgICAgIGRlZmVyOiAoX2RlZmVyID0gc2NyaXB0UHJvcHMuZGVmZXIpICE9PSBudWxsICYmIF9kZWZlciAhPT0gdm9pZCAwID8gX2RlZmVyIDogIWRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nLFxuICAgICAgICAgICAgbm9uY2U6IHByb3BzLm5vbmNlLFxuICAgICAgICAgICAgXCJkYXRhLW5zY3JpcHRcIjogXCJiZWZvcmVJbnRlcmFjdGl2ZVwiLFxuICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luXG4gICAgICAgIH0pKSk7XG4gICAgfSk7XG4gICAgcmV0dXJuKC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcmVhY3QuZGVmYXVsdC5GcmFnbWVudCwgbnVsbCwgd2ViV29ya2VyU2NyaXB0cywgYmVmb3JlSW50ZXJhY3RpdmVTY3JpcHRzKSk7XG59XG5mdW5jdGlvbiBnZXREeW5hbWljQ2h1bmtzKGNvbnRleHQsIHByb3BzLCBmaWxlcykge1xuICAgIGNvbnN0IHsgZHluYW1pY0ltcG9ydHMgLCBhc3NldFByZWZpeCAsIGlzRGV2ZWxvcG1lbnQgLCBkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZyAsIGRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nICwgY3Jvc3NPcmlnaW4gLCAgfSA9IGNvbnRleHQ7XG4gICAgcmV0dXJuIGR5bmFtaWNJbXBvcnRzLm1hcCgoZmlsZSk9PntcbiAgICAgICAgaWYgKCFmaWxlLmVuZHNXaXRoKCcuanMnKSB8fCBmaWxlcy5hbGxGaWxlcy5pbmNsdWRlcyhmaWxlKSkgcmV0dXJuIG51bGw7XG4gICAgICAgIHJldHVybigvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJzY3JpcHRcIiwge1xuICAgICAgICAgICAgYXN5bmM6ICFpc0RldmVsb3BtZW50ICYmIGRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nLFxuICAgICAgICAgICAgZGVmZXI6ICFkaXNhYmxlT3B0aW1pemVkTG9hZGluZyxcbiAgICAgICAgICAgIGtleTogZmlsZSxcbiAgICAgICAgICAgIHNyYzogYCR7YXNzZXRQcmVmaXh9L19uZXh0LyR7ZW5jb2RlVVJJKGZpbGUpfSR7ZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmd9YCxcbiAgICAgICAgICAgIG5vbmNlOiBwcm9wcy5ub25jZSxcbiAgICAgICAgICAgIGNyb3NzT3JpZ2luOiBwcm9wcy5jcm9zc09yaWdpbiB8fCBjcm9zc09yaWdpblxuICAgICAgICB9KSk7XG4gICAgfSk7XG59XG5mdW5jdGlvbiBnZXRTY3JpcHRzKGNvbnRleHQsIHByb3BzLCBmaWxlcykge1xuICAgIHZhciByZWY7XG4gICAgY29uc3QgeyBhc3NldFByZWZpeCAsIGJ1aWxkTWFuaWZlc3QgLCBpc0RldmVsb3BtZW50ICwgZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmcgLCBkaXNhYmxlT3B0aW1pemVkTG9hZGluZyAsIGNyb3NzT3JpZ2luICwgIH0gPSBjb250ZXh0O1xuICAgIGNvbnN0IG5vcm1hbFNjcmlwdHMgPSBmaWxlcy5hbGxGaWxlcy5maWx0ZXIoKGZpbGUpPT5maWxlLmVuZHNXaXRoKCcuanMnKVxuICAgICk7XG4gICAgY29uc3QgbG93UHJpb3JpdHlTY3JpcHRzID0gKHJlZiA9IGJ1aWxkTWFuaWZlc3QubG93UHJpb3JpdHlGaWxlcykgPT09IG51bGwgfHwgcmVmID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZWYuZmlsdGVyKChmaWxlKT0+ZmlsZS5lbmRzV2l0aCgnLmpzJylcbiAgICApO1xuICAgIHJldHVybiBbXG4gICAgICAgIC4uLm5vcm1hbFNjcmlwdHMsXG4gICAgICAgIC4uLmxvd1ByaW9yaXR5U2NyaXB0c1xuICAgIF0ubWFwKChmaWxlKT0+e1xuICAgICAgICByZXR1cm4oLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIsIHtcbiAgICAgICAgICAgIGtleTogZmlsZSxcbiAgICAgICAgICAgIHNyYzogYCR7YXNzZXRQcmVmaXh9L19uZXh0LyR7ZW5jb2RlVVJJKGZpbGUpfSR7ZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmd9YCxcbiAgICAgICAgICAgIG5vbmNlOiBwcm9wcy5ub25jZSxcbiAgICAgICAgICAgIGFzeW5jOiAhaXNEZXZlbG9wbWVudCAmJiBkaXNhYmxlT3B0aW1pemVkTG9hZGluZyxcbiAgICAgICAgICAgIGRlZmVyOiAhZGlzYWJsZU9wdGltaXplZExvYWRpbmcsXG4gICAgICAgICAgICBjcm9zc09yaWdpbjogcHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW5cbiAgICAgICAgfSkpO1xuICAgIH0pO1xufVxuY2xhc3MgRG9jdW1lbnQgZXh0ZW5kcyBfcmVhY3QuQ29tcG9uZW50IHtcbiAgICAvKipcbiAgICogYGdldEluaXRpYWxQcm9wc2AgaG9vayByZXR1cm5zIHRoZSBjb250ZXh0IG9iamVjdCB3aXRoIHRoZSBhZGRpdGlvbiBvZiBgcmVuZGVyUGFnZWAuXG4gICAqIGByZW5kZXJQYWdlYCBjYWxsYmFjayBleGVjdXRlcyBgUmVhY3RgIHJlbmRlcmluZyBsb2dpYyBzeW5jaHJvbm91c2x5IHRvIHN1cHBvcnQgc2VydmVyLXJlbmRlcmluZyB3cmFwcGVyc1xuICAgKi8gc3RhdGljIGdldEluaXRpYWxQcm9wcyhjdHgpIHtcbiAgICAgICAgcmV0dXJuIGN0eC5kZWZhdWx0R2V0SW5pdGlhbFByb3BzKGN0eCk7XG4gICAgfVxuICAgIHJlbmRlcigpIHtcbiAgICAgICAgcmV0dXJuKC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChIdG1sLCBudWxsLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoSGVhZCwgbnVsbCksIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImJvZHlcIiwgbnVsbCwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KE1haW4sIG51bGwpLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoTmV4dFNjcmlwdCwgbnVsbCkpKSk7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gRG9jdW1lbnQ7XG5Eb2N1bWVudC5fX25leHRfaW50ZXJuYWxfZG9jdW1lbnQgPSBmdW5jdGlvbiBJbnRlcm5hbEZ1bmN0aW9uRG9jdW1lbnQoKSB7XG4gICAgcmV0dXJuKC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChIdG1sLCBudWxsLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoSGVhZCwgbnVsbCksIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImJvZHlcIiwgbnVsbCwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KE1haW4sIG51bGwpLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoTmV4dFNjcmlwdCwgbnVsbCkpKSk7XG59O1xuZnVuY3Rpb24gSHRtbChwcm9wcykge1xuICAgIGNvbnN0IHsgaW5BbXBNb2RlICwgZG9jQ29tcG9uZW50c1JlbmRlcmVkICwgbG9jYWxlICB9ID0gKDAsIF9yZWFjdCkudXNlQ29udGV4dChfaHRtbENvbnRleHQuSHRtbENvbnRleHQpO1xuICAgIGRvY0NvbXBvbmVudHNSZW5kZXJlZC5IdG1sID0gdHJ1ZTtcbiAgICByZXR1cm4oLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwiaHRtbFwiLCBPYmplY3QuYXNzaWduKHt9LCBwcm9wcywge1xuICAgICAgICBsYW5nOiBwcm9wcy5sYW5nIHx8IGxvY2FsZSB8fCB1bmRlZmluZWQsXG4gICAgICAgIGFtcDogaW5BbXBNb2RlID8gJycgOiB1bmRlZmluZWQsXG4gICAgICAgIFwiZGF0YS1hbXBkZXZtb2RlXCI6IGluQW1wTW9kZSAmJiBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nID8gJycgOiB1bmRlZmluZWRcbiAgICB9KSkpO1xufVxuZnVuY3Rpb24gQW1wU3R5bGVzKHsgc3R5bGVzICB9KSB7XG4gICAgaWYgKCFzdHlsZXMpIHJldHVybiBudWxsO1xuICAgIC8vIHRyeSB0byBwYXJzZSBzdHlsZXMgZnJvbSBmcmFnbWVudCBmb3IgYmFja3dhcmRzIGNvbXBhdFxuICAgIGNvbnN0IGN1clN0eWxlcyA9IEFycmF5LmlzQXJyYXkoc3R5bGVzKSA/IHN0eWxlcyA6IFtdO1xuICAgIGlmICgvLyBAdHMtaWdub3JlIFByb3BlcnR5ICdwcm9wcycgZG9lcyBub3QgZXhpc3Qgb24gdHlwZSBSZWFjdEVsZW1lbnRcbiAgICBzdHlsZXMucHJvcHMgJiYgLy8gQHRzLWlnbm9yZSBQcm9wZXJ0eSAncHJvcHMnIGRvZXMgbm90IGV4aXN0IG9uIHR5cGUgUmVhY3RFbGVtZW50XG4gICAgQXJyYXkuaXNBcnJheShzdHlsZXMucHJvcHMuY2hpbGRyZW4pKSB7XG4gICAgICAgIGNvbnN0IGhhc1N0eWxlcyA9IChlbCk9PntcbiAgICAgICAgICAgIHZhciByZWYsIHJlZjI7XG4gICAgICAgICAgICByZXR1cm4gZWwgPT09IG51bGwgfHwgZWwgPT09IHZvaWQgMCA/IHZvaWQgMCA6IChyZWYgPSBlbC5wcm9wcykgPT09IG51bGwgfHwgcmVmID09PSB2b2lkIDAgPyB2b2lkIDAgOiAocmVmMiA9IHJlZi5kYW5nZXJvdXNseVNldElubmVySFRNTCkgPT09IG51bGwgfHwgcmVmMiA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmMi5fX2h0bWw7XG4gICAgICAgIH07XG4gICAgICAgIC8vIEB0cy1pZ25vcmUgUHJvcGVydHkgJ3Byb3BzJyBkb2VzIG5vdCBleGlzdCBvbiB0eXBlIFJlYWN0RWxlbWVudFxuICAgICAgICBzdHlsZXMucHJvcHMuY2hpbGRyZW4uZm9yRWFjaCgoY2hpbGQpPT57XG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShjaGlsZCkpIHtcbiAgICAgICAgICAgICAgICBjaGlsZC5mb3JFYWNoKChlbCk9Pmhhc1N0eWxlcyhlbCkgJiYgY3VyU3R5bGVzLnB1c2goZWwpXG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoaGFzU3R5bGVzKGNoaWxkKSkge1xuICAgICAgICAgICAgICAgIGN1clN0eWxlcy5wdXNoKGNoaWxkKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIC8qIEFkZCBjdXN0b20gc3R5bGVzIGJlZm9yZSBBTVAgc3R5bGVzIHRvIHByZXZlbnQgYWNjaWRlbnRhbCBvdmVycmlkZXMgKi8gcmV0dXJuKC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInN0eWxlXCIsIHtcbiAgICAgICAgXCJhbXAtY3VzdG9tXCI6IFwiXCIsXG4gICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MOiB7XG4gICAgICAgICAgICBfX2h0bWw6IGN1clN0eWxlcy5tYXAoKHN0eWxlKT0+c3R5bGUucHJvcHMuZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwuX19odG1sXG4gICAgICAgICAgICApLmpvaW4oJycpLnJlcGxhY2UoL1xcL1xcKiMgc291cmNlTWFwcGluZ1VSTD0uKlxcKlxcLy9nLCAnJykucmVwbGFjZSgvXFwvXFwqQCBzb3VyY2VVUkw9Lio/XFwqXFwvL2csICcnKVxuICAgICAgICB9XG4gICAgfSkpO1xufVxuY2xhc3MgSGVhZCBleHRlbmRzIF9yZWFjdC5Db21wb25lbnQge1xuICAgIGdldENzc0xpbmtzKGZpbGVzKSB7XG4gICAgICAgIGNvbnN0IHsgYXNzZXRQcmVmaXggLCBkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZyAsIGR5bmFtaWNJbXBvcnRzICwgY3Jvc3NPcmlnaW4gLCBvcHRpbWl6ZUNzcyAsIG9wdGltaXplRm9udHMgLCAgfSA9IHRoaXMuY29udGV4dDtcbiAgICAgICAgY29uc3QgY3NzRmlsZXMgPSBmaWxlcy5hbGxGaWxlcy5maWx0ZXIoKGYpPT5mLmVuZHNXaXRoKCcuY3NzJylcbiAgICAgICAgKTtcbiAgICAgICAgY29uc3Qgc2hhcmVkRmlsZXMgPSBuZXcgU2V0KGZpbGVzLnNoYXJlZEZpbGVzKTtcbiAgICAgICAgLy8gVW5tYW5hZ2VkIGZpbGVzIGFyZSBDU1MgZmlsZXMgdGhhdCB3aWxsIGJlIGhhbmRsZWQgZGlyZWN0bHkgYnkgdGhlXG4gICAgICAgIC8vIHdlYnBhY2sgcnVudGltZSAoYG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luYCkuXG4gICAgICAgIGxldCB1bm1hbmdlZEZpbGVzID0gbmV3IFNldChbXSk7XG4gICAgICAgIGxldCBkeW5hbWljQ3NzRmlsZXMgPSBBcnJheS5mcm9tKG5ldyBTZXQoZHluYW1pY0ltcG9ydHMuZmlsdGVyKChmaWxlKT0+ZmlsZS5lbmRzV2l0aCgnLmNzcycpXG4gICAgICAgICkpKTtcbiAgICAgICAgaWYgKGR5bmFtaWNDc3NGaWxlcy5sZW5ndGgpIHtcbiAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nID0gbmV3IFNldChjc3NGaWxlcyk7XG4gICAgICAgICAgICBkeW5hbWljQ3NzRmlsZXMgPSBkeW5hbWljQ3NzRmlsZXMuZmlsdGVyKChmKT0+IShleGlzdGluZy5oYXMoZikgfHwgc2hhcmVkRmlsZXMuaGFzKGYpKVxuICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIHVubWFuZ2VkRmlsZXMgPSBuZXcgU2V0KGR5bmFtaWNDc3NGaWxlcyk7XG4gICAgICAgICAgICBjc3NGaWxlcy5wdXNoKC4uLmR5bmFtaWNDc3NGaWxlcyk7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IGNzc0xpbmtFbGVtZW50cyA9IFtdO1xuICAgICAgICBjc3NGaWxlcy5mb3JFYWNoKChmaWxlKT0+e1xuICAgICAgICAgICAgY29uc3QgaXNTaGFyZWRGaWxlID0gc2hhcmVkRmlsZXMuaGFzKGZpbGUpO1xuICAgICAgICAgICAgaWYgKCFvcHRpbWl6ZUNzcykge1xuICAgICAgICAgICAgICAgIGNzc0xpbmtFbGVtZW50cy5wdXNoKC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImxpbmtcIiwge1xuICAgICAgICAgICAgICAgICAgICBrZXk6IGAke2ZpbGV9LXByZWxvYWRgLFxuICAgICAgICAgICAgICAgICAgICBub25jZTogdGhpcy5wcm9wcy5ub25jZSxcbiAgICAgICAgICAgICAgICAgICAgcmVsOiBcInByZWxvYWRcIixcbiAgICAgICAgICAgICAgICAgICAgaHJlZjogYCR7YXNzZXRQcmVmaXh9L19uZXh0LyR7ZW5jb2RlVVJJKGZpbGUpfSR7ZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmd9YCxcbiAgICAgICAgICAgICAgICAgICAgYXM6IFwic3R5bGVcIixcbiAgICAgICAgICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW5cbiAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBpc1VubWFuYWdlZEZpbGUgPSB1bm1hbmdlZEZpbGVzLmhhcyhmaWxlKTtcbiAgICAgICAgICAgIGNzc0xpbmtFbGVtZW50cy5wdXNoKC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImxpbmtcIiwge1xuICAgICAgICAgICAgICAgIGtleTogZmlsZSxcbiAgICAgICAgICAgICAgICBub25jZTogdGhpcy5wcm9wcy5ub25jZSxcbiAgICAgICAgICAgICAgICByZWw6IFwic3R5bGVzaGVldFwiLFxuICAgICAgICAgICAgICAgIGhyZWY6IGAke2Fzc2V0UHJlZml4fS9fbmV4dC8ke2VuY29kZVVSSShmaWxlKX0ke2Rldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nfWAsXG4gICAgICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW4sXG4gICAgICAgICAgICAgICAgXCJkYXRhLW4tZ1wiOiBpc1VubWFuYWdlZEZpbGUgPyB1bmRlZmluZWQgOiBpc1NoYXJlZEZpbGUgPyAnJyA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgICAgICBcImRhdGEtbi1wXCI6IGlzVW5tYW5hZ2VkRmlsZSA/IHVuZGVmaW5lZCA6IGlzU2hhcmVkRmlsZSA/IHVuZGVmaW5lZCA6ICcnXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgIH0pO1xuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCcgJiYgb3B0aW1pemVGb250cykge1xuICAgICAgICAgICAgY3NzTGlua0VsZW1lbnRzID0gdGhpcy5tYWtlU3R5bGVzaGVldEluZXJ0KGNzc0xpbmtFbGVtZW50cyk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNzc0xpbmtFbGVtZW50cy5sZW5ndGggPT09IDAgPyBudWxsIDogY3NzTGlua0VsZW1lbnRzO1xuICAgIH1cbiAgICBnZXRQcmVsb2FkRHluYW1pY0NodW5rcygpIHtcbiAgICAgICAgY29uc3QgeyBkeW5hbWljSW1wb3J0cyAsIGFzc2V0UHJlZml4ICwgZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmcgLCBjcm9zc09yaWdpbiAsICB9ID0gdGhpcy5jb250ZXh0O1xuICAgICAgICByZXR1cm4gZHluYW1pY0ltcG9ydHMubWFwKChmaWxlKT0+e1xuICAgICAgICAgICAgaWYgKCFmaWxlLmVuZHNXaXRoKCcuanMnKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuKC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImxpbmtcIiwge1xuICAgICAgICAgICAgICAgIHJlbDogXCJwcmVsb2FkXCIsXG4gICAgICAgICAgICAgICAga2V5OiBmaWxlLFxuICAgICAgICAgICAgICAgIGhyZWY6IGAke2Fzc2V0UHJlZml4fS9fbmV4dC8ke2VuY29kZVVSSShmaWxlKX0ke2Rldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nfWAsXG4gICAgICAgICAgICAgICAgYXM6IFwic2NyaXB0XCIsXG4gICAgICAgICAgICAgICAgbm9uY2U6IHRoaXMucHJvcHMubm9uY2UsXG4gICAgICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW5cbiAgICAgICAgICAgIH0pKTtcbiAgICAgICAgfSkvLyBGaWx0ZXIgb3V0IG51bGxlZCBzY3JpcHRzXG4gICAgICAgIC5maWx0ZXIoQm9vbGVhbik7XG4gICAgfVxuICAgIGdldFByZWxvYWRNYWluTGlua3MoZmlsZXMpIHtcbiAgICAgICAgY29uc3QgeyBhc3NldFByZWZpeCAsIGRldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nICwgc2NyaXB0TG9hZGVyICwgY3Jvc3NPcmlnaW4gLCAgfSA9IHRoaXMuY29udGV4dDtcbiAgICAgICAgY29uc3QgcHJlbG9hZEZpbGVzID0gZmlsZXMuYWxsRmlsZXMuZmlsdGVyKChmaWxlKT0+e1xuICAgICAgICAgICAgcmV0dXJuIGZpbGUuZW5kc1dpdGgoJy5qcycpO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIC4uLihzY3JpcHRMb2FkZXIuYmVmb3JlSW50ZXJhY3RpdmUgfHwgW10pLm1hcCgoZmlsZSk9Pi8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImxpbmtcIiwge1xuICAgICAgICAgICAgICAgICAgICBrZXk6IGZpbGUuc3JjLFxuICAgICAgICAgICAgICAgICAgICBub25jZTogdGhpcy5wcm9wcy5ub25jZSxcbiAgICAgICAgICAgICAgICAgICAgcmVsOiBcInByZWxvYWRcIixcbiAgICAgICAgICAgICAgICAgICAgaHJlZjogZmlsZS5zcmMsXG4gICAgICAgICAgICAgICAgICAgIGFzOiBcInNjcmlwdFwiLFxuICAgICAgICAgICAgICAgICAgICBjcm9zc09yaWdpbjogdGhpcy5wcm9wcy5jcm9zc09yaWdpbiB8fCBjcm9zc09yaWdpblxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICApLFxuICAgICAgICAgICAgLi4ucHJlbG9hZEZpbGVzLm1hcCgoZmlsZSk9Pi8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImxpbmtcIiwge1xuICAgICAgICAgICAgICAgICAgICBrZXk6IGZpbGUsXG4gICAgICAgICAgICAgICAgICAgIG5vbmNlOiB0aGlzLnByb3BzLm5vbmNlLFxuICAgICAgICAgICAgICAgICAgICByZWw6IFwicHJlbG9hZFwiLFxuICAgICAgICAgICAgICAgICAgICBocmVmOiBgJHthc3NldFByZWZpeH0vX25leHQvJHtlbmNvZGVVUkkoZmlsZSl9JHtkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZ31gLFxuICAgICAgICAgICAgICAgICAgICBhczogXCJzY3JpcHRcIixcbiAgICAgICAgICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW5cbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgKSwgXG4gICAgICAgIF07XG4gICAgfVxuICAgIGdldER5bmFtaWNDaHVua3MoZmlsZXMpIHtcbiAgICAgICAgcmV0dXJuIGdldER5bmFtaWNDaHVua3ModGhpcy5jb250ZXh0LCB0aGlzLnByb3BzLCBmaWxlcyk7XG4gICAgfVxuICAgIGdldFByZU5leHRTY3JpcHRzKCkge1xuICAgICAgICByZXR1cm4gZ2V0UHJlTmV4dFNjcmlwdHModGhpcy5jb250ZXh0LCB0aGlzLnByb3BzKTtcbiAgICB9XG4gICAgZ2V0U2NyaXB0cyhmaWxlcykge1xuICAgICAgICByZXR1cm4gZ2V0U2NyaXB0cyh0aGlzLmNvbnRleHQsIHRoaXMucHJvcHMsIGZpbGVzKTtcbiAgICB9XG4gICAgZ2V0UG9seWZpbGxTY3JpcHRzKCkge1xuICAgICAgICByZXR1cm4gZ2V0UG9seWZpbGxTY3JpcHRzKHRoaXMuY29udGV4dCwgdGhpcy5wcm9wcyk7XG4gICAgfVxuICAgIGhhbmRsZURvY3VtZW50U2NyaXB0TG9hZGVySXRlbXMoY2hpbGRyZW4pIHtcbiAgICAgICAgY29uc3QgeyBzY3JpcHRMb2FkZXIgIH0gPSB0aGlzLmNvbnRleHQ7XG4gICAgICAgIGNvbnN0IHNjcmlwdExvYWRlckl0ZW1zID0gW107XG4gICAgICAgIGNvbnN0IGZpbHRlcmVkQ2hpbGRyZW4gPSBbXTtcbiAgICAgICAgX3JlYWN0LmRlZmF1bHQuQ2hpbGRyZW4uZm9yRWFjaChjaGlsZHJlbiwgKGNoaWxkKT0+e1xuICAgICAgICAgICAgaWYgKGNoaWxkLnR5cGUgPT09IF9zY3JpcHQuZGVmYXVsdCkge1xuICAgICAgICAgICAgICAgIGlmIChjaGlsZC5wcm9wcy5zdHJhdGVneSA9PT0gJ2JlZm9yZUludGVyYWN0aXZlJykge1xuICAgICAgICAgICAgICAgICAgICBzY3JpcHRMb2FkZXIuYmVmb3JlSW50ZXJhY3RpdmUgPSAoc2NyaXB0TG9hZGVyLmJlZm9yZUludGVyYWN0aXZlIHx8IFtdKS5jb25jYXQoW1xuICAgICAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmNoaWxkLnByb3BzXG4gICAgICAgICAgICAgICAgICAgICAgICB9LCBcbiAgICAgICAgICAgICAgICAgICAgXSk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKFtcbiAgICAgICAgICAgICAgICAgICAgJ2xhenlPbmxvYWQnLFxuICAgICAgICAgICAgICAgICAgICAnYWZ0ZXJJbnRlcmFjdGl2ZScsXG4gICAgICAgICAgICAgICAgICAgICd3b3JrZXInXG4gICAgICAgICAgICAgICAgXS5pbmNsdWRlcyhjaGlsZC5wcm9wcy5zdHJhdGVneSkpIHtcbiAgICAgICAgICAgICAgICAgICAgc2NyaXB0TG9hZGVySXRlbXMucHVzaChjaGlsZC5wcm9wcyk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmaWx0ZXJlZENoaWxkcmVuLnB1c2goY2hpbGQpO1xuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5jb250ZXh0Ll9fTkVYVF9EQVRBX18uc2NyaXB0TG9hZGVyID0gc2NyaXB0TG9hZGVySXRlbXM7XG4gICAgICAgIHJldHVybiBmaWx0ZXJlZENoaWxkcmVuO1xuICAgIH1cbiAgICBtYWtlU3R5bGVzaGVldEluZXJ0KG5vZGUpIHtcbiAgICAgICAgcmV0dXJuIF9yZWFjdC5kZWZhdWx0LkNoaWxkcmVuLm1hcChub2RlLCAoYyk9PntcbiAgICAgICAgICAgIHZhciByZWY1LCByZWYzO1xuICAgICAgICAgICAgaWYgKChjID09PSBudWxsIHx8IGMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGMudHlwZSkgPT09ICdsaW5rJyAmJiAoYyA9PT0gbnVsbCB8fCBjID09PSB2b2lkIDAgPyB2b2lkIDAgOiAocmVmNSA9IGMucHJvcHMpID09PSBudWxsIHx8IHJlZjUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHJlZjUuaHJlZikgJiYgX2NvbnN0YW50cy5PUFRJTUlaRURfRk9OVF9QUk9WSURFUlMuc29tZSgoeyB1cmwgIH0pPT57XG4gICAgICAgICAgICAgICAgdmFyIHJlZiwgcmVmNDtcbiAgICAgICAgICAgICAgICByZXR1cm4gYyA9PT0gbnVsbCB8fCBjID09PSB2b2lkIDAgPyB2b2lkIDAgOiAocmVmID0gYy5wcm9wcykgPT09IG51bGwgfHwgcmVmID09PSB2b2lkIDAgPyB2b2lkIDAgOiAocmVmNCA9IHJlZi5ocmVmKSA9PT0gbnVsbCB8fCByZWY0ID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZWY0LnN0YXJ0c1dpdGgodXJsKTtcbiAgICAgICAgICAgIH0pKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbmV3UHJvcHMgPSB7XG4gICAgICAgICAgICAgICAgICAgIC4uLmMucHJvcHMgfHwge30sXG4gICAgICAgICAgICAgICAgICAgICdkYXRhLWhyZWYnOiBjLnByb3BzLmhyZWYsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgcmV0dXJuKC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY2xvbmVFbGVtZW50KGMsIG5ld1Byb3BzKSk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKGMgPT09IG51bGwgfHwgYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogKHJlZjMgPSBjLnByb3BzKSA9PT0gbnVsbCB8fCByZWYzID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZWYzLmNoaWxkcmVuKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbmV3UHJvcHMgPSB7XG4gICAgICAgICAgICAgICAgICAgIC4uLmMucHJvcHMgfHwge30sXG4gICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiB0aGlzLm1ha2VTdHlsZXNoZWV0SW5lcnQoYy5wcm9wcy5jaGlsZHJlbilcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIHJldHVybigvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNsb25lRWxlbWVudChjLCBuZXdQcm9wcykpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGM7XG4gICAgICAgIH0pLmZpbHRlcihCb29sZWFuKTtcbiAgICB9XG4gICAgcmVuZGVyKCkge1xuICAgICAgICBjb25zdCB7IHN0eWxlcyAsIGFtcFBhdGggLCBpbkFtcE1vZGUgLCBoeWJyaWRBbXAgLCBjYW5vbmljYWxCYXNlICwgX19ORVhUX0RBVEFfXyAsIGRhbmdlcm91c0FzUGF0aCAsIGhlYWRUYWdzICwgdW5zdGFibGVfcnVudGltZUpTICwgdW5zdGFibGVfSnNQcmVsb2FkICwgZGlzYWJsZU9wdGltaXplZExvYWRpbmcgLCBvcHRpbWl6ZUNzcyAsIG9wdGltaXplRm9udHMgLCAgfSA9IHRoaXMuY29udGV4dDtcbiAgICAgICAgY29uc3QgZGlzYWJsZVJ1bnRpbWVKUyA9IHVuc3RhYmxlX3J1bnRpbWVKUyA9PT0gZmFsc2U7XG4gICAgICAgIGNvbnN0IGRpc2FibGVKc1ByZWxvYWQgPSB1bnN0YWJsZV9Kc1ByZWxvYWQgPT09IGZhbHNlIHx8ICFkaXNhYmxlT3B0aW1pemVkTG9hZGluZztcbiAgICAgICAgdGhpcy5jb250ZXh0LmRvY0NvbXBvbmVudHNSZW5kZXJlZC5IZWFkID0gdHJ1ZTtcbiAgICAgICAgbGV0IHsgaGVhZCAgfSA9IHRoaXMuY29udGV4dDtcbiAgICAgICAgbGV0IGNzc1ByZWxvYWRzID0gW107XG4gICAgICAgIGxldCBvdGhlckhlYWRFbGVtZW50cyA9IFtdO1xuICAgICAgICBpZiAoaGVhZCkge1xuICAgICAgICAgICAgaGVhZC5mb3JFYWNoKChjKT0+e1xuICAgICAgICAgICAgICAgIGlmIChjICYmIGMudHlwZSA9PT0gJ2xpbmsnICYmIGMucHJvcHNbJ3JlbCddID09PSAncHJlbG9hZCcgJiYgYy5wcm9wc1snYXMnXSA9PT0gJ3N0eWxlJykge1xuICAgICAgICAgICAgICAgICAgICBjc3NQcmVsb2Fkcy5wdXNoKGMpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGMgJiYgb3RoZXJIZWFkRWxlbWVudHMucHVzaChjKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGhlYWQgPSBjc3NQcmVsb2Fkcy5jb25jYXQob3RoZXJIZWFkRWxlbWVudHMpO1xuICAgICAgICB9XG4gICAgICAgIGxldCBjaGlsZHJlbiA9IF9yZWFjdC5kZWZhdWx0LkNoaWxkcmVuLnRvQXJyYXkodGhpcy5wcm9wcy5jaGlsZHJlbikuZmlsdGVyKEJvb2xlYW4pO1xuICAgICAgICAvLyBzaG93IGEgd2FybmluZyBpZiBIZWFkIGNvbnRhaW5zIDx0aXRsZT4gKG9ubHkgaW4gZGV2ZWxvcG1lbnQpXG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgICAgICBjaGlsZHJlbiA9IF9yZWFjdC5kZWZhdWx0LkNoaWxkcmVuLm1hcChjaGlsZHJlbiwgKGNoaWxkKT0+e1xuICAgICAgICAgICAgICAgIHZhciByZWY7XG4gICAgICAgICAgICAgICAgY29uc3QgaXNSZWFjdEhlbG1ldCA9IGNoaWxkID09PSBudWxsIHx8IGNoaWxkID09PSB2b2lkIDAgPyB2b2lkIDAgOiAocmVmID0gY2hpbGQucHJvcHMpID09PSBudWxsIHx8IHJlZiA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmWydkYXRhLXJlYWN0LWhlbG1ldCddO1xuICAgICAgICAgICAgICAgIGlmICghaXNSZWFjdEhlbG1ldCkge1xuICAgICAgICAgICAgICAgICAgICB2YXIgcmVmNjtcbiAgICAgICAgICAgICAgICAgICAgaWYgKChjaGlsZCA9PT0gbnVsbCB8fCBjaGlsZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogY2hpbGQudHlwZSkgPT09ICd0aXRsZScpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIldhcm5pbmc6IDx0aXRsZT4gc2hvdWxkIG5vdCBiZSB1c2VkIGluIF9kb2N1bWVudC5qcydzIDxIZWFkPi4gaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvbm8tZG9jdW1lbnQtdGl0bGVcIik7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoKGNoaWxkID09PSBudWxsIHx8IGNoaWxkID09PSB2b2lkIDAgPyB2b2lkIDAgOiBjaGlsZC50eXBlKSA9PT0gJ21ldGEnICYmIChjaGlsZCA9PT0gbnVsbCB8fCBjaGlsZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogKHJlZjYgPSBjaGlsZC5wcm9wcykgPT09IG51bGwgfHwgcmVmNiA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVmNi5uYW1lKSA9PT0gJ3ZpZXdwb3J0Jykge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiV2FybmluZzogdmlld3BvcnQgbWV0YSB0YWdzIHNob3VsZCBub3QgYmUgdXNlZCBpbiBfZG9jdW1lbnQuanMncyA8SGVhZD4uIGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL25vLWRvY3VtZW50LXZpZXdwb3J0LW1ldGFcIik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIGNoaWxkO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAodGhpcy5wcm9wcy5jcm9zc09yaWdpbikgY29uc29sZS53YXJuKCdXYXJuaW5nOiBgSGVhZGAgYXR0cmlidXRlIGBjcm9zc09yaWdpbmAgaXMgZGVwcmVjYXRlZC4gaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvZG9jLWNyb3Nzb3JpZ2luLWRlcHJlY2F0ZWQnKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCcgJiYgb3B0aW1pemVGb250cyAmJiAhaW5BbXBNb2RlKSB7XG4gICAgICAgICAgICBjaGlsZHJlbiA9IHRoaXMubWFrZVN0eWxlc2hlZXRJbmVydChjaGlsZHJlbik7XG4gICAgICAgIH1cbiAgICAgICAgY2hpbGRyZW4gPSB0aGlzLmhhbmRsZURvY3VtZW50U2NyaXB0TG9hZGVySXRlbXMoY2hpbGRyZW4pO1xuICAgICAgICBsZXQgaGFzQW1waHRtbFJlbCA9IGZhbHNlO1xuICAgICAgICBsZXQgaGFzQ2Fub25pY2FsUmVsID0gZmFsc2U7XG4gICAgICAgIC8vIHNob3cgd2FybmluZyBhbmQgcmVtb3ZlIGNvbmZsaWN0aW5nIGFtcCBoZWFkIHRhZ3NcbiAgICAgICAgaGVhZCA9IF9yZWFjdC5kZWZhdWx0LkNoaWxkcmVuLm1hcChoZWFkIHx8IFtdLCAoY2hpbGQpPT57XG4gICAgICAgICAgICBpZiAoIWNoaWxkKSByZXR1cm4gY2hpbGQ7XG4gICAgICAgICAgICBjb25zdCB7IHR5cGUgLCBwcm9wcyAgfSA9IGNoaWxkO1xuICAgICAgICAgICAgaWYgKGluQW1wTW9kZSkge1xuICAgICAgICAgICAgICAgIGxldCBiYWRQcm9wID0gJyc7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGUgPT09ICdtZXRhJyAmJiBwcm9wcy5uYW1lID09PSAndmlld3BvcnQnKSB7XG4gICAgICAgICAgICAgICAgICAgIGJhZFByb3AgPSAnbmFtZT1cInZpZXdwb3J0XCInO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gJ2xpbmsnICYmIHByb3BzLnJlbCA9PT0gJ2Nhbm9uaWNhbCcpIHtcbiAgICAgICAgICAgICAgICAgICAgaGFzQ2Fub25pY2FsUmVsID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICdzY3JpcHQnKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIG9ubHkgYmxvY2sgaWZcbiAgICAgICAgICAgICAgICAgICAgLy8gMS4gaXQgaGFzIGEgc3JjIGFuZCBpc24ndCBwb2ludGluZyB0byBhbXBwcm9qZWN0J3MgQ0ROXG4gICAgICAgICAgICAgICAgICAgIC8vIDIuIGl0IGlzIHVzaW5nIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIHdpdGhvdXQgYSB0eXBlIG9yXG4gICAgICAgICAgICAgICAgICAgIC8vIGEgdHlwZSBvZiB0ZXh0L2phdmFzY3JpcHRcbiAgICAgICAgICAgICAgICAgICAgaWYgKHByb3BzLnNyYyAmJiBwcm9wcy5zcmMuaW5kZXhPZignYW1wcHJvamVjdCcpIDwgLTEgfHwgcHJvcHMuZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwgJiYgKCFwcm9wcy50eXBlIHx8IHByb3BzLnR5cGUgPT09ICd0ZXh0L2phdmFzY3JpcHQnKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYmFkUHJvcCA9ICc8c2NyaXB0JztcbiAgICAgICAgICAgICAgICAgICAgICAgIE9iamVjdC5rZXlzKHByb3BzKS5mb3JFYWNoKChwcm9wKT0+e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhZFByb3AgKz0gYCAke3Byb3B9PVwiJHtwcm9wc1twcm9wXX1cImA7XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhZFByb3AgKz0gJy8+JztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoYmFkUHJvcCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYEZvdW5kIGNvbmZsaWN0aW5nIGFtcCB0YWcgXCIke2NoaWxkLnR5cGV9XCIgd2l0aCBjb25mbGljdGluZyBwcm9wICR7YmFkUHJvcH0gaW4gJHtfX05FWFRfREFUQV9fLnBhZ2V9LiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9jb25mbGljdGluZy1hbXAtdGFnYCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgLy8gbm9uLWFtcCBtb2RlXG4gICAgICAgICAgICAgICAgaWYgKHR5cGUgPT09ICdsaW5rJyAmJiBwcm9wcy5yZWwgPT09ICdhbXBodG1sJykge1xuICAgICAgICAgICAgICAgICAgICBoYXNBbXBodG1sUmVsID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gY2hpbGQ7XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBmaWxlcyA9IGdldERvY3VtZW50RmlsZXModGhpcy5jb250ZXh0LmJ1aWxkTWFuaWZlc3QsIHRoaXMuY29udGV4dC5fX05FWFRfREFUQV9fLnBhZ2UsIGluQW1wTW9kZSk7XG4gICAgICAgIHZhciBfbm9uY2UsIF9ub25jZTE7XG4gICAgICAgIHJldHVybigvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJoZWFkXCIsIE9iamVjdC5hc3NpZ24oe30sIHRoaXMucHJvcHMpLCB0aGlzLmNvbnRleHQuaXNEZXZlbG9wbWVudCAmJiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX3JlYWN0LmRlZmF1bHQuRnJhZ21lbnQsIG51bGwsIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInN0eWxlXCIsIHtcbiAgICAgICAgICAgIFwiZGF0YS1uZXh0LWhpZGUtZm91Y1wiOiB0cnVlLFxuICAgICAgICAgICAgXCJkYXRhLWFtcGRldm1vZGVcIjogaW5BbXBNb2RlID8gJ3RydWUnIDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgICAgICBfX2h0bWw6IGBib2R5e2Rpc3BsYXk6bm9uZX1gXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJub3NjcmlwdFwiLCB7XG4gICAgICAgICAgICBcImRhdGEtbmV4dC1oaWRlLWZvdWNcIjogdHJ1ZSxcbiAgICAgICAgICAgIFwiZGF0YS1hbXBkZXZtb2RlXCI6IGluQW1wTW9kZSA/ICd0cnVlJyA6IHVuZGVmaW5lZFxuICAgICAgICB9LCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiLCB7XG4gICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTDoge1xuICAgICAgICAgICAgICAgIF9faHRtbDogYGJvZHl7ZGlzcGxheTpibG9ja31gXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pKSksIGhlYWQsIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcIm1ldGFcIiwge1xuICAgICAgICAgICAgbmFtZTogXCJuZXh0LWhlYWQtY291bnRcIixcbiAgICAgICAgICAgIGNvbnRlbnQ6IF9yZWFjdC5kZWZhdWx0LkNoaWxkcmVuLmNvdW50KGhlYWQgfHwgW10pLnRvU3RyaW5nKClcbiAgICAgICAgfSksIGNoaWxkcmVuLCBvcHRpbWl6ZUZvbnRzICYmIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcIm1ldGFcIiwge1xuICAgICAgICAgICAgbmFtZTogXCJuZXh0LWZvbnQtcHJlY29ubmVjdFwiXG4gICAgICAgIH0pLCBpbkFtcE1vZGUgJiYgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KF9yZWFjdC5kZWZhdWx0LkZyYWdtZW50LCBudWxsLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJtZXRhXCIsIHtcbiAgICAgICAgICAgIG5hbWU6IFwidmlld3BvcnRcIixcbiAgICAgICAgICAgIGNvbnRlbnQ6IFwid2lkdGg9ZGV2aWNlLXdpZHRoLG1pbmltdW0tc2NhbGU9MSxpbml0aWFsLXNjYWxlPTFcIlxuICAgICAgICB9KSwgIWhhc0Nhbm9uaWNhbFJlbCAmJiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJsaW5rXCIsIHtcbiAgICAgICAgICAgIHJlbDogXCJjYW5vbmljYWxcIixcbiAgICAgICAgICAgIGhyZWY6IGNhbm9uaWNhbEJhc2UgKyAoMCwgX3V0aWxzKS5jbGVhbkFtcFBhdGgoZGFuZ2Vyb3VzQXNQYXRoKVxuICAgICAgICB9KSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwibGlua1wiLCB7XG4gICAgICAgICAgICByZWw6IFwicHJlbG9hZFwiLFxuICAgICAgICAgICAgYXM6IFwic2NyaXB0XCIsXG4gICAgICAgICAgICBocmVmOiBcImh0dHBzOi8vY2RuLmFtcHByb2plY3Qub3JnL3YwLmpzXCJcbiAgICAgICAgfSksIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChBbXBTdHlsZXMsIHtcbiAgICAgICAgICAgIHN0eWxlczogc3R5bGVzXG4gICAgICAgIH0pLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiLCB7XG4gICAgICAgICAgICBcImFtcC1ib2lsZXJwbGF0ZVwiOiBcIlwiLFxuICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgICAgICBfX2h0bWw6IGBib2R5ey13ZWJraXQtYW5pbWF0aW9uOi1hbXAtc3RhcnQgOHMgc3RlcHMoMSxlbmQpIDBzIDEgbm9ybWFsIGJvdGg7LW1vei1hbmltYXRpb246LWFtcC1zdGFydCA4cyBzdGVwcygxLGVuZCkgMHMgMSBub3JtYWwgYm90aDstbXMtYW5pbWF0aW9uOi1hbXAtc3RhcnQgOHMgc3RlcHMoMSxlbmQpIDBzIDEgbm9ybWFsIGJvdGg7YW5pbWF0aW9uOi1hbXAtc3RhcnQgOHMgc3RlcHMoMSxlbmQpIDBzIDEgbm9ybWFsIGJvdGh9QC13ZWJraXQta2V5ZnJhbWVzIC1hbXAtc3RhcnR7ZnJvbXt2aXNpYmlsaXR5OmhpZGRlbn10b3t2aXNpYmlsaXR5OnZpc2libGV9fUAtbW96LWtleWZyYW1lcyAtYW1wLXN0YXJ0e2Zyb217dmlzaWJpbGl0eTpoaWRkZW59dG97dmlzaWJpbGl0eTp2aXNpYmxlfX1ALW1zLWtleWZyYW1lcyAtYW1wLXN0YXJ0e2Zyb217dmlzaWJpbGl0eTpoaWRkZW59dG97dmlzaWJpbGl0eTp2aXNpYmxlfX1ALW8ta2V5ZnJhbWVzIC1hbXAtc3RhcnR7ZnJvbXt2aXNpYmlsaXR5OmhpZGRlbn10b3t2aXNpYmlsaXR5OnZpc2libGV9fUBrZXlmcmFtZXMgLWFtcC1zdGFydHtmcm9te3Zpc2liaWxpdHk6aGlkZGVufXRve3Zpc2liaWxpdHk6dmlzaWJsZX19YFxuICAgICAgICAgICAgfVxuICAgICAgICB9KSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwibm9zY3JpcHRcIiwgbnVsbCwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic3R5bGVcIiwge1xuICAgICAgICAgICAgXCJhbXAtYm9pbGVycGxhdGVcIjogXCJcIixcbiAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MOiB7XG4gICAgICAgICAgICAgICAgX19odG1sOiBgYm9keXstd2Via2l0LWFuaW1hdGlvbjpub25lOy1tb3otYW5pbWF0aW9uOm5vbmU7LW1zLWFuaW1hdGlvbjpub25lO2FuaW1hdGlvbjpub25lfWBcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSkpLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJzY3JpcHRcIiwge1xuICAgICAgICAgICAgYXN5bmM6IHRydWUsXG4gICAgICAgICAgICBzcmM6IFwiaHR0cHM6Ly9jZG4uYW1wcHJvamVjdC5vcmcvdjAuanNcIlxuICAgICAgICB9KSksICFpbkFtcE1vZGUgJiYgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KF9yZWFjdC5kZWZhdWx0LkZyYWdtZW50LCBudWxsLCAhaGFzQW1waHRtbFJlbCAmJiBoeWJyaWRBbXAgJiYgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwibGlua1wiLCB7XG4gICAgICAgICAgICByZWw6IFwiYW1waHRtbFwiLFxuICAgICAgICAgICAgaHJlZjogY2Fub25pY2FsQmFzZSArIGdldEFtcFBhdGgoYW1wUGF0aCwgZGFuZ2Vyb3VzQXNQYXRoKVxuICAgICAgICB9KSwgIW9wdGltaXplQ3NzICYmIHRoaXMuZ2V0Q3NzTGlua3MoZmlsZXMpLCAhb3B0aW1pemVDc3MgJiYgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwibm9zY3JpcHRcIiwge1xuICAgICAgICAgICAgXCJkYXRhLW4tY3NzXCI6IChfbm9uY2UgPSB0aGlzLnByb3BzLm5vbmNlKSAhPT0gbnVsbCAmJiBfbm9uY2UgIT09IHZvaWQgMCA/IF9ub25jZSA6ICcnXG4gICAgICAgIH0pLCAhZGlzYWJsZVJ1bnRpbWVKUyAmJiAhZGlzYWJsZUpzUHJlbG9hZCAmJiB0aGlzLmdldFByZWxvYWREeW5hbWljQ2h1bmtzKCksICFkaXNhYmxlUnVudGltZUpTICYmICFkaXNhYmxlSnNQcmVsb2FkICYmIHRoaXMuZ2V0UHJlbG9hZE1haW5MaW5rcyhmaWxlcyksICFkaXNhYmxlT3B0aW1pemVkTG9hZGluZyAmJiAhZGlzYWJsZVJ1bnRpbWVKUyAmJiB0aGlzLmdldFBvbHlmaWxsU2NyaXB0cygpLCAhZGlzYWJsZU9wdGltaXplZExvYWRpbmcgJiYgIWRpc2FibGVSdW50aW1lSlMgJiYgdGhpcy5nZXRQcmVOZXh0U2NyaXB0cygpLCAhZGlzYWJsZU9wdGltaXplZExvYWRpbmcgJiYgIWRpc2FibGVSdW50aW1lSlMgJiYgdGhpcy5nZXREeW5hbWljQ2h1bmtzKGZpbGVzKSwgIWRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nICYmICFkaXNhYmxlUnVudGltZUpTICYmIHRoaXMuZ2V0U2NyaXB0cyhmaWxlcyksIG9wdGltaXplQ3NzICYmIHRoaXMuZ2V0Q3NzTGlua3MoZmlsZXMpLCBvcHRpbWl6ZUNzcyAmJiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJub3NjcmlwdFwiLCB7XG4gICAgICAgICAgICBcImRhdGEtbi1jc3NcIjogKF9ub25jZTEgPSB0aGlzLnByb3BzLm5vbmNlKSAhPT0gbnVsbCAmJiBfbm9uY2UxICE9PSB2b2lkIDAgPyBfbm9uY2UxIDogJydcbiAgICAgICAgfSksIHRoaXMuY29udGV4dC5pc0RldmVsb3BtZW50ICYmIC8vIHRoaXMgZWxlbWVudCBpcyB1c2VkIHRvIG1vdW50IGRldmVsb3BtZW50IHN0eWxlcyBzbyB0aGVcbiAgICAgICAgLy8gb3JkZXJpbmcgbWF0Y2hlcyBwcm9kdWN0aW9uXG4gICAgICAgIC8vIChieSBkZWZhdWx0LCBzdHlsZS1sb2FkZXIgaW5qZWN0cyBhdCB0aGUgYm90dG9tIG9mIDxoZWFkIC8+KVxuICAgICAgICAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJub3NjcmlwdFwiLCB7XG4gICAgICAgICAgICBpZDogXCJfX25leHRfY3NzX19ET19OT1RfVVNFX19cIlxuICAgICAgICB9KSwgc3R5bGVzIHx8IG51bGwpLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX3JlYWN0LmRlZmF1bHQuRnJhZ21lbnQsIHt9LCAuLi5oZWFkVGFncyB8fCBbXSkpKTtcbiAgICB9XG59XG5leHBvcnRzLkhlYWQgPSBIZWFkO1xuSGVhZC5jb250ZXh0VHlwZSA9IF9odG1sQ29udGV4dC5IdG1sQ29udGV4dDtcbmZ1bmN0aW9uIE1haW4oKSB7XG4gICAgY29uc3QgeyBkb2NDb21wb25lbnRzUmVuZGVyZWQgIH0gPSAoMCwgX3JlYWN0KS51c2VDb250ZXh0KF9odG1sQ29udGV4dC5IdG1sQ29udGV4dCk7XG4gICAgZG9jQ29tcG9uZW50c1JlbmRlcmVkLk1haW4gPSB0cnVlO1xuICAgIC8vIEB0cy1pZ25vcmVcbiAgICByZXR1cm4oLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwibmV4dC1qcy1pbnRlcm5hbC1ib2R5LXJlbmRlci10YXJnZXRcIiwgbnVsbCkpO1xufVxuY2xhc3MgTmV4dFNjcmlwdCBleHRlbmRzIF9yZWFjdC5Db21wb25lbnQge1xuICAgIGdldER5bmFtaWNDaHVua3MoZmlsZXMpIHtcbiAgICAgICAgcmV0dXJuIGdldER5bmFtaWNDaHVua3ModGhpcy5jb250ZXh0LCB0aGlzLnByb3BzLCBmaWxlcyk7XG4gICAgfVxuICAgIGdldFByZU5leHRTY3JpcHRzKCkge1xuICAgICAgICByZXR1cm4gZ2V0UHJlTmV4dFNjcmlwdHModGhpcy5jb250ZXh0LCB0aGlzLnByb3BzKTtcbiAgICB9XG4gICAgZ2V0U2NyaXB0cyhmaWxlcykge1xuICAgICAgICByZXR1cm4gZ2V0U2NyaXB0cyh0aGlzLmNvbnRleHQsIHRoaXMucHJvcHMsIGZpbGVzKTtcbiAgICB9XG4gICAgZ2V0UG9seWZpbGxTY3JpcHRzKCkge1xuICAgICAgICByZXR1cm4gZ2V0UG9seWZpbGxTY3JpcHRzKHRoaXMuY29udGV4dCwgdGhpcy5wcm9wcyk7XG4gICAgfVxuICAgIHN0YXRpYyBnZXRJbmxpbmVTY3JpcHRTb3VyY2UoY29udGV4dCkge1xuICAgICAgICBjb25zdCB7IF9fTkVYVF9EQVRBX18gIH0gPSBjb250ZXh0O1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgZGF0YSA9IEpTT04uc3RyaW5naWZ5KF9fTkVYVF9EQVRBX18pO1xuICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgYnl0ZXMgPSBCdWZmZXIuZnJvbShkYXRhKS5ieXRlTGVuZ3RoO1xuICAgICAgICAgICAgICAgIGNvbnN0IHByZXR0eUJ5dGVzID0gcmVxdWlyZSgnLi4vbGliL3ByZXR0eS1ieXRlcycpLmRlZmF1bHQ7XG4gICAgICAgICAgICAgICAgaWYgKGJ5dGVzID4gMTI4ICogMTAwMCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYFdhcm5pbmc6IGRhdGEgZm9yIHBhZ2UgXCIke19fTkVYVF9EQVRBX18ucGFnZX1cIiBpcyAke3ByZXR0eUJ5dGVzKGJ5dGVzKX0sIHRoaXMgYW1vdW50IG9mIGRhdGEgY2FuIHJlZHVjZSBwZXJmb3JtYW5jZS5cXG5TZWUgbW9yZSBpbmZvIGhlcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL2xhcmdlLXBhZ2UtZGF0YWApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiAoMCwgX2h0bWxlc2NhcGUpLmh0bWxFc2NhcGVKc29uU3RyaW5nKGRhdGEpO1xuICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIGlmICgoMCwgX2lzRXJyb3IpLmRlZmF1bHQoZXJyKSAmJiBlcnIubWVzc2FnZS5pbmRleE9mKCdjaXJjdWxhciBzdHJ1Y3R1cmUnKSAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYENpcmN1bGFyIHN0cnVjdHVyZSBpbiBcImdldEluaXRpYWxQcm9wc1wiIHJlc3VsdCBvZiBwYWdlIFwiJHtfX05FWFRfREFUQV9fLnBhZ2V9XCIuIGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL2NpcmN1bGFyLXN0cnVjdHVyZWApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJlbmRlcigpIHtcbiAgICAgICAgY29uc3QgeyBhc3NldFByZWZpeCAsIGluQW1wTW9kZSAsIGJ1aWxkTWFuaWZlc3QgLCB1bnN0YWJsZV9ydW50aW1lSlMgLCBkb2NDb21wb25lbnRzUmVuZGVyZWQgLCBkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZyAsIGRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nICwgY3Jvc3NPcmlnaW4gLCAgfSA9IHRoaXMuY29udGV4dDtcbiAgICAgICAgY29uc3QgZGlzYWJsZVJ1bnRpbWVKUyA9IHVuc3RhYmxlX3J1bnRpbWVKUyA9PT0gZmFsc2U7XG4gICAgICAgIGRvY0NvbXBvbmVudHNSZW5kZXJlZC5OZXh0U2NyaXB0ID0gdHJ1ZTtcbiAgICAgICAgaWYgKGluQW1wTW9kZSkge1xuICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGFtcERldkZpbGVzID0gW1xuICAgICAgICAgICAgICAgIC4uLmJ1aWxkTWFuaWZlc3QuZGV2RmlsZXMsXG4gICAgICAgICAgICAgICAgLi4uYnVpbGRNYW5pZmVzdC5wb2x5ZmlsbEZpbGVzLFxuICAgICAgICAgICAgICAgIC4uLmJ1aWxkTWFuaWZlc3QuYW1wRGV2RmlsZXMsIFxuICAgICAgICAgICAgXTtcbiAgICAgICAgICAgIHJldHVybigvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX3JlYWN0LmRlZmF1bHQuRnJhZ21lbnQsIG51bGwsIGRpc2FibGVSdW50aW1lSlMgPyBudWxsIDogLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIsIHtcbiAgICAgICAgICAgICAgICBpZDogXCJfX05FWFRfREFUQV9fXCIsXG4gICAgICAgICAgICAgICAgdHlwZTogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgICAgICAgICAgbm9uY2U6IHRoaXMucHJvcHMubm9uY2UsXG4gICAgICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW4sXG4gICAgICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgICAgICAgICAgX19odG1sOiBOZXh0U2NyaXB0LmdldElubGluZVNjcmlwdFNvdXJjZSh0aGlzLmNvbnRleHQpXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBcImRhdGEtYW1wZGV2bW9kZVwiOiB0cnVlXG4gICAgICAgICAgICB9KSwgYW1wRGV2RmlsZXMubWFwKChmaWxlKT0+LyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIsIHtcbiAgICAgICAgICAgICAgICAgICAga2V5OiBmaWxlLFxuICAgICAgICAgICAgICAgICAgICBzcmM6IGAke2Fzc2V0UHJlZml4fS9fbmV4dC8ke2ZpbGV9JHtkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZ31gLFxuICAgICAgICAgICAgICAgICAgICBub25jZTogdGhpcy5wcm9wcy5ub25jZSxcbiAgICAgICAgICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW4sXG4gICAgICAgICAgICAgICAgICAgIFwiZGF0YS1hbXBkZXZtb2RlXCI6IHRydWVcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgKSkpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5wcm9wcy5jcm9zc09yaWdpbikgY29uc29sZS53YXJuKCdXYXJuaW5nOiBgTmV4dFNjcmlwdGAgYXR0cmlidXRlIGBjcm9zc09yaWdpbmAgaXMgZGVwcmVjYXRlZC4gaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvZG9jLWNyb3Nzb3JpZ2luLWRlcHJlY2F0ZWQnKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBmaWxlcyA9IGdldERvY3VtZW50RmlsZXModGhpcy5jb250ZXh0LmJ1aWxkTWFuaWZlc3QsIHRoaXMuY29udGV4dC5fX05FWFRfREFUQV9fLnBhZ2UsIGluQW1wTW9kZSk7XG4gICAgICAgIHJldHVybigvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX3JlYWN0LmRlZmF1bHQuRnJhZ21lbnQsIG51bGwsICFkaXNhYmxlUnVudGltZUpTICYmIGJ1aWxkTWFuaWZlc3QuZGV2RmlsZXMgPyBidWlsZE1hbmlmZXN0LmRldkZpbGVzLm1hcCgoZmlsZSk9Pi8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCB7XG4gICAgICAgICAgICAgICAga2V5OiBmaWxlLFxuICAgICAgICAgICAgICAgIHNyYzogYCR7YXNzZXRQcmVmaXh9L19uZXh0LyR7ZW5jb2RlVVJJKGZpbGUpfSR7ZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmd9YCxcbiAgICAgICAgICAgICAgICBub25jZTogdGhpcy5wcm9wcy5ub25jZSxcbiAgICAgICAgICAgICAgICBjcm9zc09yaWdpbjogdGhpcy5wcm9wcy5jcm9zc09yaWdpbiB8fCBjcm9zc09yaWdpblxuICAgICAgICAgICAgfSlcbiAgICAgICAgKSA6IG51bGwsIGRpc2FibGVSdW50aW1lSlMgPyBudWxsIDogLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIsIHtcbiAgICAgICAgICAgIGlkOiBcIl9fTkVYVF9EQVRBX19cIixcbiAgICAgICAgICAgIHR5cGU6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICAgICAgbm9uY2U6IHRoaXMucHJvcHMubm9uY2UsXG4gICAgICAgICAgICBjcm9zc09yaWdpbjogdGhpcy5wcm9wcy5jcm9zc09yaWdpbiB8fCBjcm9zc09yaWdpbixcbiAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MOiB7XG4gICAgICAgICAgICAgICAgX19odG1sOiBOZXh0U2NyaXB0LmdldElubGluZVNjcmlwdFNvdXJjZSh0aGlzLmNvbnRleHQpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pLCBkaXNhYmxlT3B0aW1pemVkTG9hZGluZyAmJiAhZGlzYWJsZVJ1bnRpbWVKUyAmJiB0aGlzLmdldFBvbHlmaWxsU2NyaXB0cygpLCBkaXNhYmxlT3B0aW1pemVkTG9hZGluZyAmJiAhZGlzYWJsZVJ1bnRpbWVKUyAmJiB0aGlzLmdldFByZU5leHRTY3JpcHRzKCksIGRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nICYmICFkaXNhYmxlUnVudGltZUpTICYmIHRoaXMuZ2V0RHluYW1pY0NodW5rcyhmaWxlcyksIGRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nICYmICFkaXNhYmxlUnVudGltZUpTICYmIHRoaXMuZ2V0U2NyaXB0cyhmaWxlcykpKTtcbiAgICB9XG59XG5leHBvcnRzLk5leHRTY3JpcHQgPSBOZXh0U2NyaXB0O1xuTmV4dFNjcmlwdC5jb250ZXh0VHlwZSA9IF9odG1sQ29udGV4dC5IdG1sQ29udGV4dDtcbk5leHRTY3JpcHQuc2FmYXJpTm9tb2R1bGVGaXggPSAnIWZ1bmN0aW9uKCl7dmFyIGU9ZG9jdW1lbnQsdD1lLmNyZWF0ZUVsZW1lbnQoXCJzY3JpcHRcIik7aWYoIShcIm5vTW9kdWxlXCJpbiB0KSYmXCJvbmJlZm9yZWxvYWRcImluIHQpe3ZhciBuPSExO2UuYWRkRXZlbnRMaXN0ZW5lcihcImJlZm9yZWxvYWRcIixmdW5jdGlvbihlKXtpZihlLnRhcmdldD09PXQpbj0hMDtlbHNlIGlmKCFlLnRhcmdldC5oYXNBdHRyaWJ1dGUoXCJub21vZHVsZVwiKXx8IW4pcmV0dXJuO2UucHJldmVudERlZmF1bHQoKX0sITApLHQudHlwZT1cIm1vZHVsZVwiLHQuc3JjPVwiLlwiLGUuaGVhZC5hcHBlbmRDaGlsZCh0KSx0LnJlbW92ZSgpfX0oKTsnO1xuZnVuY3Rpb24gZ2V0QW1wUGF0aChhbXBQYXRoLCBhc1BhdGgpIHtcbiAgICByZXR1cm4gYW1wUGF0aCB8fCBgJHthc1BhdGh9JHthc1BhdGguaW5jbHVkZXMoJz8nKSA/ICcmJyA6ICc/J31hbXA9MWA7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPV9kb2N1bWVudC5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJIdG1sIiwiTWFpbiIsImRlZmF1bHQiLCJfcmVhY3QiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsInJlcXVpcmUiLCJfY29uc3RhbnRzIiwiX2dldFBhZ2VGaWxlcyIsIl91dGlscyIsIl9odG1sZXNjYXBlIiwiX3NjcmlwdCIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJfaXNFcnJvciIsIl9odG1sQ29udGV4dCIsIm9iaiIsIl9fZXNNb2R1bGUiLCJuZXdPYmoiLCJrZXkiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJkZXNjIiwiZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yIiwiZ2V0Iiwic2V0IiwiZ2V0RG9jdW1lbnRGaWxlcyIsImJ1aWxkTWFuaWZlc3QiLCJwYXRobmFtZSIsImluQW1wTW9kZSIsInNoYXJlZEZpbGVzIiwiZ2V0UGFnZUZpbGVzIiwicGFnZUZpbGVzIiwiYWxsRmlsZXMiLCJTZXQiLCJnZXRQb2x5ZmlsbFNjcmlwdHMiLCJjb250ZXh0IiwicHJvcHMiLCJhc3NldFByZWZpeCIsImRldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nIiwiZGlzYWJsZU9wdGltaXplZExvYWRpbmciLCJjcm9zc09yaWdpbiIsInBvbHlmaWxsRmlsZXMiLCJmaWx0ZXIiLCJwb2x5ZmlsbCIsImVuZHNXaXRoIiwibWFwIiwiY3JlYXRlRWxlbWVudCIsImRlZmVyIiwibm9uY2UiLCJub01vZHVsZSIsInNyYyIsImhhc0NvbXBvbmVudFByb3BzIiwiY2hpbGQiLCJnZXRQcmVOZXh0V29ya2VyU2NyaXB0cyIsInNjcmlwdExvYWRlciIsIm5leHRTY3JpcHRXb3JrZXJzIiwicHJvY2VzcyIsImJyb3dzZXIiLCJwYXJ0eXRvd25TbmlwcGV0IiwiY2hpbGRyZW4iLCJBcnJheSIsImlzQXJyYXkiLCJ1c2VyRGVmaW5lZENvbmZpZyIsImZpbmQiLCJyZWYiLCJyZWYxIiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiLCJsZW5ndGgiLCJGcmFnbWVudCIsIndvcmtlciIsImZpbGUiLCJpbmRleCIsInN0cmF0ZWd5Iiwic2NyaXB0UHJvcHMiLCJhc3NpZ24iLCJ0eXBlIiwiZXJyIiwiY29uc29sZSIsIndhcm4iLCJnZXRQcmVOZXh0U2NyaXB0cyIsIndlYldvcmtlclNjcmlwdHMiLCJiZWZvcmVJbnRlcmFjdGl2ZVNjcmlwdHMiLCJiZWZvcmVJbnRlcmFjdGl2ZSIsIl9kZWZlciIsImdldER5bmFtaWNDaHVua3MiLCJmaWxlcyIsImR5bmFtaWNJbXBvcnRzIiwiaXNEZXZlbG9wbWVudCIsImluY2x1ZGVzIiwiYXN5bmMiLCJlbmNvZGVVUkkiLCJnZXRTY3JpcHRzIiwibm9ybWFsU2NyaXB0cyIsImxvd1ByaW9yaXR5U2NyaXB0cyIsImxvd1ByaW9yaXR5RmlsZXMiLCJEb2N1bWVudCIsIkNvbXBvbmVudCIsImdldEluaXRpYWxQcm9wcyIsImN0eCIsImRlZmF1bHRHZXRJbml0aWFsUHJvcHMiLCJyZW5kZXIiLCJIZWFkIiwiTmV4dFNjcmlwdCIsIl9fbmV4dF9pbnRlcm5hbF9kb2N1bWVudCIsIkludGVybmFsRnVuY3Rpb25Eb2N1bWVudCIsImRvY0NvbXBvbmVudHNSZW5kZXJlZCIsImxvY2FsZSIsInVzZUNvbnRleHQiLCJIdG1sQ29udGV4dCIsImxhbmciLCJ1bmRlZmluZWQiLCJhbXAiLCJBbXBTdHlsZXMiLCJzdHlsZXMiLCJjdXJTdHlsZXMiLCJoYXNTdHlsZXMiLCJlbCIsInJlZjIiLCJmb3JFYWNoIiwicHVzaCIsInN0eWxlIiwiam9pbiIsInJlcGxhY2UiLCJnZXRDc3NMaW5rcyIsIm9wdGltaXplQ3NzIiwib3B0aW1pemVGb250cyIsImNzc0ZpbGVzIiwiZiIsInVubWFuZ2VkRmlsZXMiLCJkeW5hbWljQ3NzRmlsZXMiLCJmcm9tIiwiZXhpc3RpbmciLCJoYXMiLCJjc3NMaW5rRWxlbWVudHMiLCJpc1NoYXJlZEZpbGUiLCJyZWwiLCJocmVmIiwiYXMiLCJpc1VubWFuYWdlZEZpbGUiLCJtYWtlU3R5bGVzaGVldEluZXJ0IiwiZ2V0UHJlbG9hZER5bmFtaWNDaHVua3MiLCJCb29sZWFuIiwiZ2V0UHJlbG9hZE1haW5MaW5rcyIsInByZWxvYWRGaWxlcyIsImhhbmRsZURvY3VtZW50U2NyaXB0TG9hZGVySXRlbXMiLCJzY3JpcHRMb2FkZXJJdGVtcyIsImZpbHRlcmVkQ2hpbGRyZW4iLCJDaGlsZHJlbiIsImNvbmNhdCIsIl9fTkVYVF9EQVRBX18iLCJub2RlIiwiYyIsInJlZjUiLCJyZWYzIiwiT1BUSU1JWkVEX0ZPTlRfUFJPVklERVJTIiwic29tZSIsInVybCIsInJlZjQiLCJzdGFydHNXaXRoIiwibmV3UHJvcHMiLCJjbG9uZUVsZW1lbnQiLCJhbXBQYXRoIiwiaHlicmlkQW1wIiwiY2Fub25pY2FsQmFzZSIsImRhbmdlcm91c0FzUGF0aCIsImhlYWRUYWdzIiwidW5zdGFibGVfcnVudGltZUpTIiwidW5zdGFibGVfSnNQcmVsb2FkIiwiZGlzYWJsZVJ1bnRpbWVKUyIsImRpc2FibGVKc1ByZWxvYWQiLCJoZWFkIiwiY3NzUHJlbG9hZHMiLCJvdGhlckhlYWRFbGVtZW50cyIsInRvQXJyYXkiLCJpc1JlYWN0SGVsbWV0IiwicmVmNiIsIm5hbWUiLCJoYXNBbXBodG1sUmVsIiwiaGFzQ2Fub25pY2FsUmVsIiwiYmFkUHJvcCIsImluZGV4T2YiLCJrZXlzIiwicHJvcCIsInBhZ2UiLCJfbm9uY2UiLCJfbm9uY2UxIiwiY29udGVudCIsImNvdW50IiwidG9TdHJpbmciLCJjbGVhbkFtcFBhdGgiLCJnZXRBbXBQYXRoIiwiaWQiLCJjb250ZXh0VHlwZSIsImdldElubGluZVNjcmlwdFNvdXJjZSIsImRhdGEiLCJKU09OIiwic3RyaW5naWZ5IiwiYnl0ZXMiLCJCdWZmZXIiLCJieXRlTGVuZ3RoIiwicHJldHR5Qnl0ZXMiLCJodG1sRXNjYXBlSnNvblN0cmluZyIsIm1lc3NhZ2UiLCJFcnJvciIsImFtcERldkZpbGVzIiwiZGV2RmlsZXMiLCJzYWZhcmlOb21vZHVsZUZpeCIsImFzUGF0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kinco-day-care-kindergarten-react-template-30-06-2025\\\\Kinco\\\\React Template\\\\pages\\\\_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kinco-day-care-kindergarten-react-template-30-06-2025\\\\Kinco\\\\React Template\\\\pages\\\\_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kinco-day-care-kindergarten-react-template-30-06-2025\\\\Kinco\\\\React Template\\\\pages\\\\_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kinco-day-care-kindergarten-react-template-30-06-2025\\\\Kinco\\\\React Template\\\\pages\\\\_document.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kinco-day-care-kindergarten-react-template-30-06-2025\\\\Kinco\\\\React Template\\\\pages\\\\_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQuanMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTREO0FBRTdDLFFBQVEsQ0FBQ0ksUUFBUSxHQUFHLENBQUM7SUFDbEMsTUFBTSw2RUFDSEgsK0NBQUk7UUFBQ0ksSUFBSSxFQUFDLENBQUk7O3dGQUNaTCwrQ0FBSTs7Ozs7d0ZBQ0pNLENBQUk7O2dHQUNGSiwrQ0FBSTs7Ozs7Z0dBQ0pDLHFEQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQixDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2luY28tcmVhY3QvLi9wYWdlcy9fZG9jdW1lbnQuanM/NTM4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIZWFkLCBIdG1sLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSBcIm5leHQvZG9jdW1lbnRcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8SHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPEhlYWQgLz5cclxuICAgICAgPGJvZHk+XHJcbiAgICAgICAgPE1haW4gLz5cclxuICAgICAgICA8TmV4dFNjcmlwdCAvPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L0h0bWw+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiSGVhZCIsIkh0bWwiLCJNYWluIiwiTmV4dFNjcmlwdCIsIkRvY3VtZW50IiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_document.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nexports.getProperError = getProperError;\nvar _isPlainObject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === 'object' && err !== null && 'name' in err && 'message' in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === 'undefined') {\n            return new Error('An undefined error was thrown, ' + 'see here for more info: https://nextjs.org/docs/messages/threw-undefined');\n        }\n        if (err === null) {\n            return new Error('A null error was thrown, ' + 'see here for more info: https://nextjs.org/docs/messages/threw-undefined');\n        }\n    }\n    return new Error((0, _isPlainObject).isPlainObject(err) ? JSON.stringify(err) : err + '');\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2xpYi9pcy1lcnJvci5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGtCQUFlO0FBQ2Ysc0JBQXNCO0FBQ3RCLHFCQUFxQixtQkFBTyxDQUFDLG9FQUErQjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsSUFBc0M7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tpbmNvLXJlYWN0Ly4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9saWIvaXMtZXJyb3IuanM/MTc4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IGlzRXJyb3I7XG5leHBvcnRzLmdldFByb3BlckVycm9yID0gZ2V0UHJvcGVyRXJyb3I7XG52YXIgX2lzUGxhaW5PYmplY3QgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9pcy1wbGFpbi1vYmplY3RcIik7XG5mdW5jdGlvbiBpc0Vycm9yKGVycikge1xuICAgIHJldHVybiB0eXBlb2YgZXJyID09PSAnb2JqZWN0JyAmJiBlcnIgIT09IG51bGwgJiYgJ25hbWUnIGluIGVyciAmJiAnbWVzc2FnZScgaW4gZXJyO1xufVxuZnVuY3Rpb24gZ2V0UHJvcGVyRXJyb3IoZXJyKSB7XG4gICAgaWYgKGlzRXJyb3IoZXJyKSkge1xuICAgICAgICByZXR1cm4gZXJyO1xuICAgIH1cbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgICAgLy8gcHJvdmlkZSBiZXR0ZXIgZXJyb3IgZm9yIGNhc2Ugd2hlcmUgYHRocm93IHVuZGVmaW5lZGBcbiAgICAgICAgLy8gaXMgY2FsbGVkIGluIGRldmVsb3BtZW50XG4gICAgICAgIGlmICh0eXBlb2YgZXJyID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBFcnJvcignQW4gdW5kZWZpbmVkIGVycm9yIHdhcyB0aHJvd24sICcgKyAnc2VlIGhlcmUgZm9yIG1vcmUgaW5mbzogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvdGhyZXctdW5kZWZpbmVkJyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGVyciA9PT0gbnVsbCkge1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBFcnJvcignQSBudWxsIGVycm9yIHdhcyB0aHJvd24sICcgKyAnc2VlIGhlcmUgZm9yIG1vcmUgaW5mbzogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvdGhyZXctdW5kZWZpbmVkJyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG5ldyBFcnJvcigoMCwgX2lzUGxhaW5PYmplY3QpLmlzUGxhaW5PYmplY3QoZXJyKSA/IEpTT04uc3RyaW5naWZ5KGVycikgOiBlcnIgKyAnJyk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWlzLWVycm9yLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = prettyBytes;\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return ' 0 B';\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? '-' : options.signed ? '+' : '';\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + ' B';\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + ' ' + unit;\n}\n/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ const UNITS = [\n    'B',\n    'kB',\n    'MB',\n    'GB',\n    'TB',\n    'PB',\n    'EB',\n    'ZB',\n    'YB'\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === 'string') {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "./node_modules/next/document.js":
/*!***************************************!*\
  !*** ./node_modules/next/document.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/pages/_document */ \"./node_modules/next/dist/pages/_document.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kb2N1bWVudC5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxpSEFBa0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9raW5jby1yZWFjdC8uL25vZGVfbW9kdWxlcy9uZXh0L2RvY3VtZW50LmpzPzlhMTQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvcGFnZXMvX2RvY3VtZW50JylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/document.js\n");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/head-manager-context":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/head-manager-context.js" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/head-manager-context.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_document.js"));
module.exports = __webpack_exports__;

})();