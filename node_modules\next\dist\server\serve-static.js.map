{"version": 3, "sources": ["../../server/serve-static.ts"], "names": ["serveStatic", "getContentType", "getExtension", "req", "res", "path", "Promise", "resolve", "reject", "send", "on", "err", "Error", "code", "pipe", "extWithoutDot", "mime", "getType", "lookup", "contentType", "extension"], "mappings": "Y;;;E;QAGgBA,WAAW,GAAXA,WAAW,A;QAmBXC,cAAc,GAAdA,cAAc,A;QAcdC,YAAY,GAAZA,YAAY,A;AAnCX,GAAyB,CAAzB,KAAyB;;;;;;SAE1BF,WAAW,CACzBG,GAAoB,EACpBC,GAAmB,EACnBC,IAAY,EACG,CAAC;IAChB,MAAM,CAAC,GAAG,CAACC,OAAO,EAAEC,OAAO,EAAEC,MAAM,GAAK,CAAC;YACvCC,KAAI,UAACN,GAAG,EAAEE,IAAI,EACXK,EAAE,CAAC,CAAW,gBAAQ,CAAC;YACtB,EAAyC,AAAzC,uCAAyC;YACzC,KAAK,CAACC,GAAG,GAAQ,GAAG,CAACC,KAAK,CAAC,CAAqB;YAChDD,GAAG,CAACE,IAAI,GAAG,CAAQ,O;YACnBL,MAAM,CAACG,GAAG,C;QACZ,CAAC,EACAD,EAAE,CAAC,CAAO,QAAEF,MAAM,EAClBM,IAAI,CAACV,GAAG,EACRM,EAAE,CAAC,CAAQ,SAAEH,OAAO,C;IACzB,CAAC;AACH,CAAC;SAEeN,cAAc,CAACc,aAAqB,EAAiB,CAAC;IACpE,EAAE,EAAEA,aAAa,KAAK,CAAM,OAAE,CAAC;QAC7B,EAA8B,AAA9B,4BAA8B;QAC9B,MAAM,CAAC,CAAY;IACrB,CAAC;IACD,KAAK,CAAC,CAAC,CAACC,IAAI,EAAC,CAAC,GAAGP,KAAI;IACrB,EAAE,EAAE,CAAS,YAAIO,IAAI,EAAE,CAAC;QACtB,EAAM,AAAN,IAAM;QACN,MAAM,CAACA,IAAI,CAACC,OAAO,CAACF,aAAa;IACnC,CAAC;IACD,EAAM,AAAN,IAAM;IACN,MAAM,CAAEC,IAAI,CAASE,MAAM,CAACH,aAAa;AAC3C,CAAC;SAEeb,YAAY,CAACiB,WAAmB,EAAiB,CAAC;IAChE,EAAE,EAAEA,WAAW,KAAK,CAAY,aAAE,CAAC;QACjC,EAA8B,AAA9B,4BAA8B;QAC9B,MAAM,CAAC,CAAM;IACf,CAAC;IACD,KAAK,CAAC,CAAC,CAACH,IAAI,EAAC,CAAC,GAAGP,KAAI;IACrB,EAAE,EAAE,CAAc,iBAAIO,IAAI,EAAE,CAAC;QAC3B,EAAM,AAAN,IAAM;QACN,MAAM,CAACA,IAAI,CAACd,YAAY,CAACiB,WAAW;IACtC,CAAC;IACD,EAAM,AAAN,IAAM;IACN,MAAM,CAAEH,IAAI,CAASI,SAAS,CAACD,WAAW;AAC5C,CAAC"}