{"version": 3, "sources": ["../../../../build/webpack/plugins/serverless-plugin.ts"], "names": ["ServerlessPlugin", "apply", "compiler", "hooks", "compilation", "tap", "hook", "optimizeChunks", "chunks", "chunk", "chunkGraph", "getNumberOfEntryModules", "dynamicChunks", "getAllAsyncChunks", "dynamicChunk", "module", "getChunkModulesIterable", "isModuleInChunk", "connectChunkAndModule"], "mappings": "Y;;;E;MAQaA,gBAAgB;IAC3BC,KAAK,CAACC,QAA0B,EAAE,CAAC;QACjCA,QAAQ,CAACC,KAAK,CAACC,WAAW,CAACC,GAAG,CAAC,CAAkB,oBAAGD,WAAW,GAAK,CAAC;YACnE,KAAK,CAACE,IAAI,GAAGF,WAAW,CAACD,KAAK,CAACI,cAAc;YAE7CD,IAAI,CAACD,GAAG,CAAC,CAAkB,oBAAGG,MAAM,GAAK,CAAC;gBACxC,GAAG,EAAE,KAAK,CAACC,KAAK,IAAID,MAAM,CAAE,CAAC;oBAC3B,EAA2C,AAA3C,yCAA2C;oBAC3C,EAA0D,AAA1D,wDAA0D;oBAC1D,EAAE,EAAEJ,WAAW,CAACM,UAAU,CAACC,uBAAuB,CAACF,KAAK,MAAM,CAAC,EAAE,CAAC;wBAChE,QAAQ;oBACV,CAAC;oBAED,EAAkD,AAAlD,gDAAkD;oBAClD,KAAK,CAACG,aAAa,GAAGH,KAAK,CAACI,iBAAiB;oBAC7C,GAAG,EAAE,KAAK,CAACC,YAAY,IAAIF,aAAa,CAAE,CAAC;wBACzC,EAA0D,AAA1D,wDAA0D;wBAC1D,GAAG,EAAE,KAAK,CAACG,MAAM,IAAIX,WAAW,CAACM,UAAU,CAACM,uBAAuB,CACjEF,YAAY,EACX,CAAC;4BACF,EAAuC,AAAvC,qCAAuC;4BACvC,EAA0D,AAA1D,wDAA0D;4BAC1D,EAAE,GAAGV,WAAW,CAACM,UAAU,CAACO,eAAe,CAACF,MAAM,EAAEN,KAAK,GAAG,CAAC;gCAC3D,EAA0D,AAA1D,wDAA0D;gCAC1DL,WAAW,CAACM,UAAU,CAACQ,qBAAqB,CAACT,KAAK,EAAEM,MAAM,C;4BAC5D,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,C;QACH,CAAC,C;IACH,CAAC;;QA/BUf,gBAAgB,GAAhBA,gBAAgB,A"}