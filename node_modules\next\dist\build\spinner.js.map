{"version": 3, "sources": ["../../build/spinner.ts"], "names": ["createSpinner", "text", "options", "logFn", "console", "log", "spinner", "prefixText", "process", "stdout", "isTTY", "ora", "undefined", "dots<PERSON>pinner", "stream", "start", "origLog", "origWarn", "warn", "origError", "error", "origStop", "stop", "bind", "origStopAndPersist", "stopAndPersist", "logHandle", "method", "args", "resetLog", "frames", "interval"], "mappings": "Y;;;E;kBAOwBA,aAAa,A;AAPrB,GAAwB,CAAxB,IAAwB;SAOhBA,aAAa,CACnCC,IAAqC,EACrCC,OAAoB,GAAG,CAAC,CAAC,EACzBC,KAA+B,GAAGC,OAAO,CAACC,GAAG,EAC7C,CAAC;IACD,GAAG,CAACC,OAAO;IACX,GAAG,CAACC,UAAU,GAAGN,IAAI,IAAI,MAAM,CAACA,IAAI,KAAK,CAAQ,WAAIA,IAAI,CAACM,UAAU;IAEpE,EAAE,EAAEC,OAAO,CAACC,MAAM,CAACC,KAAK,EAAE,CAAC;QACzBJ,OAAO,OAAGK,IAAG,UAAC,CAAC;YACbV,IAAI,EAAE,MAAM,CAACA,IAAI,KAAK,CAAQ,UAAGA,IAAI,GAAGW,SAAS;YACjDL,UAAU,EAAE,MAAM,CAACA,UAAU,KAAK,CAAQ,UAAGA,UAAU,GAAGK,SAAS;YACnEN,OAAO,EAAEO,WAAW;YACpBC,MAAM,EAAEN,OAAO,CAACC,MAAM;eACnBP,OAAO;QACZ,CAAC,EAAEa,KAAK,E;QAER,EAA2D,AAA3D,yDAA2D;QAC3D,EAA+D,AAA/D,6DAA+D;QAC/D,KAAK,CAACC,OAAO,GAAGZ,OAAO,CAACC,GAAG;QAC3B,KAAK,CAACY,QAAQ,GAAGb,OAAO,CAACc,IAAI;QAC7B,KAAK,CAACC,SAAS,GAAGf,OAAO,CAACgB,KAAK;QAC/B,KAAK,CAACC,QAAQ,GAAGf,OAAO,CAACgB,IAAI,CAACC,IAAI,CAACjB,OAAO;QAC1C,KAAK,CAACkB,kBAAkB,GAAGlB,OAAO,CAACmB,cAAc,CAACF,IAAI,CAACjB,OAAO;QAE9D,KAAK,CAACoB,SAAS,IAAIC,MAAW,EAAEC,IAAW,GAAK,CAAC;YAC/CP,QAAQ,E;YACRM,MAAM,IAAIC,IAAI,C;YACdtB,OAAO,CAAES,KAAK,E;QAChB,CAAC;QAEDX,OAAO,CAACC,GAAG,OAAOuB,IAAI,GAAUF,SAAS,CAACV,OAAO,EAAEY,IAAI;A;QACvDxB,OAAO,CAACc,IAAI,OAAOU,IAAI,GAAUF,SAAS,CAACT,QAAQ,EAAEW,IAAI;A;QACzDxB,OAAO,CAACgB,KAAK,OAAOQ,IAAI,GAAUF,SAAS,CAACP,SAAS,EAAES,IAAI;A;QAE3D,KAAK,CAACC,QAAQ,OAAS,CAAC;YACtBzB,OAAO,CAACC,GAAG,GAAGW,OAAO,A;YACrBZ,OAAO,CAACc,IAAI,GAAGD,QAAQ,A;YACvBb,OAAO,CAACgB,KAAK,GAAGD,SAAS,A;QAC3B,CAAC;QACDb,OAAO,CAACgB,IAAI,OAAkB,CAAC;YAC7BD,QAAQ,E;YACRQ,QAAQ,E;YACR,MAAM,CAACvB,OAAO;QAChB,CAAC,A;QACDA,OAAO,CAACmB,cAAc,OAAkB,CAAC;YACvCD,kBAAkB,E;YAClBK,QAAQ,E;YACR,MAAM,CAACvB,OAAO;QAChB,CAAC,A;IACH,CAAC,MAAM,EAAE,EAAEC,UAAU,IAAIN,IAAI,EAAE,CAAC;QAC9BE,KAAK,CAACI,UAAU,GAAGA,UAAU,GAAG,CAAK,OAAGN,IAAI,C;IAC9C,CAAC;IAED,MAAM,CAACK,OAAO;AAChB,CAAC;;;;;;AA5DD,KAAK,CAACO,WAAW,GAAG,CAAC;IACnBiB,MAAM,EAAE,CAAC;QAAA,CAAG;QAAE,CAAI;QAAE,CAAK;IAAA,CAAC;IAC1BC,QAAQ,EAAE,GAAG;AACf,CAAC"}