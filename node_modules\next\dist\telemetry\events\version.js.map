{"version": 3, "sources": ["../../../telemetry/events/version.ts"], "names": ["eventCliSession", "EVENT_VERSION", "hasBabelConfig", "dir", "res", "noopFile", "path", "join", "require", "loadPartialConfig", "cwd", "filename", "sourceFileName", "isForTooling", "options", "presets", "every", "e", "file", "request", "plugins", "length", "hasFilesystemConfig", "nextConfig", "event", "process", "env", "__NEXT_VERSION", "images", "i18n", "payload", "nextVersion", "nodeVersion", "version", "cliCommand", "isSrcDir", "has<PERSON>ow<PERSON><PERSON>", "isCustomServer", "hasNextConfig", "config<PERSON><PERSON><PERSON>", "buildTarget", "target", "hasWebpackConfig", "webpack", "imageEnabled", "basePathEnabled", "basePath", "i18nEnabled", "locales", "localeDomainsCount", "domains", "localeDetectionEnabled", "localeDetection", "imageDomainsCount", "imageSizes", "imageLoader", "loader", "trailingSlashEnabled", "trailingSlash", "reactStrictMode", "webpackVersion", "eventName"], "mappings": "Y;;;E;QAgDgBA,eAAe,GAAfA,eAAe,A;AAhDd,GAAM,CAAN,KAAM;;;;;;AAGvB,KAAK,CAACC,aAAa,GAAG,CAA0B;SA2BvCC,cAAc,CAACC,GAAW,EAAW,CAAC;IAC7C,GAAG,CAAC,CAAC;YAQDC,IAAW,QAENA,IAAW;QATlB,KAAK,CAACC,QAAQ,GAAGC,KAAI,SAACC,IAAI,CAACJ,GAAG,EAAE,CAAS;QACzC,KAAK,CAACC,GAAG,GAAGI,OAAO,CAAC,CAA+B,gCAAEC,iBAAiB,CAAC,CAAC;YACtEC,GAAG,EAAEP,GAAG;YACRQ,QAAQ,EAAEN,QAAQ;YAClBO,cAAc,EAAEP,QAAQ;QAC1B,CAAC;QACD,KAAK,CAACQ,YAAY,KAChBT,IAAW,GAAXA,GAAG,CAACU,OAAO,cAAXV,IAAW,cAAXA,IAAIU,CAAJV,CAAoB,WAApBA,IAAW,CAAEW,OAAO,gCAApBX,IAAIU,CAAJV,CAAoB,QAAEY,KAAK,EACxBC,CAAM;gBAAKA,GAAO;YAAPA,MAAM,EAANA,CAAC,aAADA,CAAC,cAADA,IAAI,CAAJA,CAAO,IAAPA,GAAO,GAAPA,CAAC,CAAEC,IAAI,cAAPD,GAAO,cAAPA,IAAI,CAAJA,CAAO,GAAPA,GAAO,CAAEE,OAAO,MAAK,CAAY;iBAC1Cf,IAAW,GAAXA,GAAG,CAACU,OAAO,cAAXV,IAAW,cAAXA,IAAIU,CAAJV,CAAoB,WAApBA,IAAW,CAAEgB,OAAO,gCAApBhB,IAAIU,CAAJV,CAAoB,QAAEiB,MAAM,MAAK,CAAC;QACzC,MAAM,CAACjB,GAAG,CAACkB,mBAAmB,OAAOT,YAAY;IACnD,CAAC,CAAC,KAAK,EAAC,CAAC;QACP,MAAM,CAAC,KAAK;IACd,CAAC;AACH,CAAC;SAEeb,eAAe,CAC7BG,GAAW,EACXoB,UAA8B,EAC9BC,KAmBC,EACyD,CAAC;IAC3D,EAAwE,AAAxE,sEAAwE;IACxE,EAAE,EAAE,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,cAAc,KAAK,CAAQ,SAAE,CAAC;QACnD,MAAM,CAAC,CAAC,CAAC;IACX,CAAC;IAED,KAAK,CAAC,CAAC,CAACC,MAAM,GAAEC,IAAI,EAAC,CAAC,GAAGN,UAAU,IAAI,CAAC,CAAC;IAEzC,KAAK,CAACO,OAAO,GAA2B,CAAC;QACvCC,WAAW,EAAEN,OAAO,CAACC,GAAG,CAACC,cAAc;QACvCK,WAAW,EAAEP,OAAO,CAACQ,OAAO;QAC5BC,UAAU,EAAEV,KAAK,CAACU,UAAU;QAC5BC,QAAQ,EAAEX,KAAK,CAACW,QAAQ;QACxBC,UAAU,EAAEZ,KAAK,CAACY,UAAU;QAC5BC,cAAc,EAAEb,KAAK,CAACa,cAAc;QACpCC,aAAa,EAAEf,UAAU,CAACgB,YAAY,KAAK,CAAS;QACpDC,WAAW,EAAEjB,UAAU,CAACkB,MAAM,KAAK,CAAQ,UAAG,CAAS,WAAGlB,UAAU,CAACkB,MAAM;QAC3EC,gBAAgB,EAAE,MAAM,EAACnB,UAAU,aAAVA,UAAU,cAAVA,IAAI,CAAJA,CAAmB,GAAnBA,UAAU,CAAEoB,OAAO,MAAK,CAAU;QAC3DzC,cAAc,EAAEA,cAAc,CAACC,GAAG;QAClCyC,YAAY,IAAIhB,MAAM;QACtBiB,eAAe,KAAItB,UAAU,aAAVA,UAAU,cAAVA,IAAI,CAAJA,CAAoB,GAApBA,UAAU,CAAEuB,QAAQ;QACvCC,WAAW,IAAIlB,IAAI;QACnBmB,OAAO,GAAEnB,IAAI,aAAJA,IAAI,cAAJA,IAAI,CAAJA,CAAa,GAAbA,IAAI,CAAEmB,OAAO,IAAGnB,IAAI,CAACmB,OAAO,CAACzC,IAAI,CAAC,CAAG,MAAI,IAAI;QACtD0C,kBAAkB,GAAEpB,IAAI,aAAJA,IAAI,cAAJA,IAAI,CAAJA,CAAa,GAAbA,IAAI,CAAEqB,OAAO,IAAGrB,IAAI,CAACqB,OAAO,CAAC7B,MAAM,GAAG,IAAI;QAC9D8B,sBAAsB,GAAGtB,IAAI,GAAG,IAAI,GAAGA,IAAI,CAACuB,eAAe,KAAK,KAAK;QACrEC,iBAAiB,GAAEzB,MAAM,aAANA,MAAM,cAANA,IAAI,CAAJA,CAAe,GAAfA,MAAM,CAAEsB,OAAO,IAAGtB,MAAM,CAACsB,OAAO,CAAC7B,MAAM,GAAG,IAAI;QACjEiC,UAAU,GAAE1B,MAAM,aAANA,MAAM,cAANA,IAAI,CAAJA,CAAkB,GAAlBA,MAAM,CAAE0B,UAAU,IAAG1B,MAAM,CAAC0B,UAAU,CAAC/C,IAAI,CAAC,CAAG,MAAI,IAAI;QACnEgD,WAAW,EAAE3B,MAAM,aAANA,MAAM,cAANA,IAAI,CAAJA,CAAc,GAAdA,MAAM,CAAE4B,MAAM;QAC3BC,oBAAoB,KAAIlC,UAAU,aAAVA,UAAU,cAAVA,IAAI,CAAJA,CAAyB,GAAzBA,UAAU,CAAEmC,aAAa;QACjDC,eAAe,KAAIpC,UAAU,aAAVA,UAAU,cAAVA,IAAI,CAAJA,CAA2B,GAA3BA,UAAU,CAAEoC,eAAe;QAC9CC,cAAc,EAAEpC,KAAK,CAACoC,cAAc,IAAI,IAAI;IAC9C,CAAC;IACD,MAAM,CAAC,CAAC;QAAA,CAAC;YAACC,SAAS,EAAE5D,aAAa;YAAE6B,OAAO;QAAC,CAAC;IAAA,CAAC;AAChD,CAAC"}