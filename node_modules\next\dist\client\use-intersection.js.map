{"version": 3, "sources": ["../../client/use-intersection.tsx"], "names": ["useIntersection", "hasIntersectionObserver", "IntersectionObserver", "rootRef", "rootMargin", "disabled", "isDisabled", "unobserve", "useRef", "visible", "setVisible", "useState", "root", "setRoot", "current", "setRef", "useCallback", "el", "undefined", "tagName", "observe", "isVisible", "useEffect", "idleCallback", "requestIdleCallback", "cancelIdleCallback", "element", "callback", "options", "id", "observer", "elements", "createObserver", "set", "delete", "size", "disconnect", "observers", "index", "idList", "findIndex", "obj", "margin", "splice", "Map", "existing", "find", "instance", "get", "push", "entries", "for<PERSON>ach", "entry", "target", "isIntersecting", "intersectionRatio"], "mappings": "Y;;;E;QA2BgBA,eAAe,GAAfA,eAAe,A;AA3B0B,GAAO,CAAP,MAAO;AAIzD,GAAyB,CAAzB,oBAAyB;AAqBhC,KAAK,CAACC,uBAAuB,GAAG,MAAM,CAACC,oBAAoB,KAAK,CAAW;SAE3DF,eAAe,CAAoB,CAAC,CAClDG,OAAO,GACPC,UAAU,GACVC,QAAQ,EACO,CAAC,EAA0C,CAAC;IAC3D,KAAK,CAACC,UAAU,GAAYD,QAAQ,KAAKJ,uBAAuB;IAEhE,KAAK,CAACM,SAAS,OAAGC,MAAM;IACxB,KAAK,EAAEC,OAAO,EAAEC,UAAU,QAAIC,MAAQ,WAAC,KAAK;IAC5C,KAAK,EAAEC,IAAI,EAAEC,OAAO,QAAIF,MAAQ,WAACR,OAAO,GAAGA,OAAO,CAACW,OAAO,GAAG,IAAI;IACjE,KAAK,CAACC,MAAM,OAAGC,MAAW,eACvBC,EAAY,GAAK,CAAC;QACjB,EAAE,EAAEV,SAAS,CAACO,OAAO,EAAE,CAAC;YACtBP,SAAS,CAACO,OAAO,E;YACjBP,SAAS,CAACO,OAAO,GAAGI,SAAS,A;QAC/B,CAAC;QAED,EAAE,EAAEZ,UAAU,IAAIG,OAAO,EAAE,MAAM;QAEjC,EAAE,EAAEQ,EAAE,IAAIA,EAAE,CAACE,OAAO,EAAE,CAAC;YACrBZ,SAAS,CAACO,OAAO,GAAGM,OAAO,CACzBH,EAAE,GACDI,SAAS,GAAKA,SAAS,IAAIX,UAAU,CAACW,SAAS;cAChD,CAAC;gBAACT,IAAI;gBAAER,UAAU;YAAC,CAAC,C;QAExB,CAAC;IACH,CAAC,EACD,CAACE;QAAAA,UAAU;QAAEM,IAAI;QAAER,UAAU;QAAEK,OAAO;IAAA,CAAC;QAGzCa,MAAS,gBAAO,CAAC;QACf,EAAE,GAAGrB,uBAAuB,EAAE,CAAC;YAC7B,EAAE,GAAGQ,OAAO,EAAE,CAAC;gBACb,KAAK,CAACc,YAAY,OAAGC,oBAAmB,0BAAOd,UAAU,CAAC,IAAI;;gBAC9D,MAAM,SAAOe,oBAAkB,qBAACF,YAAY;;YAC9C,CAAC;QACH,CAAC;IACH,CAAC,EAAE,CAACd;QAAAA,OAAO;IAAA,CAAC,C;QAEZa,MAAS,gBAAO,CAAC;QACf,EAAE,EAAEnB,OAAO,EAAEU,OAAO,CAACV,OAAO,CAACW,OAAO,C;IACtC,CAAC,EAAE,CAACX;QAAAA,OAAO;IAAA,CAAC,C;IACZ,MAAM,CAAC,CAACY;QAAAA,MAAM;QAAEN,OAAO;IAAA,CAAC;AAC1B,CAAC;SAEQW,OAAO,CACdM,OAAgB,EAChBC,QAAyB,EACzBC,OAAoC,EACxB,CAAC;IACb,KAAK,CAAC,CAAC,CAACC,EAAE,GAAEC,QAAQ,GAAEC,QAAQ,EAAC,CAAC,GAAGC,cAAc,CAACJ,OAAO;IACzDG,QAAQ,CAACE,GAAG,CAACP,OAAO,EAAEC,QAAQ,C;IAE9BG,QAAQ,CAACV,OAAO,CAACM,OAAO,C;IACxB,MAAM,CAAC,QAAQ,CAACnB,SAAS,GAAS,CAAC;QACjCwB,QAAQ,CAACG,MAAM,CAACR,OAAO,C;QACvBI,QAAQ,CAACvB,SAAS,CAACmB,OAAO,C;QAE1B,EAAuD,AAAvD,qDAAuD;QACvD,EAAE,EAAEK,QAAQ,CAACI,IAAI,KAAK,CAAC,EAAE,CAAC;YACxBL,QAAQ,CAACM,UAAU,E;YACnBC,SAAS,CAACH,MAAM,CAACL,EAAE,C;YACnB,GAAG,CAACS,KAAK,GAAGC,MAAM,CAACC,SAAS,EACzBC,GAAG,GAAKA,GAAG,CAAC7B,IAAI,KAAKiB,EAAE,CAACjB,IAAI,IAAI6B,GAAG,CAACC,MAAM,KAAKb,EAAE,CAACa,MAAM;;YAE3D,EAAE,EAAEJ,KAAK,IAAI,CAAC,EAAE,CAAC;gBACfC,MAAM,CAACI,MAAM,CAACL,KAAK,EAAE,CAAC,C;YACxB,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,CAACD,SAAS,GAAG,GAAG,CAACO,GAAG;AAEzB,KAAK,CAACL,MAAM,GAAiB,CAAC,CAAC;SAEtBP,cAAc,CAACJ,OAAoC,EAAY,CAAC;IACvE,KAAK,CAACC,EAAE,GAAG,CAAC;QACVjB,IAAI,EAAEgB,OAAO,CAAChB,IAAI,IAAI,IAAI;QAC1B8B,MAAM,EAAEd,OAAO,CAACxB,UAAU,IAAI,CAAE;IAClC,CAAC;IACD,GAAG,CAACyC,QAAQ,GAAGN,MAAM,CAACO,IAAI,EACvBL,GAAG,GAAKA,GAAG,CAAC7B,IAAI,KAAKiB,EAAE,CAACjB,IAAI,IAAI6B,GAAG,CAACC,MAAM,KAAKb,EAAE,CAACa,MAAM;;IAE3D,GAAG,CAACK,QAAQ;IACZ,EAAE,EAAEF,QAAQ,EAAE,CAAC;QACbE,QAAQ,GAAGV,SAAS,CAACW,GAAG,CAACH,QAAQ,C;IACnC,CAAC,MAAM,CAAC;QACNE,QAAQ,GAAGV,SAAS,CAACW,GAAG,CAACnB,EAAE,C;QAC3BU,MAAM,CAACU,IAAI,CAACpB,EAAE,C;IAChB,CAAC;IACD,EAAE,EAAEkB,QAAQ,EAAE,CAAC;QACb,MAAM,CAACA,QAAQ;IACjB,CAAC;IAED,KAAK,CAAChB,QAAQ,GAAG,GAAG,CAACa,GAAG;IACxB,KAAK,CAACd,QAAQ,GAAG,GAAG,CAAC5B,oBAAoB,EAAEgD,OAAO,GAAK,CAAC;QACtDA,OAAO,CAACC,OAAO,EAAEC,KAAK,GAAK,CAAC;YAC1B,KAAK,CAACzB,QAAQ,GAAGI,QAAQ,CAACiB,GAAG,CAACI,KAAK,CAACC,MAAM;YAC1C,KAAK,CAAChC,SAAS,GAAG+B,KAAK,CAACE,cAAc,IAAIF,KAAK,CAACG,iBAAiB,GAAG,CAAC;YACrE,EAAE,EAAE5B,QAAQ,IAAIN,SAAS,EAAE,CAAC;gBAC1BM,QAAQ,CAACN,SAAS,C;YACpB,CAAC;QACH,CAAC,C;IACH,CAAC,EAAEO,OAAO;IAEVS,SAAS,CAACJ,GAAG,CACXJ,EAAE,EACDkB,QAAQ,GAAG,CAAC;QACXlB,EAAE;QACFC,QAAQ;QACRC,QAAQ;IACV,CAAC,C;IAEH,MAAM,CAACgB,QAAQ;AACjB,CAAC"}