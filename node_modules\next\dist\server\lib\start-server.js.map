{"version": 3, "sources": ["../../../server/lib/start-server.ts"], "names": ["startServer", "opts", "requestHandler", "server", "http", "createServer", "req", "res", "Promise", "resolve", "reject", "port", "retryCount", "on", "err", "allowRetry", "code", "warn", "listen", "hostname", "addr", "address", "app", "next", "customServer", "httpServer", "getRequestHandler"], "mappings": "Y;;;E;QASgBA,WAAW,GAAXA,WAAW,A;AARN,GAAwB,CAAxB,IAAwB;AAC5B,GAAM,CAAN,KAAM;AACN,GAAS,CAAT,KAAS;;;;;;SAMVA,WAAW,CAACC,IAAwB,EAAE,CAAC;IACrD,GAAG,CAACC,cAAc;IAElB,KAAK,CAACC,MAAM,GAAGC,KAAI,SAACC,YAAY,EAAEC,GAAG,EAAEC,GAAG,GAAK,CAAC;QAC9C,MAAM,CAACL,cAAc,CAACI,GAAG,EAAEC,GAAG;IAChC,CAAC;IAED,MAAM,CAAC,GAAG,CAACC,OAAO,EAAcC,OAAO,EAAEC,MAAM,GAAK,CAAC;QACnD,GAAG,CAACC,IAAI,GAAGV,IAAI,CAACU,IAAI;QACpB,GAAG,CAACC,UAAU,GAAG,CAAC;QAElBT,MAAM,CAACU,EAAE,CAAC,CAAO,SAAGC,GAA0B,GAAK,CAAC;YAClD,EAAE,EACAH,IAAI,IACJV,IAAI,CAACc,UAAU,IACfD,GAAG,CAACE,IAAI,KAAK,CAAY,eACzBJ,UAAU,GAAG,EAAE,EACf,CAAC;oBACDK,IAAI,QAAE,KAAK,EAAEN,IAAI,CAAC,mBAAmB,EAAEA,IAAI,GAAG,CAAC,CAAC,SAAS,E;gBACzDA,IAAI,IAAI,CAAC,A;gBACTC,UAAU,IAAI,CAAC,A;gBACfT,MAAM,CAACe,MAAM,CAACP,IAAI,EAAEV,IAAI,CAACkB,QAAQ,C;YACnC,CAAC,MAAM,CAAC;gBACNT,MAAM,CAACI,GAAG,C;YACZ,CAAC;QACH,CAAC,C;QAEDX,MAAM,CAACU,EAAE,CAAC,CAAW,gBAAQ,CAAC;YAC5B,KAAK,CAACO,IAAI,GAAGjB,MAAM,CAACkB,OAAO;YAC3B,KAAK,CAACF,QAAQ,IACXlB,IAAI,CAACkB,QAAQ,IAAIlB,IAAI,CAACkB,QAAQ,KAAK,CAAS,WACzC,CAAW,aACXlB,IAAI,CAACkB,QAAQ;YAEnB,KAAK,CAACG,GAAG,OAAGC,KAAI,UAAC,CAAC;mBACbtB,IAAI;gBACPkB,QAAQ;gBACRK,YAAY,EAAE,KAAK;gBACnBC,UAAU,EAAEtB,MAAM;gBAClBQ,IAAI,EAAES,IAAI,IAAI,MAAM,CAACA,IAAI,KAAK,CAAQ,UAAGA,IAAI,CAACT,IAAI,GAAGA,IAAI;YAC3D,CAAC;YAEDT,cAAc,GAAGoB,GAAG,CAACI,iBAAiB,E;YACtCjB,OAAO,CAACa,GAAG,C;QACb,CAAC,C;QAEDnB,MAAM,CAACe,MAAM,CAACP,IAAI,EAAEV,IAAI,CAACkB,QAAQ,C;IACnC,CAAC;AACH,CAAC"}