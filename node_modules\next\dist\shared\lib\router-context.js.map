{"version": 3, "sources": ["../../../shared/lib/router-context.ts"], "names": ["RouterContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "Y;;;E;8B;AAAkB,GAAO,CAAP,MAAO;;;;;;AAGlB,KAAK,CAACA,aAAa,GAAGC,MAAK,SAACC,aAAa,CAAa,IAAI;QAApDF,aAAa,GAAbA,aAAa,A;AAE1B,EAAE,EAAEG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,CAAY,aAAE,CAAC;IAC1CL,aAAa,CAACM,WAAW,GAAG,CAAe,c;AAC7C,CAAC"}