{"version": 3, "sources": ["../../client/script.tsx"], "names": ["initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "ignoreProps", "loadScript", "props", "src", "id", "onLoad", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "el", "document", "createElement", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "set", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "k", "value", "Object", "entries", "undefined", "includes", "attr", "DOMAttributeNames", "toLowerCase", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "handleClientScriptLoad", "window", "requestIdleCallback", "loadLazyScript", "readyState", "scriptLoaderItems", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "scripts", "getIsSsr", "useContext", "HeadManagerContext", "useEffect", "concat"], "mappings": "Y;;;E;QAmIgBA,gBAAgB,GAAhBA,gBAAgB,A;wB;AAnIa,GAAO,CAAP,MAAO;AAEjB,GAAoC,CAApC,mBAAoC;AACrC,GAAgB,CAAhB,YAAgB;AACd,GAAyB,CAAzB,oBAAyB;;;;;;;;U;;wB;;;;;;;;;;;;;;gE;;8C;;;;;4B;;;;;;;;;;;e;;;qD;U;;;;;;;;;;;qC;;;qC;;;;;;;;;;;2B;;iC;;;;AAE7D,KAAK,CAACC,WAAW,GAAG,GAAG,CAACC,GAAG;AAC3B,KAAK,CAACC,SAAS,GAAG,GAAG,CAACC,GAAG;AAezB,KAAK,CAACC,WAAW,GAAG,CAAC;IACnB,CAAQ;IACR,CAAyB;IACzB,CAAU;IACV,CAAS;IACT,CAAU;AACZ,CAAC;AAED,KAAK,CAACC,UAAU,IAAIC,KAAkB,GAAW,CAAC;IAChD,KAAK,CAAC,CAAC,CACLC,GAAG,GACHC,EAAE,GACFC,MAAM,MAAS,CAAC,CAAC,GACjBC,uBAAuB,GACvBC,QAAQ,EAAG,CAAE,IACbC,QAAQ,EAAG,CAAkB,oBAC7BC,OAAO,IACT,CAAC,GAAGP,KAAK;IAET,KAAK,CAACQ,QAAQ,GAAGN,EAAE,IAAID,GAAG;IAE1B,EAA4B,AAA5B,0BAA4B;IAC5B,EAAE,EAAEO,QAAQ,IAAIZ,SAAS,CAACa,GAAG,CAACD,QAAQ,GAAG,CAAC;QACxC,MAAM;IACR,CAAC;IAED,EAAqD,AAArD,mDAAqD;IACrD,EAAE,EAAEd,WAAW,CAACe,GAAG,CAACR,GAAG,GAAG,CAAC;QACzBL,SAAS,CAACc,GAAG,CAACF,QAAQ,C;QACtB,EAAoD,AAApD,kDAAoD;QACpDd,WAAW,CAACiB,GAAG,CAACV,GAAG,EAAEW,IAAI,CAACT,MAAM,EAAEI,OAAO,C;QACzC,MAAM;IACR,CAAC;IAED,KAAK,CAACM,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,CAAQ;IAE1C,KAAK,CAACC,WAAW,GAAG,GAAG,CAACC,OAAO,EAAQC,OAAO,EAAEC,MAAM,GAAK,CAAC;QAC1DN,EAAE,CAACO,gBAAgB,CAAC,CAAM,OAAE,QAAQ,CAAEC,CAAC,EAAE,CAAC;YACxCH,OAAO,E;YACP,EAAE,EAAEf,MAAM,EAAE,CAAC;gBACXA,MAAM,CAACmB,IAAI,CAAC,IAAI,EAAED,CAAC,C;YACrB,CAAC;QACH,CAAC,C;QACDR,EAAE,CAACO,gBAAgB,CAAC,CAAO,QAAE,QAAQ,CAAEC,CAAC,EAAE,CAAC;YACzCF,MAAM,CAACE,CAAC,C;QACV,CAAC,C;IACH,CAAC,EAAEE,KAAK,CAAC,QAAQ,CAAEF,CAAC,EAAE,CAAC;QACrB,EAAE,EAAEd,OAAO,EAAE,CAAC;YACZA,OAAO,CAACc,CAAC,C;QACX,CAAC;IACH,CAAC;IAED,EAAE,EAAEpB,GAAG,EAAE,CAAC;QACRP,WAAW,CAAC8B,GAAG,CAACvB,GAAG,EAAEe,WAAW,C;IAClC,CAAC;IACDpB,SAAS,CAACc,GAAG,CAACF,QAAQ,C;IAEtB,EAAE,EAAEJ,uBAAuB,EAAE,CAAC;QAC5BS,EAAE,CAACY,SAAS,GAAGrB,uBAAuB,CAACsB,MAAM,IAAI,CAAE,C;IACrD,CAAC,MAAM,EAAE,EAAErB,QAAQ,EAAE,CAAC;QACpBQ,EAAE,CAACc,WAAW,GACZ,MAAM,CAACtB,QAAQ,KAAK,CAAQ,UACxBA,QAAQ,GACRuB,KAAK,CAACC,OAAO,CAACxB,QAAQ,IACtBA,QAAQ,CAACyB,IAAI,CAAC,CAAE,KAChB,CAAE,C;IACV,CAAC,MAAM,EAAE,EAAE7B,GAAG,EAAE,CAAC;QACfY,EAAE,CAACZ,GAAG,GAAGA,GAAG,A;IACd,CAAC;IAED,GAAG,EAAE,KAAK,EAAE8B,CAAC,EAAEC,KAAK,KAAKC,MAAM,CAACC,OAAO,CAAClC,KAAK,EAAG,CAAC;QAC/C,EAAE,EAAEgC,KAAK,KAAKG,SAAS,IAAIrC,WAAW,CAACsC,QAAQ,CAACL,CAAC,GAAG,CAAC;YACnD,QAAQ;QACV,CAAC;QAED,KAAK,CAACM,IAAI,GAAGC,YAAiB,mBAACP,CAAC,KAAKA,CAAC,CAACQ,WAAW;QAClD1B,EAAE,CAAC2B,YAAY,CAACH,IAAI,EAAEL,KAAK,C;IAC7B,CAAC;IAED,EAAE,EAAE1B,QAAQ,KAAK,CAAQ,SAAE,CAAC;QAC1BO,EAAE,CAAC2B,YAAY,CAAC,CAAM,OAAE,CAAgB,gB;IAC1C,CAAC;IAED3B,EAAE,CAAC2B,YAAY,CAAC,CAAc,eAAElC,QAAQ,C;IAExCQ,QAAQ,CAAC2B,IAAI,CAACC,WAAW,CAAC7B,EAAE,C;AAC9B,CAAC;SAEQ8B,sBAAsB,CAAC3C,KAAkB,EAAE,CAAC;IACnD,KAAK,CAAC,CAAC,CAACM,QAAQ,EAAG,CAAkB,mBAAC,CAAC,GAAGN,KAAK;IAC/C,EAAE,EAAEM,QAAQ,KAAK,CAAkB,mBAAE,CAAC;QACpCP,UAAU,CAACC,KAAK,C;IAClB,CAAC,MAAM,EAAE,EAAEM,QAAQ,KAAK,CAAY,aAAE,CAAC;QACrCsC,MAAM,CAACxB,gBAAgB,CAAC,CAAM,WAAQ,CAAC;gBACrCyB,oBAAmB,0BAAO9C,UAAU,CAACC,KAAK;a;QAC5C,CAAC,C;IACH,CAAC;AACH,CAAC;SAEQ8C,cAAc,CAAC9C,KAAkB,EAAE,CAAC;IAC3C,EAAE,EAAEc,QAAQ,CAACiC,UAAU,KAAK,CAAU,WAAE,CAAC;YACvCF,oBAAmB,0BAAO9C,UAAU,CAACC,KAAK;S;IAC5C,CAAC,MAAM,CAAC;QACN4C,MAAM,CAACxB,gBAAgB,CAAC,CAAM,WAAQ,CAAC;gBACrCyB,oBAAmB,0BAAO9C,UAAU,CAACC,KAAK;a;QAC5C,CAAC,C;IACH,CAAC;AACH,CAAC;SAEeP,gBAAgB,CAACuD,iBAAgC,EAAE,CAAC;IAClEA,iBAAiB,CAACC,OAAO,CAACN,sBAAsB,C;AAClD,CAAC;SAEQO,MAAM,CAAClD,KAAkB,EAAsB,CAAC;IACvD,KAAK,CAAC,CAAC,CACLC,GAAG,EAAG,CAAE,IACRE,MAAM,MAAS,CAAC,CAAC,GACjBC,uBAAuB,GACvBE,QAAQ,EAAG,CAAkB,oBAC7BC,OAAO,EAET,CAAC,GAAGP,KAAK,EADJmD,SAAS,4BACVnD,KAAK;QANPC,CAAG;QACHE,CAAM;QACNC,CAAuB;QACvBE,CAAQ;QACRC,CAAO;;IAIT,EAAuC,AAAvC,qCAAuC;IACvC,KAAK,CAAC,CAAC,CAAC6C,aAAa,GAAEC,OAAO,GAAEC,QAAQ,EAAC,CAAC,OAAGC,MAAU,aAACC,mBAAkB;QAE1EC,MAAS,gBAAO,CAAC;QACf,EAAE,EAAEnD,QAAQ,KAAK,CAAkB,mBAAE,CAAC;YACpCP,UAAU,CAACC,KAAK,C;QAClB,CAAC,MAAM,EAAE,EAAEM,QAAQ,KAAK,CAAY,aAAE,CAAC;YACrCwC,cAAc,CAAC9C,KAAK,C;QACtB,CAAC;IACH,CAAC,EAAE,CAACA;QAAAA,KAAK;QAAEM,QAAQ;IAAA,CAAC,C;IAEpB,EAAE,EAAEA,QAAQ,KAAK,CAAmB,sBAAIA,QAAQ,KAAK,CAAQ,SAAE,CAAC;QAC9D,EAAE,EAAE8C,aAAa,EAAE,CAAC;YAClBC,OAAO,CAAC/C,QAAQ,KAAK+C,OAAO,CAAC/C,QAAQ,KAAK,CAAC,CAAC,EAAEoD,MAAM,CAAC,CAAC;;oBAElDzD,GAAG;oBACHE,MAAM;oBACNI,OAAO;mBACJ4C,SAAS;YAEhB,CAAC,C;YACDC,aAAa,CAACC,OAAO,C;QACvB,CAAC,MAAM,EAAE,EAAEC,QAAQ,IAAIA,QAAQ,IAAI,CAAC;YAClC,EAAuC,AAAvC,qCAAuC;YACvC1D,SAAS,CAACc,GAAG,CAACyC,SAAS,CAACjD,EAAE,IAAID,GAAG,C;QACnC,CAAC,MAAM,EAAE,EAAEqD,QAAQ,KAAKA,QAAQ,IAAI,CAAC;YACnCvD,UAAU,CAACC,KAAK,C;QAClB,CAAC;IACH,CAAC;IAED,MAAM,CAAC,IAAI;AACb,CAAC;eAEckD,MAAM;0B"}