/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_Counter_js"],{

/***/ "./node_modules/countup.js/dist/countUp.umd.js":
/*!*****************************************************!*\
  !*** ./node_modules/countup.js/dist/countUp.umd.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("!function(t,i){ true?i(exports):0}(this,(function(t){\"use strict\";var i=function(){return i=Object.assign||function(t){for(var i,n=1,s=arguments.length;n<s;n++)for(var e in i=arguments[n])Object.prototype.hasOwnProperty.call(i,e)&&(t[e]=i[e]);return t},i.apply(this,arguments)},n=function(){function t(t,n,s){var e=this;this.endVal=n,this.options=s,this.version=\"2.9.0\",this.defaults={startVal:0,decimalPlaces:0,duration:2,useEasing:!0,useGrouping:!0,useIndianSeparators:!1,smartEasingThreshold:999,smartEasingAmount:333,separator:\",\",decimal:\".\",prefix:\"\",suffix:\"\",enableScrollSpy:!1,scrollSpyDelay:200,scrollSpyOnce:!1},this.finalEndVal=null,this.useEasing=!0,this.countDown=!1,this.error=\"\",this.startVal=0,this.paused=!0,this.once=!1,this.count=function(t){e.startTime||(e.startTime=t);var i=t-e.startTime;e.remaining=e.duration-i,e.useEasing?e.countDown?e.frameVal=e.startVal-e.easingFn(i,0,e.startVal-e.endVal,e.duration):e.frameVal=e.easingFn(i,e.startVal,e.endVal-e.startVal,e.duration):e.frameVal=e.startVal+(e.endVal-e.startVal)*(i/e.duration);var n=e.countDown?e.frameVal<e.endVal:e.frameVal>e.endVal;e.frameVal=n?e.endVal:e.frameVal,e.frameVal=Number(e.frameVal.toFixed(e.options.decimalPlaces)),e.printValue(e.frameVal),i<e.duration?e.rAF=requestAnimationFrame(e.count):null!==e.finalEndVal?e.update(e.finalEndVal):e.options.onCompleteCallback&&e.options.onCompleteCallback()},this.formatNumber=function(t){var i,n,s,a,o=t<0?\"-\":\"\";i=Math.abs(t).toFixed(e.options.decimalPlaces);var r=(i+=\"\").split(\".\");if(n=r[0],s=r.length>1?e.options.decimal+r[1]:\"\",e.options.useGrouping){a=\"\";for(var l=3,u=0,h=0,p=n.length;h<p;++h)e.options.useIndianSeparators&&4===h&&(l=2,u=1),0!==h&&u%l==0&&(a=e.options.separator+a),u++,a=n[p-h-1]+a;n=a}return e.options.numerals&&e.options.numerals.length&&(n=n.replace(/[0-9]/g,(function(t){return e.options.numerals[+t]})),s=s.replace(/[0-9]/g,(function(t){return e.options.numerals[+t]}))),o+e.options.prefix+n+s+e.options.suffix},this.easeOutExpo=function(t,i,n,s){return n*(1-Math.pow(2,-10*t/s))*1024/1023+i},this.options=i(i({},this.defaults),s),this.formattingFn=this.options.formattingFn?this.options.formattingFn:this.formatNumber,this.easingFn=this.options.easingFn?this.options.easingFn:this.easeOutExpo,this.el=\"string\"==typeof t?document.getElementById(t):t,n=null==n?this.parse(this.el.innerHTML):n,this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.endVal=this.validateValue(n),this.options.decimalPlaces=Math.max(this.options.decimalPlaces),this.resetDuration(),this.options.separator=String(this.options.separator),this.useEasing=this.options.useEasing,\"\"===this.options.separator&&(this.options.useGrouping=!1),this.el?this.printValue(this.startVal):this.error=\"[CountUp] target is null or undefined\",\"undefined\"!=typeof window&&this.options.enableScrollSpy&&(this.error?console.error(this.error,t):(window.onScrollFns=window.onScrollFns||[],window.onScrollFns.push((function(){return e.handleScroll(e)})),window.onscroll=function(){window.onScrollFns.forEach((function(t){return t()}))},this.handleScroll(this)))}return t.prototype.handleScroll=function(t){if(t&&window&&!t.once){var i=window.innerHeight+window.scrollY,n=t.el.getBoundingClientRect(),s=n.top+window.pageYOffset,e=n.top+n.height+window.pageYOffset;e<i&&e>window.scrollY&&t.paused?(t.paused=!1,setTimeout((function(){return t.start()}),t.options.scrollSpyDelay),t.options.scrollSpyOnce&&(t.once=!0)):(window.scrollY>e||s>i)&&!t.paused&&t.reset()}},t.prototype.determineDirectionAndSmartEasing=function(){var t=this.finalEndVal?this.finalEndVal:this.endVal;this.countDown=this.startVal>t;var i=t-this.startVal;if(Math.abs(i)>this.options.smartEasingThreshold&&this.options.useEasing){this.finalEndVal=t;var n=this.countDown?1:-1;this.endVal=t+n*this.options.smartEasingAmount,this.duration=this.duration/2}else this.endVal=t,this.finalEndVal=null;null!==this.finalEndVal?this.useEasing=!1:this.useEasing=this.options.useEasing},t.prototype.start=function(t){this.error||(this.options.onStartCallback&&this.options.onStartCallback(),t&&(this.options.onCompleteCallback=t),this.duration>0?(this.determineDirectionAndSmartEasing(),this.paused=!1,this.rAF=requestAnimationFrame(this.count)):this.printValue(this.endVal))},t.prototype.pauseResume=function(){this.paused?(this.startTime=null,this.duration=this.remaining,this.startVal=this.frameVal,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count)):cancelAnimationFrame(this.rAF),this.paused=!this.paused},t.prototype.reset=function(){cancelAnimationFrame(this.rAF),this.paused=!0,this.resetDuration(),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.printValue(this.startVal)},t.prototype.update=function(t){cancelAnimationFrame(this.rAF),this.startTime=null,this.endVal=this.validateValue(t),this.endVal!==this.frameVal&&(this.startVal=this.frameVal,null==this.finalEndVal&&this.resetDuration(),this.finalEndVal=null,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count))},t.prototype.printValue=function(t){var i;if(this.el){var n=this.formattingFn(t);if(null===(i=this.options.plugin)||void 0===i?void 0:i.render)this.options.plugin.render(this.el,n);else if(\"INPUT\"===this.el.tagName)this.el.value=n;else\"text\"===this.el.tagName||\"tspan\"===this.el.tagName?this.el.textContent=n:this.el.innerHTML=n}},t.prototype.ensureNumber=function(t){return\"number\"==typeof t&&!isNaN(t)},t.prototype.validateValue=function(t){var i=Number(t);return this.ensureNumber(i)?i:(this.error=\"[CountUp] invalid start or end value: \".concat(t),null)},t.prototype.resetDuration=function(){this.startTime=null,this.duration=1e3*Number(this.options.duration),this.remaining=this.duration},t.prototype.parse=function(t){var i=function(t){return t.replace(/([.,'  ])/g,\"\\\\$1\")},n=i(this.options.separator),s=i(this.options.decimal),e=t.replace(new RegExp(n,\"g\"),\"\").replace(new RegExp(s,\"g\"),\".\");return parseFloat(e)},t}();t.CountUp=n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/countup.js/dist/countUp.umd.js\n");

/***/ }),

/***/ "./src/components/Counter.js":
/*!***********************************!*\
  !*** ./src/components/Counter.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_countup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-countup */ \"./node_modules/react-countup/build/index.js\");\n/* harmony import */ var react_visibility_sensor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-visibility-sensor */ \"./node_modules/react-visibility-sensor/dist/visibility-sensor.js\");\n/* harmony import */ var react_visibility_sensor__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_visibility_sensor__WEBPACK_IMPORTED_MODULE_2__);\nvar _this = undefined;\n\n\n\nvar Counter = function(param1) {\n    var end = param1.end, decimals = param1.decimals;\n    var _this1 = _this;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_countup__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        end: end ? end : 100,\n        duration: 1,\n        decimals: decimals ? decimals : 0,\n        children: function(param) {\n            var countUpRef = param.countUpRef, start = param.start;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_visibility_sensor__WEBPACK_IMPORTED_MODULE_2___default()), {\n                onChange: start,\n                delayedCall: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"counter\",\n                    \"data-from\": \"0\",\n                    \"data-to\": end,\n                    ref: countUpRef,\n                    children: \"count\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kinco-day-care-kindergarten-react-template-30-06-2025\\\\Kinco\\\\React Template\\\\src\\\\components\\\\Counter.js\",\n                    lineNumber: 12,\n                    columnNumber: 11\n                }, _this1)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kinco-day-care-kindergarten-react-template-30-06-2025\\\\Kinco\\\\React Template\\\\src\\\\components\\\\Counter.js\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, _this1);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\kinco-day-care-kindergarten-react-template-30-06-2025\\\\Kinco\\\\React Template\\\\src\\\\components\\\\Counter.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, _this);\n};\n_c = Counter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Counter);\nvar _c;\n$RefreshReg$(_c, \"Counter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Counter.js\n");

/***/ }),

/***/ "./node_modules/react-countup/build/index.js":
/*!***************************************************!*\
  !*** ./node_modules/react-countup/build/index.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar React = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nvar countup_js = __webpack_require__(/*! countup.js */ \"./node_modules/countup.js/dist/countUp.umd.js\");\n\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : String(i);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n/**\n * Silence SSR Warnings.\n * Borrowed from Formik v2.1.1, Licensed MIT.\n *\n * https://github.com/formium/formik/blob/9316a864478f8fcd4fa99a0735b1d37afdf507dc/LICENSE\n */\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n/**\n * Create a stable reference to a callback which is updated after each render is committed.\n * Typed version borrowed from Formik v2.2.1. Licensed MIT.\n *\n * https://github.com/formium/formik/blob/9316a864478f8fcd4fa99a0735b1d37afdf507dc/LICENSE\n */\nfunction useEventCallback(fn) {\n  var ref = React.useRef(fn);\n\n  // we copy a ref to the callback scoped to the current state/props on each render\n  useIsomorphicLayoutEffect(function () {\n    ref.current = fn;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current.apply(void 0, args);\n  }, []);\n}\n\nvar createCountUpInstance = function createCountUpInstance(el, props) {\n  var decimal = props.decimal,\n    decimals = props.decimals,\n    duration = props.duration,\n    easingFn = props.easingFn,\n    end = props.end,\n    formattingFn = props.formattingFn,\n    numerals = props.numerals,\n    prefix = props.prefix,\n    separator = props.separator,\n    start = props.start,\n    suffix = props.suffix,\n    useEasing = props.useEasing,\n    useGrouping = props.useGrouping,\n    useIndianSeparators = props.useIndianSeparators,\n    enableScrollSpy = props.enableScrollSpy,\n    scrollSpyDelay = props.scrollSpyDelay,\n    scrollSpyOnce = props.scrollSpyOnce,\n    plugin = props.plugin;\n  return new countup_js.CountUp(el, end, {\n    startVal: start,\n    duration: duration,\n    decimal: decimal,\n    decimalPlaces: decimals,\n    easingFn: easingFn,\n    formattingFn: formattingFn,\n    numerals: numerals,\n    separator: separator,\n    prefix: prefix,\n    suffix: suffix,\n    plugin: plugin,\n    useEasing: useEasing,\n    useIndianSeparators: useIndianSeparators,\n    useGrouping: useGrouping,\n    enableScrollSpy: enableScrollSpy,\n    scrollSpyDelay: scrollSpyDelay,\n    scrollSpyOnce: scrollSpyOnce\n  });\n};\n\nvar _excluded$1 = [\"ref\", \"startOnMount\", \"enableReinitialize\", \"delay\", \"onEnd\", \"onStart\", \"onPauseResume\", \"onReset\", \"onUpdate\"];\nvar DEFAULTS = {\n  decimal: '.',\n  separator: ',',\n  delay: null,\n  prefix: '',\n  suffix: '',\n  duration: 2,\n  start: 0,\n  decimals: 0,\n  startOnMount: true,\n  enableReinitialize: true,\n  useEasing: true,\n  useGrouping: true,\n  useIndianSeparators: false\n};\nvar useCountUp = function useCountUp(props) {\n  var filteredProps = Object.fromEntries(Object.entries(props).filter(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      value = _ref2[1];\n    return value !== undefined;\n  }));\n  var _useMemo = React.useMemo(function () {\n      return _objectSpread2(_objectSpread2({}, DEFAULTS), filteredProps);\n    }, [props]),\n    ref = _useMemo.ref,\n    startOnMount = _useMemo.startOnMount,\n    enableReinitialize = _useMemo.enableReinitialize,\n    delay = _useMemo.delay,\n    onEnd = _useMemo.onEnd,\n    onStart = _useMemo.onStart,\n    onPauseResume = _useMemo.onPauseResume,\n    onReset = _useMemo.onReset,\n    onUpdate = _useMemo.onUpdate,\n    instanceProps = _objectWithoutProperties(_useMemo, _excluded$1);\n  var countUpRef = React.useRef();\n  var timerRef = React.useRef();\n  var isInitializedRef = React.useRef(false);\n  var createInstance = useEventCallback(function () {\n    return createCountUpInstance(typeof ref === 'string' ? ref : ref.current, instanceProps);\n  });\n  var getCountUp = useEventCallback(function (recreate) {\n    var countUp = countUpRef.current;\n    if (countUp && !recreate) {\n      return countUp;\n    }\n    var newCountUp = createInstance();\n    countUpRef.current = newCountUp;\n    return newCountUp;\n  });\n  var start = useEventCallback(function () {\n    var run = function run() {\n      return getCountUp(true).start(function () {\n        onEnd === null || onEnd === void 0 || onEnd({\n          pauseResume: pauseResume,\n          reset: reset,\n          start: restart,\n          update: update\n        });\n      });\n    };\n    if (delay && delay > 0) {\n      timerRef.current = setTimeout(run, delay * 1000);\n    } else {\n      run();\n    }\n    onStart === null || onStart === void 0 || onStart({\n      pauseResume: pauseResume,\n      reset: reset,\n      update: update\n    });\n  });\n  var pauseResume = useEventCallback(function () {\n    getCountUp().pauseResume();\n    onPauseResume === null || onPauseResume === void 0 || onPauseResume({\n      reset: reset,\n      start: restart,\n      update: update\n    });\n  });\n  var reset = useEventCallback(function () {\n    // Quick fix for https://github.com/glennreyes/react-countup/issues/736 - should be investigated\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    if (getCountUp().el) {\n      timerRef.current && clearTimeout(timerRef.current);\n      getCountUp().reset();\n      onReset === null || onReset === void 0 || onReset({\n        pauseResume: pauseResume,\n        start: restart,\n        update: update\n      });\n    }\n  });\n  var update = useEventCallback(function (newEnd) {\n    getCountUp().update(newEnd);\n    onUpdate === null || onUpdate === void 0 || onUpdate({\n      pauseResume: pauseResume,\n      reset: reset,\n      start: restart\n    });\n  });\n  var restart = useEventCallback(function () {\n    reset();\n    start();\n  });\n  var maybeInitialize = useEventCallback(function (shouldReset) {\n    if (startOnMount) {\n      if (shouldReset) {\n        reset();\n      }\n      start();\n    }\n  });\n  React.useEffect(function () {\n    if (!isInitializedRef.current) {\n      isInitializedRef.current = true;\n      maybeInitialize();\n    } else if (enableReinitialize) {\n      maybeInitialize(true);\n    }\n  }, [enableReinitialize, isInitializedRef, maybeInitialize, delay, props.start, props.suffix, props.prefix, props.duration, props.separator, props.decimals, props.decimal, props.formattingFn]);\n  React.useEffect(function () {\n    return function () {\n      reset();\n    };\n  }, [reset]);\n  return {\n    start: restart,\n    pauseResume: pauseResume,\n    reset: reset,\n    update: update,\n    getCountUp: getCountUp\n  };\n};\n\nvar _excluded = [\"className\", \"redraw\", \"containerProps\", \"children\", \"style\"];\nvar CountUp = function CountUp(props) {\n  var className = props.className,\n    redraw = props.redraw,\n    containerProps = props.containerProps,\n    children = props.children,\n    style = props.style,\n    useCountUpProps = _objectWithoutProperties(props, _excluded);\n  var containerRef = React.useRef(null);\n  var isInitializedRef = React.useRef(false);\n  var _useCountUp = useCountUp(_objectSpread2(_objectSpread2({}, useCountUpProps), {}, {\n      ref: containerRef,\n      startOnMount: typeof children !== 'function' || props.delay === 0,\n      // component manually restarts\n      enableReinitialize: false\n    })),\n    start = _useCountUp.start,\n    reset = _useCountUp.reset,\n    updateCountUp = _useCountUp.update,\n    pauseResume = _useCountUp.pauseResume,\n    getCountUp = _useCountUp.getCountUp;\n  var restart = useEventCallback(function () {\n    start();\n  });\n  var update = useEventCallback(function (end) {\n    if (!props.preserveValue) {\n      reset();\n    }\n    updateCountUp(end);\n  });\n  var initializeOnMount = useEventCallback(function () {\n    if (typeof props.children === 'function') {\n      // Warn when user didn't use containerRef at all\n      if (!(containerRef.current instanceof Element)) {\n        console.error(\"Couldn't find attached element to hook the CountUp instance into! Try to attach \\\"containerRef\\\" from the render prop to a an Element, eg. <span ref={containerRef} />.\");\n        return;\n      }\n    }\n\n    // unlike the hook, the CountUp component initializes on mount\n    getCountUp();\n  });\n  React.useEffect(function () {\n    initializeOnMount();\n  }, [initializeOnMount]);\n  React.useEffect(function () {\n    if (isInitializedRef.current) {\n      update(props.end);\n    }\n  }, [props.end, update]);\n  var redrawDependencies = redraw && props;\n\n  // if props.redraw, call this effect on every props change\n  React.useEffect(function () {\n    if (redraw && isInitializedRef.current) {\n      restart();\n    }\n  }, [restart, redraw, redrawDependencies]);\n\n  // if not props.redraw, call this effect only when certain props are changed\n  React.useEffect(function () {\n    if (!redraw && isInitializedRef.current) {\n      restart();\n    }\n  }, [restart, redraw, props.start, props.suffix, props.prefix, props.duration, props.separator, props.decimals, props.decimal, props.className, props.formattingFn]);\n  React.useEffect(function () {\n    isInitializedRef.current = true;\n  }, []);\n  if (typeof children === 'function') {\n    // TypeScript forces functional components to return JSX.Element | null.\n    return children({\n      countUpRef: containerRef,\n      start: start,\n      reset: reset,\n      update: updateCountUp,\n      pauseResume: pauseResume,\n      getCountUp: getCountUp\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"span\", _extends({\n    className: className,\n    ref: containerRef,\n    style: style\n  }, containerProps), typeof props.start !== 'undefined' ? getCountUp().formattingFn(props.start) : '');\n};\n\nexports[\"default\"] = CountUp;\nexports.useCountUp = useCountUp;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QtY291bnR1cC9idWlsZC9pbmRleC5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELFlBQVksbUJBQU8sQ0FBQyw0Q0FBTztBQUMzQixpQkFBaUIsbUJBQU8sQ0FBQyxpRUFBWTs7QUFFckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsWUFBWSxrRUFBa0U7QUFDdEYsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHNCQUFzQjtBQUN4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLHVCQUF1QjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsNkJBQTZCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxTQUFTO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSx3RUFBd0UsYUFBYTtBQUNyRjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsNkNBQTZDO0FBQzdDLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0RBQStELHNCQUFzQjtBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNktBQTZLLGNBQWM7QUFDM0w7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUEsa0JBQWU7QUFDZixrQkFBa0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWNvdW50dXAvYnVpbGQvaW5kZXguanM/Nzg1ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBSZWFjdCA9IHJlcXVpcmUoJ3JlYWN0Jyk7XG52YXIgY291bnR1cF9qcyA9IHJlcXVpcmUoJ2NvdW50dXAuanMnKTtcblxuZnVuY3Rpb24gX2l0ZXJhYmxlVG9BcnJheUxpbWl0KHIsIGwpIHtcbiAgdmFyIHQgPSBudWxsID09IHIgPyBudWxsIDogXCJ1bmRlZmluZWRcIiAhPSB0eXBlb2YgU3ltYm9sICYmIHJbU3ltYm9sLml0ZXJhdG9yXSB8fCByW1wiQEBpdGVyYXRvclwiXTtcbiAgaWYgKG51bGwgIT0gdCkge1xuICAgIHZhciBlLFxuICAgICAgbixcbiAgICAgIGksXG4gICAgICB1LFxuICAgICAgYSA9IFtdLFxuICAgICAgZiA9ICEwLFxuICAgICAgbyA9ICExO1xuICAgIHRyeSB7XG4gICAgICBpZiAoaSA9ICh0ID0gdC5jYWxsKHIpKS5uZXh0LCAwID09PSBsKSB7XG4gICAgICAgIGlmIChPYmplY3QodCkgIT09IHQpIHJldHVybjtcbiAgICAgICAgZiA9ICExO1xuICAgICAgfSBlbHNlIGZvciAoOyAhKGYgPSAoZSA9IGkuY2FsbCh0KSkuZG9uZSkgJiYgKGEucHVzaChlLnZhbHVlKSwgYS5sZW5ndGggIT09IGwpOyBmID0gITApO1xuICAgIH0gY2F0Y2ggKHIpIHtcbiAgICAgIG8gPSAhMCwgbiA9IHI7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGlmICghZiAmJiBudWxsICE9IHQucmV0dXJuICYmICh1ID0gdC5yZXR1cm4oKSwgT2JqZWN0KHUpICE9PSB1KSkgcmV0dXJuO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgaWYgKG8pIHRocm93IG47XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBhO1xuICB9XG59XG5mdW5jdGlvbiBvd25LZXlzKGUsIHIpIHtcbiAgdmFyIHQgPSBPYmplY3Qua2V5cyhlKTtcbiAgaWYgKE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMpIHtcbiAgICB2YXIgbyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMoZSk7XG4gICAgciAmJiAobyA9IG8uZmlsdGVyKGZ1bmN0aW9uIChyKSB7XG4gICAgICByZXR1cm4gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihlLCByKS5lbnVtZXJhYmxlO1xuICAgIH0pKSwgdC5wdXNoLmFwcGx5KHQsIG8pO1xuICB9XG4gIHJldHVybiB0O1xufVxuZnVuY3Rpb24gX29iamVjdFNwcmVhZDIoZSkge1xuICBmb3IgKHZhciByID0gMTsgciA8IGFyZ3VtZW50cy5sZW5ndGg7IHIrKykge1xuICAgIHZhciB0ID0gbnVsbCAhPSBhcmd1bWVudHNbcl0gPyBhcmd1bWVudHNbcl0gOiB7fTtcbiAgICByICUgMiA/IG93bktleXMoT2JqZWN0KHQpLCAhMCkuZm9yRWFjaChmdW5jdGlvbiAocikge1xuICAgICAgX2RlZmluZVByb3BlcnR5KGUsIHIsIHRbcl0pO1xuICAgIH0pIDogT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcnMgPyBPYmplY3QuZGVmaW5lUHJvcGVydGllcyhlLCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyh0KSkgOiBvd25LZXlzKE9iamVjdCh0KSkuZm9yRWFjaChmdW5jdGlvbiAocikge1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGUsIHIsIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IodCwgcikpO1xuICAgIH0pO1xuICB9XG4gIHJldHVybiBlO1xufVxuZnVuY3Rpb24gX3RvUHJpbWl0aXZlKHQsIHIpIHtcbiAgaWYgKFwib2JqZWN0XCIgIT0gdHlwZW9mIHQgfHwgIXQpIHJldHVybiB0O1xuICB2YXIgZSA9IHRbU3ltYm9sLnRvUHJpbWl0aXZlXTtcbiAgaWYgKHZvaWQgMCAhPT0gZSkge1xuICAgIHZhciBpID0gZS5jYWxsKHQsIHIgfHwgXCJkZWZhdWx0XCIpO1xuICAgIGlmIChcIm9iamVjdFwiICE9IHR5cGVvZiBpKSByZXR1cm4gaTtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiQEB0b1ByaW1pdGl2ZSBtdXN0IHJldHVybiBhIHByaW1pdGl2ZSB2YWx1ZS5cIik7XG4gIH1cbiAgcmV0dXJuIChcInN0cmluZ1wiID09PSByID8gU3RyaW5nIDogTnVtYmVyKSh0KTtcbn1cbmZ1bmN0aW9uIF90b1Byb3BlcnR5S2V5KHQpIHtcbiAgdmFyIGkgPSBfdG9QcmltaXRpdmUodCwgXCJzdHJpbmdcIik7XG4gIHJldHVybiBcInN5bWJvbFwiID09IHR5cGVvZiBpID8gaSA6IFN0cmluZyhpKTtcbn1cbmZ1bmN0aW9uIF9kZWZpbmVQcm9wZXJ0eShvYmosIGtleSwgdmFsdWUpIHtcbiAga2V5ID0gX3RvUHJvcGVydHlLZXkoa2V5KTtcbiAgaWYgKGtleSBpbiBvYmopIHtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqLCBrZXksIHtcbiAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICB3cml0YWJsZTogdHJ1ZVxuICAgIH0pO1xuICB9IGVsc2Uge1xuICAgIG9ialtrZXldID0gdmFsdWU7XG4gIH1cbiAgcmV0dXJuIG9iajtcbn1cbmZ1bmN0aW9uIF9leHRlbmRzKCkge1xuICBfZXh0ZW5kcyA9IE9iamVjdC5hc3NpZ24gPyBPYmplY3QuYXNzaWduLmJpbmQoKSA6IGZ1bmN0aW9uICh0YXJnZXQpIHtcbiAgICBmb3IgKHZhciBpID0gMTsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKykge1xuICAgICAgdmFyIHNvdXJjZSA9IGFyZ3VtZW50c1tpXTtcbiAgICAgIGZvciAodmFyIGtleSBpbiBzb3VyY2UpIHtcbiAgICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzb3VyY2UsIGtleSkpIHtcbiAgICAgICAgICB0YXJnZXRba2V5XSA9IHNvdXJjZVtrZXldO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0YXJnZXQ7XG4gIH07XG4gIHJldHVybiBfZXh0ZW5kcy5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xufVxuZnVuY3Rpb24gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2Uoc291cmNlLCBleGNsdWRlZCkge1xuICBpZiAoc291cmNlID09IG51bGwpIHJldHVybiB7fTtcbiAgdmFyIHRhcmdldCA9IHt9O1xuICB2YXIgc291cmNlS2V5cyA9IE9iamVjdC5rZXlzKHNvdXJjZSk7XG4gIHZhciBrZXksIGk7XG4gIGZvciAoaSA9IDA7IGkgPCBzb3VyY2VLZXlzLmxlbmd0aDsgaSsrKSB7XG4gICAga2V5ID0gc291cmNlS2V5c1tpXTtcbiAgICBpZiAoZXhjbHVkZWQuaW5kZXhPZihrZXkpID49IDApIGNvbnRpbnVlO1xuICAgIHRhcmdldFtrZXldID0gc291cmNlW2tleV07XG4gIH1cbiAgcmV0dXJuIHRhcmdldDtcbn1cbmZ1bmN0aW9uIF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhzb3VyY2UsIGV4Y2x1ZGVkKSB7XG4gIGlmIChzb3VyY2UgPT0gbnVsbCkgcmV0dXJuIHt9O1xuICB2YXIgdGFyZ2V0ID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2Uoc291cmNlLCBleGNsdWRlZCk7XG4gIHZhciBrZXksIGk7XG4gIGlmIChPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKSB7XG4gICAgdmFyIHNvdXJjZVN5bWJvbEtleXMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKHNvdXJjZSk7XG4gICAgZm9yIChpID0gMDsgaSA8IHNvdXJjZVN5bWJvbEtleXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGtleSA9IHNvdXJjZVN5bWJvbEtleXNbaV07XG4gICAgICBpZiAoZXhjbHVkZWQuaW5kZXhPZihrZXkpID49IDApIGNvbnRpbnVlO1xuICAgICAgaWYgKCFPYmplY3QucHJvdG90eXBlLnByb3BlcnR5SXNFbnVtZXJhYmxlLmNhbGwoc291cmNlLCBrZXkpKSBjb250aW51ZTtcbiAgICAgIHRhcmdldFtrZXldID0gc291cmNlW2tleV07XG4gICAgfVxuICB9XG4gIHJldHVybiB0YXJnZXQ7XG59XG5mdW5jdGlvbiBfc2xpY2VkVG9BcnJheShhcnIsIGkpIHtcbiAgcmV0dXJuIF9hcnJheVdpdGhIb2xlcyhhcnIpIHx8IF9pdGVyYWJsZVRvQXJyYXlMaW1pdChhcnIsIGkpIHx8IF91bnN1cHBvcnRlZEl0ZXJhYmxlVG9BcnJheShhcnIsIGkpIHx8IF9ub25JdGVyYWJsZVJlc3QoKTtcbn1cbmZ1bmN0aW9uIF9hcnJheVdpdGhIb2xlcyhhcnIpIHtcbiAgaWYgKEFycmF5LmlzQXJyYXkoYXJyKSkgcmV0dXJuIGFycjtcbn1cbmZ1bmN0aW9uIF91bnN1cHBvcnRlZEl0ZXJhYmxlVG9BcnJheShvLCBtaW5MZW4pIHtcbiAgaWYgKCFvKSByZXR1cm47XG4gIGlmICh0eXBlb2YgbyA9PT0gXCJzdHJpbmdcIikgcmV0dXJuIF9hcnJheUxpa2VUb0FycmF5KG8sIG1pbkxlbik7XG4gIHZhciBuID0gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKG8pLnNsaWNlKDgsIC0xKTtcbiAgaWYgKG4gPT09IFwiT2JqZWN0XCIgJiYgby5jb25zdHJ1Y3RvcikgbiA9IG8uY29uc3RydWN0b3IubmFtZTtcbiAgaWYgKG4gPT09IFwiTWFwXCIgfHwgbiA9PT0gXCJTZXRcIikgcmV0dXJuIEFycmF5LmZyb20obyk7XG4gIGlmIChuID09PSBcIkFyZ3VtZW50c1wiIHx8IC9eKD86VWl8SSludCg/Ojh8MTZ8MzIpKD86Q2xhbXBlZCk/QXJyYXkkLy50ZXN0KG4pKSByZXR1cm4gX2FycmF5TGlrZVRvQXJyYXkobywgbWluTGVuKTtcbn1cbmZ1bmN0aW9uIF9hcnJheUxpa2VUb0FycmF5KGFyciwgbGVuKSB7XG4gIGlmIChsZW4gPT0gbnVsbCB8fCBsZW4gPiBhcnIubGVuZ3RoKSBsZW4gPSBhcnIubGVuZ3RoO1xuICBmb3IgKHZhciBpID0gMCwgYXJyMiA9IG5ldyBBcnJheShsZW4pOyBpIDwgbGVuOyBpKyspIGFycjJbaV0gPSBhcnJbaV07XG4gIHJldHVybiBhcnIyO1xufVxuZnVuY3Rpb24gX25vbkl0ZXJhYmxlUmVzdCgpIHtcbiAgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkludmFsaWQgYXR0ZW1wdCB0byBkZXN0cnVjdHVyZSBub24taXRlcmFibGUgaW5zdGFuY2UuXFxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLlwiKTtcbn1cblxuLyoqXG4gKiBTaWxlbmNlIFNTUiBXYXJuaW5ncy5cbiAqIEJvcnJvd2VkIGZyb20gRm9ybWlrIHYyLjEuMSwgTGljZW5zZWQgTUlULlxuICpcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9mb3JtaXVtL2Zvcm1pay9ibG9iLzkzMTZhODY0NDc4ZjhmY2Q0ZmE5OWEwNzM1YjFkMzdhZmRmNTA3ZGMvTElDRU5TRVxuICovXG52YXIgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiB3aW5kb3cuZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiB3aW5kb3cuZG9jdW1lbnQuY3JlYXRlRWxlbWVudCAhPT0gJ3VuZGVmaW5lZCcgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiBSZWFjdC51c2VFZmZlY3Q7XG5cbi8qIGVzbGludC1kaXNhYmxlIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnkgKi9cblxuLyoqXG4gKiBDcmVhdGUgYSBzdGFibGUgcmVmZXJlbmNlIHRvIGEgY2FsbGJhY2sgd2hpY2ggaXMgdXBkYXRlZCBhZnRlciBlYWNoIHJlbmRlciBpcyBjb21taXR0ZWQuXG4gKiBUeXBlZCB2ZXJzaW9uIGJvcnJvd2VkIGZyb20gRm9ybWlrIHYyLjIuMS4gTGljZW5zZWQgTUlULlxuICpcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9mb3JtaXVtL2Zvcm1pay9ibG9iLzkzMTZhODY0NDc4ZjhmY2Q0ZmE5OWEwNzM1YjFkMzdhZmRmNTA3ZGMvTElDRU5TRVxuICovXG5mdW5jdGlvbiB1c2VFdmVudENhbGxiYWNrKGZuKSB7XG4gIHZhciByZWYgPSBSZWFjdC51c2VSZWYoZm4pO1xuXG4gIC8vIHdlIGNvcHkgYSByZWYgdG8gdGhlIGNhbGxiYWNrIHNjb3BlZCB0byB0aGUgY3VycmVudCBzdGF0ZS9wcm9wcyBvbiBlYWNoIHJlbmRlclxuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICByZWYuY3VycmVudCA9IGZuO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZUNhbGxiYWNrKGZ1bmN0aW9uICgpIHtcbiAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGFyZ3NbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gICAgfVxuICAgIHJldHVybiByZWYuY3VycmVudC5hcHBseSh2b2lkIDAsIGFyZ3MpO1xuICB9LCBbXSk7XG59XG5cbnZhciBjcmVhdGVDb3VudFVwSW5zdGFuY2UgPSBmdW5jdGlvbiBjcmVhdGVDb3VudFVwSW5zdGFuY2UoZWwsIHByb3BzKSB7XG4gIHZhciBkZWNpbWFsID0gcHJvcHMuZGVjaW1hbCxcbiAgICBkZWNpbWFscyA9IHByb3BzLmRlY2ltYWxzLFxuICAgIGR1cmF0aW9uID0gcHJvcHMuZHVyYXRpb24sXG4gICAgZWFzaW5nRm4gPSBwcm9wcy5lYXNpbmdGbixcbiAgICBlbmQgPSBwcm9wcy5lbmQsXG4gICAgZm9ybWF0dGluZ0ZuID0gcHJvcHMuZm9ybWF0dGluZ0ZuLFxuICAgIG51bWVyYWxzID0gcHJvcHMubnVtZXJhbHMsXG4gICAgcHJlZml4ID0gcHJvcHMucHJlZml4LFxuICAgIHNlcGFyYXRvciA9IHByb3BzLnNlcGFyYXRvcixcbiAgICBzdGFydCA9IHByb3BzLnN0YXJ0LFxuICAgIHN1ZmZpeCA9IHByb3BzLnN1ZmZpeCxcbiAgICB1c2VFYXNpbmcgPSBwcm9wcy51c2VFYXNpbmcsXG4gICAgdXNlR3JvdXBpbmcgPSBwcm9wcy51c2VHcm91cGluZyxcbiAgICB1c2VJbmRpYW5TZXBhcmF0b3JzID0gcHJvcHMudXNlSW5kaWFuU2VwYXJhdG9ycyxcbiAgICBlbmFibGVTY3JvbGxTcHkgPSBwcm9wcy5lbmFibGVTY3JvbGxTcHksXG4gICAgc2Nyb2xsU3B5RGVsYXkgPSBwcm9wcy5zY3JvbGxTcHlEZWxheSxcbiAgICBzY3JvbGxTcHlPbmNlID0gcHJvcHMuc2Nyb2xsU3B5T25jZSxcbiAgICBwbHVnaW4gPSBwcm9wcy5wbHVnaW47XG4gIHJldHVybiBuZXcgY291bnR1cF9qcy5Db3VudFVwKGVsLCBlbmQsIHtcbiAgICBzdGFydFZhbDogc3RhcnQsXG4gICAgZHVyYXRpb246IGR1cmF0aW9uLFxuICAgIGRlY2ltYWw6IGRlY2ltYWwsXG4gICAgZGVjaW1hbFBsYWNlczogZGVjaW1hbHMsXG4gICAgZWFzaW5nRm46IGVhc2luZ0ZuLFxuICAgIGZvcm1hdHRpbmdGbjogZm9ybWF0dGluZ0ZuLFxuICAgIG51bWVyYWxzOiBudW1lcmFscyxcbiAgICBzZXBhcmF0b3I6IHNlcGFyYXRvcixcbiAgICBwcmVmaXg6IHByZWZpeCxcbiAgICBzdWZmaXg6IHN1ZmZpeCxcbiAgICBwbHVnaW46IHBsdWdpbixcbiAgICB1c2VFYXNpbmc6IHVzZUVhc2luZyxcbiAgICB1c2VJbmRpYW5TZXBhcmF0b3JzOiB1c2VJbmRpYW5TZXBhcmF0b3JzLFxuICAgIHVzZUdyb3VwaW5nOiB1c2VHcm91cGluZyxcbiAgICBlbmFibGVTY3JvbGxTcHk6IGVuYWJsZVNjcm9sbFNweSxcbiAgICBzY3JvbGxTcHlEZWxheTogc2Nyb2xsU3B5RGVsYXksXG4gICAgc2Nyb2xsU3B5T25jZTogc2Nyb2xsU3B5T25jZVxuICB9KTtcbn07XG5cbnZhciBfZXhjbHVkZWQkMSA9IFtcInJlZlwiLCBcInN0YXJ0T25Nb3VudFwiLCBcImVuYWJsZVJlaW5pdGlhbGl6ZVwiLCBcImRlbGF5XCIsIFwib25FbmRcIiwgXCJvblN0YXJ0XCIsIFwib25QYXVzZVJlc3VtZVwiLCBcIm9uUmVzZXRcIiwgXCJvblVwZGF0ZVwiXTtcbnZhciBERUZBVUxUUyA9IHtcbiAgZGVjaW1hbDogJy4nLFxuICBzZXBhcmF0b3I6ICcsJyxcbiAgZGVsYXk6IG51bGwsXG4gIHByZWZpeDogJycsXG4gIHN1ZmZpeDogJycsXG4gIGR1cmF0aW9uOiAyLFxuICBzdGFydDogMCxcbiAgZGVjaW1hbHM6IDAsXG4gIHN0YXJ0T25Nb3VudDogdHJ1ZSxcbiAgZW5hYmxlUmVpbml0aWFsaXplOiB0cnVlLFxuICB1c2VFYXNpbmc6IHRydWUsXG4gIHVzZUdyb3VwaW5nOiB0cnVlLFxuICB1c2VJbmRpYW5TZXBhcmF0b3JzOiBmYWxzZVxufTtcbnZhciB1c2VDb3VudFVwID0gZnVuY3Rpb24gdXNlQ291bnRVcChwcm9wcykge1xuICB2YXIgZmlsdGVyZWRQcm9wcyA9IE9iamVjdC5mcm9tRW50cmllcyhPYmplY3QuZW50cmllcyhwcm9wcykuZmlsdGVyKGZ1bmN0aW9uIChfcmVmKSB7XG4gICAgdmFyIF9yZWYyID0gX3NsaWNlZFRvQXJyYXkoX3JlZiwgMiksXG4gICAgICB2YWx1ZSA9IF9yZWYyWzFdO1xuICAgIHJldHVybiB2YWx1ZSAhPT0gdW5kZWZpbmVkO1xuICB9KSk7XG4gIHZhciBfdXNlTWVtbyA9IFJlYWN0LnVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQyKF9vYmplY3RTcHJlYWQyKHt9LCBERUZBVUxUUyksIGZpbHRlcmVkUHJvcHMpO1xuICAgIH0sIFtwcm9wc10pLFxuICAgIHJlZiA9IF91c2VNZW1vLnJlZixcbiAgICBzdGFydE9uTW91bnQgPSBfdXNlTWVtby5zdGFydE9uTW91bnQsXG4gICAgZW5hYmxlUmVpbml0aWFsaXplID0gX3VzZU1lbW8uZW5hYmxlUmVpbml0aWFsaXplLFxuICAgIGRlbGF5ID0gX3VzZU1lbW8uZGVsYXksXG4gICAgb25FbmQgPSBfdXNlTWVtby5vbkVuZCxcbiAgICBvblN0YXJ0ID0gX3VzZU1lbW8ub25TdGFydCxcbiAgICBvblBhdXNlUmVzdW1lID0gX3VzZU1lbW8ub25QYXVzZVJlc3VtZSxcbiAgICBvblJlc2V0ID0gX3VzZU1lbW8ub25SZXNldCxcbiAgICBvblVwZGF0ZSA9IF91c2VNZW1vLm9uVXBkYXRlLFxuICAgIGluc3RhbmNlUHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMoX3VzZU1lbW8sIF9leGNsdWRlZCQxKTtcbiAgdmFyIGNvdW50VXBSZWYgPSBSZWFjdC51c2VSZWYoKTtcbiAgdmFyIHRpbWVyUmVmID0gUmVhY3QudXNlUmVmKCk7XG4gIHZhciBpc0luaXRpYWxpemVkUmVmID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcbiAgdmFyIGNyZWF0ZUluc3RhbmNlID0gdXNlRXZlbnRDYWxsYmFjayhmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGNyZWF0ZUNvdW50VXBJbnN0YW5jZSh0eXBlb2YgcmVmID09PSAnc3RyaW5nJyA/IHJlZiA6IHJlZi5jdXJyZW50LCBpbnN0YW5jZVByb3BzKTtcbiAgfSk7XG4gIHZhciBnZXRDb3VudFVwID0gdXNlRXZlbnRDYWxsYmFjayhmdW5jdGlvbiAocmVjcmVhdGUpIHtcbiAgICB2YXIgY291bnRVcCA9IGNvdW50VXBSZWYuY3VycmVudDtcbiAgICBpZiAoY291bnRVcCAmJiAhcmVjcmVhdGUpIHtcbiAgICAgIHJldHVybiBjb3VudFVwO1xuICAgIH1cbiAgICB2YXIgbmV3Q291bnRVcCA9IGNyZWF0ZUluc3RhbmNlKCk7XG4gICAgY291bnRVcFJlZi5jdXJyZW50ID0gbmV3Q291bnRVcDtcbiAgICByZXR1cm4gbmV3Q291bnRVcDtcbiAgfSk7XG4gIHZhciBzdGFydCA9IHVzZUV2ZW50Q2FsbGJhY2soZnVuY3Rpb24gKCkge1xuICAgIHZhciBydW4gPSBmdW5jdGlvbiBydW4oKSB7XG4gICAgICByZXR1cm4gZ2V0Q291bnRVcCh0cnVlKS5zdGFydChmdW5jdGlvbiAoKSB7XG4gICAgICAgIG9uRW5kID09PSBudWxsIHx8IG9uRW5kID09PSB2b2lkIDAgfHwgb25FbmQoe1xuICAgICAgICAgIHBhdXNlUmVzdW1lOiBwYXVzZVJlc3VtZSxcbiAgICAgICAgICByZXNldDogcmVzZXQsXG4gICAgICAgICAgc3RhcnQ6IHJlc3RhcnQsXG4gICAgICAgICAgdXBkYXRlOiB1cGRhdGVcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICB9O1xuICAgIGlmIChkZWxheSAmJiBkZWxheSA+IDApIHtcbiAgICAgIHRpbWVyUmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KHJ1biwgZGVsYXkgKiAxMDAwKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcnVuKCk7XG4gICAgfVxuICAgIG9uU3RhcnQgPT09IG51bGwgfHwgb25TdGFydCA9PT0gdm9pZCAwIHx8IG9uU3RhcnQoe1xuICAgICAgcGF1c2VSZXN1bWU6IHBhdXNlUmVzdW1lLFxuICAgICAgcmVzZXQ6IHJlc2V0LFxuICAgICAgdXBkYXRlOiB1cGRhdGVcbiAgICB9KTtcbiAgfSk7XG4gIHZhciBwYXVzZVJlc3VtZSA9IHVzZUV2ZW50Q2FsbGJhY2soZnVuY3Rpb24gKCkge1xuICAgIGdldENvdW50VXAoKS5wYXVzZVJlc3VtZSgpO1xuICAgIG9uUGF1c2VSZXN1bWUgPT09IG51bGwgfHwgb25QYXVzZVJlc3VtZSA9PT0gdm9pZCAwIHx8IG9uUGF1c2VSZXN1bWUoe1xuICAgICAgcmVzZXQ6IHJlc2V0LFxuICAgICAgc3RhcnQ6IHJlc3RhcnQsXG4gICAgICB1cGRhdGU6IHVwZGF0ZVxuICAgIH0pO1xuICB9KTtcbiAgdmFyIHJlc2V0ID0gdXNlRXZlbnRDYWxsYmFjayhmdW5jdGlvbiAoKSB7XG4gICAgLy8gUXVpY2sgZml4IGZvciBodHRwczovL2dpdGh1Yi5jb20vZ2xlbm5yZXllcy9yZWFjdC1jb3VudHVwL2lzc3Vlcy83MzYgLSBzaG91bGQgYmUgaW52ZXN0aWdhdGVkXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9iYW4tdHMtY29tbWVudFxuICAgIC8vIEB0cy1pZ25vcmVcbiAgICBpZiAoZ2V0Q291bnRVcCgpLmVsKSB7XG4gICAgICB0aW1lclJlZi5jdXJyZW50ICYmIGNsZWFyVGltZW91dCh0aW1lclJlZi5jdXJyZW50KTtcbiAgICAgIGdldENvdW50VXAoKS5yZXNldCgpO1xuICAgICAgb25SZXNldCA9PT0gbnVsbCB8fCBvblJlc2V0ID09PSB2b2lkIDAgfHwgb25SZXNldCh7XG4gICAgICAgIHBhdXNlUmVzdW1lOiBwYXVzZVJlc3VtZSxcbiAgICAgICAgc3RhcnQ6IHJlc3RhcnQsXG4gICAgICAgIHVwZGF0ZTogdXBkYXRlXG4gICAgICB9KTtcbiAgICB9XG4gIH0pO1xuICB2YXIgdXBkYXRlID0gdXNlRXZlbnRDYWxsYmFjayhmdW5jdGlvbiAobmV3RW5kKSB7XG4gICAgZ2V0Q291bnRVcCgpLnVwZGF0ZShuZXdFbmQpO1xuICAgIG9uVXBkYXRlID09PSBudWxsIHx8IG9uVXBkYXRlID09PSB2b2lkIDAgfHwgb25VcGRhdGUoe1xuICAgICAgcGF1c2VSZXN1bWU6IHBhdXNlUmVzdW1lLFxuICAgICAgcmVzZXQ6IHJlc2V0LFxuICAgICAgc3RhcnQ6IHJlc3RhcnRcbiAgICB9KTtcbiAgfSk7XG4gIHZhciByZXN0YXJ0ID0gdXNlRXZlbnRDYWxsYmFjayhmdW5jdGlvbiAoKSB7XG4gICAgcmVzZXQoKTtcbiAgICBzdGFydCgpO1xuICB9KTtcbiAgdmFyIG1heWJlSW5pdGlhbGl6ZSA9IHVzZUV2ZW50Q2FsbGJhY2soZnVuY3Rpb24gKHNob3VsZFJlc2V0KSB7XG4gICAgaWYgKHN0YXJ0T25Nb3VudCkge1xuICAgICAgaWYgKHNob3VsZFJlc2V0KSB7XG4gICAgICAgIHJlc2V0KCk7XG4gICAgICB9XG4gICAgICBzdGFydCgpO1xuICAgIH1cbiAgfSk7XG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKCFpc0luaXRpYWxpemVkUmVmLmN1cnJlbnQpIHtcbiAgICAgIGlzSW5pdGlhbGl6ZWRSZWYuY3VycmVudCA9IHRydWU7XG4gICAgICBtYXliZUluaXRpYWxpemUoKTtcbiAgICB9IGVsc2UgaWYgKGVuYWJsZVJlaW5pdGlhbGl6ZSkge1xuICAgICAgbWF5YmVJbml0aWFsaXplKHRydWUpO1xuICAgIH1cbiAgfSwgW2VuYWJsZVJlaW5pdGlhbGl6ZSwgaXNJbml0aWFsaXplZFJlZiwgbWF5YmVJbml0aWFsaXplLCBkZWxheSwgcHJvcHMuc3RhcnQsIHByb3BzLnN1ZmZpeCwgcHJvcHMucHJlZml4LCBwcm9wcy5kdXJhdGlvbiwgcHJvcHMuc2VwYXJhdG9yLCBwcm9wcy5kZWNpbWFscywgcHJvcHMuZGVjaW1hbCwgcHJvcHMuZm9ybWF0dGluZ0ZuXSk7XG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIHJlc2V0KCk7XG4gICAgfTtcbiAgfSwgW3Jlc2V0XSk7XG4gIHJldHVybiB7XG4gICAgc3RhcnQ6IHJlc3RhcnQsXG4gICAgcGF1c2VSZXN1bWU6IHBhdXNlUmVzdW1lLFxuICAgIHJlc2V0OiByZXNldCxcbiAgICB1cGRhdGU6IHVwZGF0ZSxcbiAgICBnZXRDb3VudFVwOiBnZXRDb3VudFVwXG4gIH07XG59O1xuXG52YXIgX2V4Y2x1ZGVkID0gW1wiY2xhc3NOYW1lXCIsIFwicmVkcmF3XCIsIFwiY29udGFpbmVyUHJvcHNcIiwgXCJjaGlsZHJlblwiLCBcInN0eWxlXCJdO1xudmFyIENvdW50VXAgPSBmdW5jdGlvbiBDb3VudFVwKHByb3BzKSB7XG4gIHZhciBjbGFzc05hbWUgPSBwcm9wcy5jbGFzc05hbWUsXG4gICAgcmVkcmF3ID0gcHJvcHMucmVkcmF3LFxuICAgIGNvbnRhaW5lclByb3BzID0gcHJvcHMuY29udGFpbmVyUHJvcHMsXG4gICAgY2hpbGRyZW4gPSBwcm9wcy5jaGlsZHJlbixcbiAgICBzdHlsZSA9IHByb3BzLnN0eWxlLFxuICAgIHVzZUNvdW50VXBQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhwcm9wcywgX2V4Y2x1ZGVkKTtcbiAgdmFyIGNvbnRhaW5lclJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgdmFyIGlzSW5pdGlhbGl6ZWRSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICB2YXIgX3VzZUNvdW50VXAgPSB1c2VDb3VudFVwKF9vYmplY3RTcHJlYWQyKF9vYmplY3RTcHJlYWQyKHt9LCB1c2VDb3VudFVwUHJvcHMpLCB7fSwge1xuICAgICAgcmVmOiBjb250YWluZXJSZWYsXG4gICAgICBzdGFydE9uTW91bnQ6IHR5cGVvZiBjaGlsZHJlbiAhPT0gJ2Z1bmN0aW9uJyB8fCBwcm9wcy5kZWxheSA9PT0gMCxcbiAgICAgIC8vIGNvbXBvbmVudCBtYW51YWxseSByZXN0YXJ0c1xuICAgICAgZW5hYmxlUmVpbml0aWFsaXplOiBmYWxzZVxuICAgIH0pKSxcbiAgICBzdGFydCA9IF91c2VDb3VudFVwLnN0YXJ0LFxuICAgIHJlc2V0ID0gX3VzZUNvdW50VXAucmVzZXQsXG4gICAgdXBkYXRlQ291bnRVcCA9IF91c2VDb3VudFVwLnVwZGF0ZSxcbiAgICBwYXVzZVJlc3VtZSA9IF91c2VDb3VudFVwLnBhdXNlUmVzdW1lLFxuICAgIGdldENvdW50VXAgPSBfdXNlQ291bnRVcC5nZXRDb3VudFVwO1xuICB2YXIgcmVzdGFydCA9IHVzZUV2ZW50Q2FsbGJhY2soZnVuY3Rpb24gKCkge1xuICAgIHN0YXJ0KCk7XG4gIH0pO1xuICB2YXIgdXBkYXRlID0gdXNlRXZlbnRDYWxsYmFjayhmdW5jdGlvbiAoZW5kKSB7XG4gICAgaWYgKCFwcm9wcy5wcmVzZXJ2ZVZhbHVlKSB7XG4gICAgICByZXNldCgpO1xuICAgIH1cbiAgICB1cGRhdGVDb3VudFVwKGVuZCk7XG4gIH0pO1xuICB2YXIgaW5pdGlhbGl6ZU9uTW91bnQgPSB1c2VFdmVudENhbGxiYWNrKGZ1bmN0aW9uICgpIHtcbiAgICBpZiAodHlwZW9mIHByb3BzLmNoaWxkcmVuID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAvLyBXYXJuIHdoZW4gdXNlciBkaWRuJ3QgdXNlIGNvbnRhaW5lclJlZiBhdCBhbGxcbiAgICAgIGlmICghKGNvbnRhaW5lclJlZi5jdXJyZW50IGluc3RhbmNlb2YgRWxlbWVudCkpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkNvdWxkbid0IGZpbmQgYXR0YWNoZWQgZWxlbWVudCB0byBob29rIHRoZSBDb3VudFVwIGluc3RhbmNlIGludG8hIFRyeSB0byBhdHRhY2ggXFxcImNvbnRhaW5lclJlZlxcXCIgZnJvbSB0aGUgcmVuZGVyIHByb3AgdG8gYSBhbiBFbGVtZW50LCBlZy4gPHNwYW4gcmVmPXtjb250YWluZXJSZWZ9IC8+LlwiKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIHVubGlrZSB0aGUgaG9vaywgdGhlIENvdW50VXAgY29tcG9uZW50IGluaXRpYWxpemVzIG9uIG1vdW50XG4gICAgZ2V0Q291bnRVcCgpO1xuICB9KTtcbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpbml0aWFsaXplT25Nb3VudCgpO1xuICB9LCBbaW5pdGlhbGl6ZU9uTW91bnRdKTtcbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoaXNJbml0aWFsaXplZFJlZi5jdXJyZW50KSB7XG4gICAgICB1cGRhdGUocHJvcHMuZW5kKTtcbiAgICB9XG4gIH0sIFtwcm9wcy5lbmQsIHVwZGF0ZV0pO1xuICB2YXIgcmVkcmF3RGVwZW5kZW5jaWVzID0gcmVkcmF3ICYmIHByb3BzO1xuXG4gIC8vIGlmIHByb3BzLnJlZHJhdywgY2FsbCB0aGlzIGVmZmVjdCBvbiBldmVyeSBwcm9wcyBjaGFuZ2VcbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAocmVkcmF3ICYmIGlzSW5pdGlhbGl6ZWRSZWYuY3VycmVudCkge1xuICAgICAgcmVzdGFydCgpO1xuICAgIH1cbiAgfSwgW3Jlc3RhcnQsIHJlZHJhdywgcmVkcmF3RGVwZW5kZW5jaWVzXSk7XG5cbiAgLy8gaWYgbm90IHByb3BzLnJlZHJhdywgY2FsbCB0aGlzIGVmZmVjdCBvbmx5IHdoZW4gY2VydGFpbiBwcm9wcyBhcmUgY2hhbmdlZFxuICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmICghcmVkcmF3ICYmIGlzSW5pdGlhbGl6ZWRSZWYuY3VycmVudCkge1xuICAgICAgcmVzdGFydCgpO1xuICAgIH1cbiAgfSwgW3Jlc3RhcnQsIHJlZHJhdywgcHJvcHMuc3RhcnQsIHByb3BzLnN1ZmZpeCwgcHJvcHMucHJlZml4LCBwcm9wcy5kdXJhdGlvbiwgcHJvcHMuc2VwYXJhdG9yLCBwcm9wcy5kZWNpbWFscywgcHJvcHMuZGVjaW1hbCwgcHJvcHMuY2xhc3NOYW1lLCBwcm9wcy5mb3JtYXR0aW5nRm5dKTtcbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpc0luaXRpYWxpemVkUmVmLmN1cnJlbnQgPSB0cnVlO1xuICB9LCBbXSk7XG4gIGlmICh0eXBlb2YgY2hpbGRyZW4gPT09ICdmdW5jdGlvbicpIHtcbiAgICAvLyBUeXBlU2NyaXB0IGZvcmNlcyBmdW5jdGlvbmFsIGNvbXBvbmVudHMgdG8gcmV0dXJuIEpTWC5FbGVtZW50IHwgbnVsbC5cbiAgICByZXR1cm4gY2hpbGRyZW4oe1xuICAgICAgY291bnRVcFJlZjogY29udGFpbmVyUmVmLFxuICAgICAgc3RhcnQ6IHN0YXJ0LFxuICAgICAgcmVzZXQ6IHJlc2V0LFxuICAgICAgdXBkYXRlOiB1cGRhdGVDb3VudFVwLFxuICAgICAgcGF1c2VSZXN1bWU6IHBhdXNlUmVzdW1lLFxuICAgICAgZ2V0Q291bnRVcDogZ2V0Q291bnRVcFxuICAgIH0pO1xuICB9XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInNwYW5cIiwgX2V4dGVuZHMoe1xuICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lLFxuICAgIHJlZjogY29udGFpbmVyUmVmLFxuICAgIHN0eWxlOiBzdHlsZVxuICB9LCBjb250YWluZXJQcm9wcyksIHR5cGVvZiBwcm9wcy5zdGFydCAhPT0gJ3VuZGVmaW5lZCcgPyBnZXRDb3VudFVwKCkuZm9ybWF0dGluZ0ZuKHByb3BzLnN0YXJ0KSA6ICcnKTtcbn07XG5cbmV4cG9ydHMuZGVmYXVsdCA9IENvdW50VXA7XG5leHBvcnRzLnVzZUNvdW50VXAgPSB1c2VDb3VudFVwO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/react-countup/build/index.js\n");

/***/ }),

/***/ "./node_modules/react-visibility-sensor/dist/visibility-sensor.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-visibility-sensor/dist/visibility-sensor.js ***!
  \************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("(function webpackUniversalModuleDefinition(root, factory) {\n\tif(true)\n\t\tmodule.exports = factory(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"), __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\"));\n\telse {}\n})(this, function(__WEBPACK_EXTERNAL_MODULE__1__, __WEBPACK_EXTERNAL_MODULE__2__) {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __nested_webpack_require_757__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __nested_webpack_require_757__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__nested_webpack_require_757__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__nested_webpack_require_757__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__nested_webpack_require_757__.d = function(exports, name, getter) {\n/******/ \t\tif(!__nested_webpack_require_757__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__nested_webpack_require_757__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__nested_webpack_require_757__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __nested_webpack_require_757__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__nested_webpack_require_757__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __nested_webpack_require_757__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__nested_webpack_require_757__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__nested_webpack_require_757__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__nested_webpack_require_757__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__nested_webpack_require_757__.p = \"\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __nested_webpack_require_757__(__nested_webpack_require_757__.s = 4);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __nested_webpack_require_4221__) {\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (false) { var throwOnDirectAccess, ReactIs; } else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = __nested_webpack_require_4221__(5)();\n}\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__1__;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__2__;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\n// Tell whether the rect is visible, given an offset\n//\n// return: boolean\nmodule.exports = function (offset, rect, containmentRect) {\n  var offsetDir = offset.direction;\n  var offsetVal = offset.value; // Rules for checking different kind of offsets. In example if the element is\n  // 90px below viewport and offsetTop is 100, it is considered visible.\n\n  switch (offsetDir) {\n    case 'top':\n      return containmentRect.top + offsetVal < rect.top && containmentRect.bottom > rect.bottom && containmentRect.left < rect.left && containmentRect.right > rect.right;\n\n    case 'left':\n      return containmentRect.left + offsetVal < rect.left && containmentRect.bottom > rect.bottom && containmentRect.top < rect.top && containmentRect.right > rect.right;\n\n    case 'bottom':\n      return containmentRect.bottom - offsetVal > rect.bottom && containmentRect.left < rect.left && containmentRect.right > rect.right && containmentRect.top < rect.top;\n\n    case 'right':\n      return containmentRect.right - offsetVal > rect.right && containmentRect.left < rect.left && containmentRect.top < rect.top && containmentRect.bottom > rect.bottom;\n  }\n};\n\n/***/ }),\n/* 4 */\n/***/ (function(module, __webpack_exports__, __nested_webpack_require_6122__) {\n\n\"use strict\";\n__nested_webpack_require_6122__.r(__webpack_exports__);\n/* harmony export (binding) */ __nested_webpack_require_6122__.d(__webpack_exports__, \"default\", function() { return VisibilitySensor; });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __nested_webpack_require_6122__(1);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__nested_webpack_require_6122__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __nested_webpack_require_6122__(2);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__nested_webpack_require_6122__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __nested_webpack_require_6122__(0);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__nested_webpack_require_6122__.n(prop_types__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_is_visible_with_offset__WEBPACK_IMPORTED_MODULE_3__ = __nested_webpack_require_6122__(3);\n/* harmony import */ var _lib_is_visible_with_offset__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__nested_webpack_require_6122__.n(_lib_is_visible_with_offset__WEBPACK_IMPORTED_MODULE_3__);\n\n\nfunction _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n\n\n\n\nfunction normalizeRect(rect) {\n  if (rect.width === undefined) {\n    rect.width = rect.right - rect.left;\n  }\n\n  if (rect.height === undefined) {\n    rect.height = rect.bottom - rect.top;\n  }\n\n  return rect;\n}\n\nvar VisibilitySensor =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inherits(VisibilitySensor, _React$Component);\n\n  function VisibilitySensor(props) {\n    var _this;\n\n    _classCallCheck(this, VisibilitySensor);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(VisibilitySensor).call(this, props));\n\n    _defineProperty(_assertThisInitialized(_this), \"getContainer\", function () {\n      return _this.props.containment || window;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"addEventListener\", function (target, event, delay, throttle) {\n      if (!_this.debounceCheck) {\n        _this.debounceCheck = {};\n      }\n\n      var timeout;\n      var func;\n\n      var later = function later() {\n        timeout = null;\n\n        _this.check();\n      };\n\n      if (throttle > -1) {\n        func = function func() {\n          if (!timeout) {\n            timeout = setTimeout(later, throttle || 0);\n          }\n        };\n      } else {\n        func = function func() {\n          clearTimeout(timeout);\n          timeout = setTimeout(later, delay || 0);\n        };\n      }\n\n      var info = {\n        target: target,\n        fn: func,\n        getLastTimeout: function getLastTimeout() {\n          return timeout;\n        }\n      };\n      target.addEventListener(event, info.fn);\n      _this.debounceCheck[event] = info;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"startWatching\", function () {\n      if (_this.debounceCheck || _this.interval) {\n        return;\n      }\n\n      if (_this.props.intervalCheck) {\n        _this.interval = setInterval(_this.check, _this.props.intervalDelay);\n      }\n\n      if (_this.props.scrollCheck) {\n        _this.addEventListener(_this.getContainer(), \"scroll\", _this.props.scrollDelay, _this.props.scrollThrottle);\n      }\n\n      if (_this.props.resizeCheck) {\n        _this.addEventListener(window, \"resize\", _this.props.resizeDelay, _this.props.resizeThrottle);\n      } // if dont need delayed call, check on load ( before the first interval fires )\n\n\n      !_this.props.delayedCall && _this.check();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"stopWatching\", function () {\n      if (_this.debounceCheck) {\n        // clean up event listeners and their debounce callers\n        for (var debounceEvent in _this.debounceCheck) {\n          if (_this.debounceCheck.hasOwnProperty(debounceEvent)) {\n            var debounceInfo = _this.debounceCheck[debounceEvent];\n            clearTimeout(debounceInfo.getLastTimeout());\n            debounceInfo.target.removeEventListener(debounceEvent, debounceInfo.fn);\n            _this.debounceCheck[debounceEvent] = null;\n          }\n        }\n      }\n\n      _this.debounceCheck = null;\n\n      if (_this.interval) {\n        _this.interval = clearInterval(_this.interval);\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"check\", function () {\n      var el = _this.node;\n      var rect;\n      var containmentRect; // if the component has rendered to null, dont update visibility\n\n      if (!el) {\n        return _this.state;\n      }\n\n      rect = normalizeRect(_this.roundRectDown(el.getBoundingClientRect()));\n\n      if (_this.props.containment) {\n        var containmentDOMRect = _this.props.containment.getBoundingClientRect();\n\n        containmentRect = {\n          top: containmentDOMRect.top,\n          left: containmentDOMRect.left,\n          bottom: containmentDOMRect.bottom,\n          right: containmentDOMRect.right\n        };\n      } else {\n        containmentRect = {\n          top: 0,\n          left: 0,\n          bottom: window.innerHeight || document.documentElement.clientHeight,\n          right: window.innerWidth || document.documentElement.clientWidth\n        };\n      } // Check if visibility is wanted via offset?\n\n\n      var offset = _this.props.offset || {};\n      var hasValidOffset = _typeof(offset) === \"object\";\n\n      if (hasValidOffset) {\n        containmentRect.top += offset.top || 0;\n        containmentRect.left += offset.left || 0;\n        containmentRect.bottom -= offset.bottom || 0;\n        containmentRect.right -= offset.right || 0;\n      }\n\n      var visibilityRect = {\n        top: rect.top >= containmentRect.top,\n        left: rect.left >= containmentRect.left,\n        bottom: rect.bottom <= containmentRect.bottom,\n        right: rect.right <= containmentRect.right\n      }; // https://github.com/joshwnj/react-visibility-sensor/pull/114\n\n      var hasSize = rect.height > 0 && rect.width > 0;\n      var isVisible = hasSize && visibilityRect.top && visibilityRect.left && visibilityRect.bottom && visibilityRect.right; // check for partial visibility\n\n      if (hasSize && _this.props.partialVisibility) {\n        var partialVisible = rect.top <= containmentRect.bottom && rect.bottom >= containmentRect.top && rect.left <= containmentRect.right && rect.right >= containmentRect.left; // account for partial visibility on a single edge\n\n        if (typeof _this.props.partialVisibility === \"string\") {\n          partialVisible = visibilityRect[_this.props.partialVisibility];\n        } // if we have minimum top visibility set by props, lets check, if it meets the passed value\n        // so if for instance element is at least 200px in viewport, then show it.\n\n\n        isVisible = _this.props.minTopValue ? partialVisible && rect.top <= containmentRect.bottom - _this.props.minTopValue : partialVisible;\n      } // Deprecated options for calculating offset.\n\n\n      if (typeof offset.direction === \"string\" && typeof offset.value === \"number\") {\n        console.warn(\"[notice] offset.direction and offset.value have been deprecated. They still work for now, but will be removed in next major version. Please upgrade to the new syntax: { %s: %d }\", offset.direction, offset.value);\n        isVisible = _lib_is_visible_with_offset__WEBPACK_IMPORTED_MODULE_3___default()(offset, rect, containmentRect);\n      }\n\n      var state = _this.state; // notify the parent when the value changes\n\n      if (_this.state.isVisible !== isVisible) {\n        state = {\n          isVisible: isVisible,\n          visibilityRect: visibilityRect\n        };\n\n        _this.setState(state);\n\n        if (_this.props.onChange) _this.props.onChange(isVisible);\n      }\n\n      return state;\n    });\n\n    _this.state = {\n      isVisible: null,\n      visibilityRect: {}\n    };\n    return _this;\n  }\n\n  _createClass(VisibilitySensor, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.node = react_dom__WEBPACK_IMPORTED_MODULE_1___default.a.findDOMNode(this);\n\n      if (this.props.active) {\n        this.startWatching();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.stopWatching();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      // re-register node in componentDidUpdate if children diffs [#103]\n      this.node = react_dom__WEBPACK_IMPORTED_MODULE_1___default.a.findDOMNode(this);\n\n      if (this.props.active && !prevProps.active) {\n        this.setState({\n          isVisible: null,\n          visibilityRect: {}\n        });\n        this.startWatching();\n      } else if (!this.props.active) {\n        this.stopWatching();\n      }\n    }\n  }, {\n    key: \"roundRectDown\",\n    value: function roundRectDown(rect) {\n      return {\n        top: Math.floor(rect.top),\n        left: Math.floor(rect.left),\n        bottom: Math.floor(rect.bottom),\n        right: Math.floor(rect.right)\n      };\n    }\n    /**\n     * Check if the element is within the visible viewport\n     */\n\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (this.props.children instanceof Function) {\n        return this.props.children({\n          isVisible: this.state.isVisible,\n          visibilityRect: this.state.visibilityRect\n        });\n      }\n\n      return react__WEBPACK_IMPORTED_MODULE_0___default.a.Children.only(this.props.children);\n    }\n  }]);\n\n  return VisibilitySensor;\n}(react__WEBPACK_IMPORTED_MODULE_0___default.a.Component);\n\n_defineProperty(VisibilitySensor, \"defaultProps\", {\n  active: true,\n  partialVisibility: false,\n  minTopValue: 0,\n  scrollCheck: false,\n  scrollDelay: 250,\n  scrollThrottle: -1,\n  resizeCheck: false,\n  resizeDelay: 250,\n  resizeThrottle: -1,\n  intervalCheck: true,\n  intervalDelay: 100,\n  delayedCall: false,\n  offset: {},\n  containment: null,\n  children: react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(\"span\", null)\n});\n\n_defineProperty(VisibilitySensor, \"propTypes\", {\n  onChange: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.func,\n  active: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.bool,\n  partialVisibility: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.bool, prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.oneOf([\"top\", \"right\", \"bottom\", \"left\"])]),\n  delayedCall: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.bool,\n  offset: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.shape({\n    top: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number,\n    left: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number,\n    bottom: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number,\n    right: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number\n  }), // deprecated offset property\n  prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.shape({\n    direction: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.oneOf([\"top\", \"right\", \"bottom\", \"left\"]),\n    value: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number\n  })]),\n  scrollCheck: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.bool,\n  scrollDelay: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number,\n  scrollThrottle: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number,\n  resizeCheck: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.bool,\n  resizeDelay: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number,\n  resizeThrottle: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number,\n  intervalCheck: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.bool,\n  intervalDelay: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number,\n  containment: typeof window !== \"undefined\" ? prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.instanceOf(window.Element) : prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.any,\n  children: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.element, prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.func]),\n  minTopValue: prop_types__WEBPACK_IMPORTED_MODULE_2___default.a.number\n});\n\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __nested_webpack_require_20664__) {\n\n\"use strict\";\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nvar ReactPropTypesSecret = __nested_webpack_require_20664__(6);\n\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error(\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n    err.name = 'Invariant Violation';\n    throw err;\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n\n\n/***/ })\n/******/ ]);\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/react-visibility-sensor/dist/visibility-sensor.js\n");

/***/ })

}]);