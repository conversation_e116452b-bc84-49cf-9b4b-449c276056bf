import { webpack } from 'next/dist/compiled/webpack/webpack';
import type { webpack5 } from 'next/dist/compiled/webpack/webpack';
import { CustomRoutes } from '../lib/load-custom-routes.js';
import { NextConfigComplete } from '../server/config-shared';
import type { Span } from '../trace';
export declare function attachReactRefresh(webpackConfig: webpack.Configuration, targetLoader: webpack.RuleSetUseItem): void;
export declare const NODE_RESOLVE_OPTIONS: {
    dependencyType: string;
    modules: string[];
    fallback: boolean;
    exportsFields: string[];
    importsFields: string[];
    conditionNames: string[];
    descriptionFiles: string[];
    extensions: string[];
    enforceExtensions: boolean;
    symlinks: boolean;
    mainFields: string[];
    mainFiles: string[];
    roots: never[];
    fullySpecified: boolean;
    preferRelative: boolean;
    preferAbsolute: boolean;
    restrictions: never[];
};
export declare const NODE_BASE_RESOLVE_OPTIONS: {
    alias: boolean;
    dependencyType: string;
    modules: string[];
    fallback: boolean;
    exportsFields: string[];
    importsFields: string[];
    conditionNames: string[];
    descriptionFiles: string[];
    extensions: string[];
    enforceExtensions: boolean;
    symlinks: boolean;
    mainFields: string[];
    mainFiles: string[];
    roots: never[];
    fullySpecified: boolean;
    preferRelative: boolean;
    preferAbsolute: boolean;
    restrictions: never[];
};
export declare const NODE_ESM_RESOLVE_OPTIONS: {
    alias: boolean;
    dependencyType: string;
    conditionNames: string[];
    fullySpecified: boolean;
    modules: string[];
    fallback: boolean;
    exportsFields: string[];
    importsFields: string[];
    descriptionFiles: string[];
    extensions: string[];
    enforceExtensions: boolean;
    symlinks: boolean;
    mainFields: string[];
    mainFiles: string[];
    roots: never[];
    preferRelative: boolean;
    preferAbsolute: boolean;
    restrictions: never[];
};
export declare const NODE_BASE_ESM_RESOLVE_OPTIONS: {
    alias: boolean;
    dependencyType: string;
    conditionNames: string[];
    fullySpecified: boolean;
    modules: string[];
    fallback: boolean;
    exportsFields: string[];
    importsFields: string[];
    descriptionFiles: string[];
    extensions: string[];
    enforceExtensions: boolean;
    symlinks: boolean;
    mainFields: string[];
    mainFiles: string[];
    roots: never[];
    preferRelative: boolean;
    preferAbsolute: boolean;
    restrictions: never[];
};
export declare const nextImageLoaderRegex: RegExp;
export declare function resolveExternal(appDir: string, esmExternalsConfig: NextConfigComplete['experimental']['esmExternals'], context: string, request: string, isEsmRequested: boolean, getResolve: (options: any) => (resolveContext: string, resolveRequest: string) => Promise<[string | null, boolean]>, isLocalCallback?: (res: string) => any, baseResolveCheck?: boolean, esmResolveOptions?: any, nodeResolveOptions?: any, baseEsmResolveOptions?: any, baseResolveOptions?: any): Promise<{
    localRes: any;
    res?: undefined;
    isEsm?: undefined;
} | {
    res: string | null;
    isEsm: boolean;
    localRes?: undefined;
}>;
export default function getBaseWebpackConfig(dir: string, { buildId, config, dev, isServer, isEdgeRuntime, pagesDir, target, reactProductionProfiling, entrypoints, rewrites, isDevFallback, runWebpackSpan, hasReactRoot, }: {
    buildId: string;
    config: NextConfigComplete;
    dev?: boolean;
    isServer?: boolean;
    isEdgeRuntime?: boolean;
    pagesDir: string;
    target?: string;
    reactProductionProfiling?: boolean;
    entrypoints: webpack5.EntryObject;
    rewrites: CustomRoutes['rewrites'];
    isDevFallback?: boolean;
    runWebpackSpan: Span;
    hasReactRoot: boolean;
}): Promise<webpack.Configuration>;
