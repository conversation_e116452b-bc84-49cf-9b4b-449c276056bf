{"version": 3, "sources": ["../../../server/web/utils.ts"], "names": ["streamToIterator", "notImplemented", "fromNodeHeaders", "toNodeHeaders", "splitCookiesString", "validateURL", "readable", "reader", "<PERSON><PERSON><PERSON><PERSON>", "value", "done", "read", "releaseLock", "name", "method", "Error", "object", "headers", "Headers", "key", "Object", "entries", "values", "Array", "isArray", "v", "undefined", "append", "result", "toLowerCase", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "length", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "url", "String", "URL", "error", "cause"], "mappings": "Y;;;E;QAEuBA,gBAAgB,GAAhBA,gBAAgB,A;QAcvBC,cAAc,GAAdA,cAAc,A;QAMdC,eAAe,GAAfA,eAAe,A;QAafC,aAAa,GAAbA,aAAa,A;QAuBbC,kBAAkB,GAAlBA,kBAAkB,A;QAqElBC,WAAW,GAAXA,WAAW,A;gBA7HJL,gBAAgB,CACrCM,QAA2B,EACD,CAAC;IAC3B,KAAK,CAACC,MAAM,GAAGD,QAAQ,CAACE,SAAS;UAC1B,IAAI,CAAE,CAAC;QACZ,KAAK,CAAC,CAAC,CAACC,KAAK,GAAEC,IAAI,EAAC,CAAC,GAAG,KAAK,CAACH,MAAM,CAACI,IAAI;QACzC,EAAE,EAAED,IAAI,EAAE,KAAK;QACf,EAAE,EAAED,KAAK,EAAE,CAAC;kBACJA,KAAK,A;QACb,CAAC;IACH,CAAC;IACDF,MAAM,CAACK,WAAW,E;AACpB,CAAC;SAEeX,cAAc,CAACY,IAAY,EAAEC,MAAc,EAAO,CAAC;IACjE,KAAK,CAAC,GAAG,CAACC,KAAK,EACZ,mBAAmB,EAAED,MAAM,CAAC,eAAe,EAAED,IAAI,CAAC,kCAAkC;AAEzF,CAAC;SAEeX,eAAe,CAACc,MAAmB,EAAW,CAAC;IAC7D,KAAK,CAACC,OAAO,GAAG,GAAG,CAACC,OAAO;IAC3B,GAAG,EAAE,GAAG,EAAEC,GAAG,EAAEV,KAAK,KAAKW,MAAM,CAACC,OAAO,CAACL,MAAM,EAAG,CAAC;QAChD,KAAK,CAACM,MAAM,GAAGC,KAAK,CAACC,OAAO,CAACf,KAAK,IAAIA,KAAK,GAAG,CAACA;YAAAA,KAAK;QAAA,CAAC;QACrD,GAAG,EAAE,GAAG,CAACgB,CAAC,IAAIH,MAAM,CAAE,CAAC;YACrB,EAAE,EAAEG,CAAC,KAAKC,SAAS,EAAE,CAAC;gBACpBT,OAAO,CAACU,MAAM,CAACR,GAAG,EAAEM,CAAC,C;YACvB,CAAC;QACH,CAAC;IACH,CAAC;IACD,MAAM,CAACR,OAAO;AAChB,CAAC;SAEed,aAAa,CAACc,OAAiB,EAAe,CAAC;IAC7D,KAAK,CAACW,MAAM,GAAgB,CAAC,CAAC;IAC9B,EAAE,EAAEX,OAAO,EAAE,CAAC;QACZ,GAAG,EAAE,KAAK,EAAEE,GAAG,EAAEV,KAAK,KAAKQ,OAAO,CAACI,OAAO,GAAI,CAAC;YAC7CO,MAAM,CAACT,GAAG,IAAIV,KAAK,A;YACnB,EAAE,EAAEU,GAAG,CAACU,WAAW,OAAO,CAAY,aAAE,CAAC;gBACvCD,MAAM,CAACT,GAAG,IAAIf,kBAAkB,CAACK,KAAK,C;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IACD,MAAM,CAACmB,MAAM;AACf,CAAC;SAYexB,kBAAkB,CAAC0B,aAAqB,EAAE,CAAC;IACzD,GAAG,CAACC,cAAc,GAAG,CAAC,CAAC;IACvB,GAAG,CAACC,GAAG,GAAG,CAAC;IACX,GAAG,CAACC,KAAK;IACT,GAAG,CAACC,EAAE;IACN,GAAG,CAACC,SAAS;IACb,GAAG,CAACC,SAAS;IACb,GAAG,CAACC,qBAAqB;aAEhBC,cAAc,GAAG,CAAC;cAClBN,GAAG,GAAGF,aAAa,CAACS,MAAM,SAASC,IAAI,CAACV,aAAa,CAACW,MAAM,CAACT,GAAG,GAAI,CAAC;YAC1EA,GAAG,IAAI,CAAC,A;QACV,CAAC;QACD,MAAM,CAACA,GAAG,GAAGF,aAAa,CAACS,MAAM;IACnC,CAAC;aAEQG,cAAc,GAAG,CAAC;QACzBR,EAAE,GAAGJ,aAAa,CAACW,MAAM,CAACT,GAAG,C;QAE7B,MAAM,CAACE,EAAE,KAAK,CAAG,MAAIA,EAAE,KAAK,CAAG,MAAIA,EAAE,KAAK,CAAG;IAC/C,CAAC;UAEMF,GAAG,GAAGF,aAAa,CAACS,MAAM,CAAE,CAAC;QAClCN,KAAK,GAAGD,GAAG,A;QACXK,qBAAqB,GAAG,KAAK,A;cAEtBC,cAAc,GAAI,CAAC;YACxBJ,EAAE,GAAGJ,aAAa,CAACW,MAAM,CAACT,GAAG,C;YAC7B,EAAE,EAAEE,EAAE,KAAK,CAAG,IAAE,CAAC;gBACf,EAAuE,AAAvE,qEAAuE;gBACvEC,SAAS,GAAGH,GAAG,A;gBACfA,GAAG,IAAI,CAAC,A;gBAERM,cAAc,E;gBACdF,SAAS,GAAGJ,GAAG,A;sBAERA,GAAG,GAAGF,aAAa,CAACS,MAAM,IAAIG,cAAc,GAAI,CAAC;oBACtDV,GAAG,IAAI,CAAC,A;gBACV,CAAC;gBAED,EAA8B,AAA9B,4BAA8B;gBAC9B,EAAE,EAAEA,GAAG,GAAGF,aAAa,CAACS,MAAM,IAAIT,aAAa,CAACW,MAAM,CAACT,GAAG,MAAM,CAAG,IAAE,CAAC;oBACpE,EAA6B,AAA7B,2BAA6B;oBAC7BK,qBAAqB,GAAG,IAAI,A;oBAC5B,EAA2D,AAA3D,yDAA2D;oBAC3DL,GAAG,GAAGI,SAAS,A;oBACfL,cAAc,CAACY,IAAI,CAACb,aAAa,CAACc,SAAS,CAACX,KAAK,EAAEE,SAAS,E;oBAC5DF,KAAK,GAAGD,GAAG,A;gBACb,CAAC,MAAM,CAAC;oBACN,EAAuC,AAAvC,qCAAuC;oBACvC,EAA8B,AAA9B,4BAA8B;oBAC9BA,GAAG,GAAGG,SAAS,GAAG,CAAC,A;gBACrB,CAAC;YACH,CAAC,MAAM,CAAC;gBACNH,GAAG,IAAI,CAAC,A;YACV,CAAC;QACH,CAAC;QAED,EAAE,GAAGK,qBAAqB,IAAIL,GAAG,IAAIF,aAAa,CAACS,MAAM,EAAE,CAAC;YAC1DR,cAAc,CAACY,IAAI,CAACb,aAAa,CAACc,SAAS,CAACX,KAAK,EAAEH,aAAa,CAACS,MAAM,E;QACzE,CAAC;IACH,CAAC;IAED,MAAM,CAACR,cAAc;AACvB,CAAC;SAKe1B,WAAW,CAACwC,GAAiB,EAAU,CAAC;IACtD,GAAG,CAAC,CAAC;QACH,MAAM,CAACC,MAAM,CAAC,GAAG,CAACC,GAAG,CAACD,MAAM,CAACD,GAAG;IAClC,CAAC,CAAC,KAAK,EAAEG,KAAK,EAAO,CAAC;QACpB,KAAK,CAAC,GAAG,CAACjC,KAAK,EACZ,4GAA4G,GAC7G,EAAkF,AAAlF,gFAAkF;QAClF,CAAC;YAACkC,KAAK,EAAED,KAAK;QAAC,CAAC;IAEpB,CAAC;AACH,CAAC"}