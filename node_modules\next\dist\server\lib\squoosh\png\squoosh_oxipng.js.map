{"version": 3, "sources": ["../../../../../server/lib/squoosh/png/squoosh_oxipng.js"], "names": ["optimise", "cleanup", "wasm", "cachedTextDecoder", "TextDecoder", "ignoreBOM", "fatal", "decode", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "getStringFromWasm0", "ptr", "len", "subarray", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "length", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "getArrayU8FromWasm0", "data", "level", "interlace", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "wbg", "__wbindgen_throw", "arg0", "arg1", "Error", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module"], "mappings": "Y;;;E;QAqDgBA,QAAQ,GAARA,QAAQ,A;QA6DRC,OAAO,GAAPA,OAAO,A;wB;AAlHvB,GAAG,CAACC,IAAI;AAER,GAAG,CAACC,iBAAiB,GAAG,GAAG,CAACC,WAAW,CAAC,CAAO,QAAE,CAAC;IAChDC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,IAAI;AACb,CAAC;AAEDH,iBAAiB,CAACI,MAAM,E;AAExB,GAAG,CAACC,oBAAoB,GAAG,IAAI;SACtBC,eAAe,GAAG,CAAC;IAC1B,EAAE,EACAD,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACE,MAAM,KAAKR,IAAI,CAACS,MAAM,CAACD,MAAM,EAClD,CAAC;QACDF,oBAAoB,GAAG,GAAG,CAACI,UAAU,CAACV,IAAI,CAACS,MAAM,CAACD,MAAM,C;IAC1D,CAAC;IACD,MAAM,CAACF,oBAAoB;AAC7B,CAAC;SAEQK,kBAAkB,CAACC,GAAG,EAAEC,GAAG,EAAE,CAAC;IACrC,MAAM,CAACZ,iBAAiB,CAACI,MAAM,CAACE,eAAe,GAAGO,QAAQ,CAACF,GAAG,EAAEA,GAAG,GAAGC,GAAG;AAC3E,CAAC;AAED,GAAG,CAACE,eAAe,GAAG,CAAC;SAEdC,iBAAiB,CAACC,GAAG,EAAEC,MAAM,EAAE,CAAC;IACvC,KAAK,CAACN,GAAG,GAAGM,MAAM,CAACD,GAAG,CAACE,MAAM,GAAG,CAAC;IACjCZ,eAAe,GAAGa,GAAG,CAACH,GAAG,EAAEL,GAAG,GAAG,CAAC,C;IAClCG,eAAe,GAAGE,GAAG,CAACE,MAAM,A;IAC5B,MAAM,CAACP,GAAG;AACZ,CAAC;AAED,GAAG,CAACS,oBAAoB,GAAG,IAAI;SACtBC,eAAe,GAAG,CAAC;IAC1B,EAAE,EACAD,oBAAoB,KAAK,IAAI,IAC7BA,oBAAoB,CAACb,MAAM,KAAKR,IAAI,CAACS,MAAM,CAACD,MAAM,EAClD,CAAC;QACDa,oBAAoB,GAAG,GAAG,CAACE,UAAU,CAACvB,IAAI,CAACS,MAAM,CAACD,MAAM,C;IAC1D,CAAC;IACD,MAAM,CAACa,oBAAoB;AAC7B,CAAC;SAEQG,mBAAmB,CAACZ,GAAG,EAAEC,GAAG,EAAE,CAAC;IACtC,MAAM,CAACN,eAAe,GAAGO,QAAQ,CAACF,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,GAAGC,GAAG;AAC1D,CAAC;SAOef,QAAQ,CAAC2B,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE,CAAC;IAChD,GAAG,CAAC,CAAC;QACH,KAAK,CAACC,MAAM,GAAG5B,IAAI,CAAC6B,+BAA+B,EAAE,EAAE;QACvD,GAAG,CAACC,IAAI,GAAGd,iBAAiB,CAACS,IAAI,EAAEzB,IAAI,CAAC+B,iBAAiB;QACzD,GAAG,CAACC,IAAI,GAAGjB,eAAe;QAC1Bf,IAAI,CAACF,QAAQ,CAAC8B,MAAM,EAAEE,IAAI,EAAEE,IAAI,EAAEN,KAAK,EAAEC,SAAS,C;QAClD,GAAG,CAACM,EAAE,GAAGX,eAAe,GAAGM,MAAM,GAAG,CAAC,GAAG,CAAC;QACzC,GAAG,CAACM,EAAE,GAAGZ,eAAe,GAAGM,MAAM,GAAG,CAAC,GAAG,CAAC;QACzC,GAAG,CAACO,EAAE,GAAGX,mBAAmB,CAACS,EAAE,EAAEC,EAAE,EAAEE,KAAK;QAC1CpC,IAAI,CAACqC,eAAe,CAACJ,EAAE,EAAEC,EAAE,GAAG,CAAC,C;QAC/B,MAAM,CAACC,EAAE;IACX,CAAC,QAAS,CAAC;QACTnC,IAAI,CAAC6B,+BAA+B,CAAC,EAAE,C;IACzC,CAAC;AACH,CAAC;eAEcS,IAAI,CAACC,MAAM,EAAEC,OAAO,EAAE,CAAC;IACpC,EAAE,EAAE,MAAM,CAACC,QAAQ,KAAK,CAAU,aAAIF,MAAM,YAAYE,QAAQ,EAAE,CAAC;QACjE,EAAE,EAAE,MAAM,CAACC,WAAW,CAACC,oBAAoB,KAAK,CAAU,WAAE,CAAC;YAC3D,MAAM,CAAC,KAAK,CAACD,WAAW,CAACC,oBAAoB,CAACJ,MAAM,EAAEC,OAAO;QAC/D,CAAC;QAED,KAAK,CAACI,KAAK,GAAG,KAAK,CAACL,MAAM,CAACM,WAAW;QACtC,MAAM,CAAC,KAAK,CAACH,WAAW,CAACI,WAAW,CAACF,KAAK,EAAEJ,OAAO;IACrD,CAAC,MAAM,CAAC;QACN,KAAK,CAACO,QAAQ,GAAG,KAAK,CAACL,WAAW,CAACI,WAAW,CAACP,MAAM,EAAEC,OAAO;QAE9D,EAAE,EAAEO,QAAQ,YAAYL,WAAW,CAACM,QAAQ,EAAE,CAAC;YAC7C,MAAM,CAAC,CAAC;gBAACD,QAAQ;gBAAER,MAAM;YAAC,CAAC;QAC7B,CAAC,MAAM,CAAC;YACN,MAAM,CAACQ,QAAQ;QACjB,CAAC;IACH,CAAC;AACH,CAAC;eAEcE,IAAI,CAACC,KAAK,EAAE,CAAC;IAC1B,KAAK,CAACV,OAAO,GAAG,CAAC,CAAC;IAClBA,OAAO,CAACW,GAAG,GAAG,CAAC,CAAC,A;IAChBX,OAAO,CAACW,GAAG,CAACC,gBAAgB,GAAG,QAAQ,CAAEC,IAAI,EAAEC,IAAI,EAAE,CAAC;QACpD,KAAK,CAAC,GAAG,CAACC,KAAK,CAAC5C,kBAAkB,CAAC0C,IAAI,EAAEC,IAAI;IAC/C,CAAC,A;IAED,EAAE,EACA,MAAM,CAACJ,KAAK,KAAK,CAAQ,WACxB,MAAM,CAACM,OAAO,KAAK,CAAU,aAAIN,KAAK,YAAYM,OAAO,IACzD,MAAM,CAACC,GAAG,KAAK,CAAU,aAAIP,KAAK,YAAYO,GAAG,EAClD,CAAC;QACDP,KAAK,GAAGQ,KAAK,CAACR,KAAK,C;IACrB,CAAC;IAED,KAAK,CAAC,CAAC,CAACH,QAAQ,GAAER,MAAM,EAAC,CAAC,GAAG,KAAK,CAACD,IAAI,CAAC,KAAK,CAACY,KAAK,EAAEV,OAAO;IAE5DxC,IAAI,GAAG+C,QAAQ,CAACY,OAAO,A;IACvBV,IAAI,CAACW,sBAAsB,GAAGrB,MAAM,A;IAEpC,MAAM,CAACvC,IAAI;AACb,CAAC;eAEciD,IAAI;0B;SAGHlD,OAAO,GAAG,CAAC;IACzBC,IAAI,GAAG,IAAI,A;IACXM,oBAAoB,GAAG,IAAI,A;IAC3Be,oBAAoB,GAAG,IAAI,A;AAC7B,CAAC"}