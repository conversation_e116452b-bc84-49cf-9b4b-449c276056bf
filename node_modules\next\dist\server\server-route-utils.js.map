{"version": 3, "sources": ["../../server/server-route-utils.ts"], "names": ["getCustomRouteMatcher", "pathMatch", "getCustomRoute", "type", "rule", "restrictedRedirectPaths", "match", "source", "internal", "regex", "modifyRouteRegex", "undefined", "name", "fn", "_req", "_res", "_params", "_parsedUrl", "finished", "createHeaderRoute", "headerRoute", "has", "res", "params", "hasParams", "Object", "keys", "length", "header", "headers", "key", "value", "compileNonPath", "<PERSON><PERSON><PERSON><PERSON>", "createRedirectRoute", "redirectRoute", "statusCode", "req", "parsedUrl", "parsedDestination", "prepareDestination", "appendParamsToQuery", "destination", "query", "search", "stringifyQuery", "updatedDestination", "formatUrl", "startsWith", "normalizeRepeatedSlashes", "redirect", "getRedirectStatus", "body", "send", "initialQuery", "getRequestMeta", "initialQueryValues", "values", "stringifyQs", "encodeURIComponent", "some", "initialQueryVal", "Array", "isArray", "includes"], "mappings": "Y;;;E;kH;AAUoD,GAA2B,CAA3B,iBAA2B;AACzD,GAAuC,CAAvC,UAAuC;AAItD,GAAgD,CAAhD,mBAAgD;AACxB,GAAgB,CAAhB,YAAgB;AACN,GAAa,CAAb,YAAa;AAClB,GAAK,CAAL,IAAK;AACA,GAAqB,CAArB,MAAqB;;;;;;AAE9D,KAAK,CAACA,qBAAqB,OAAGC,UAAS,UAAC,IAAI;AAErC,KAAK,CAACC,cAAc,IAAI,CAAC,CAC9BC,IAAI,GACJC,IAAI,GACJC,uBAAuB,EAKzB,CAAC,GAAK,CAAC;IACL,KAAK,CAACC,KAAK,GAAGN,qBAAqB,CACjCI,IAAI,CAACG,MAAM,GACTH,IAAI,CAASI,QAAQ,IAClBC,KAAa,OACZC,iBAAgB,mBACdD,KAAK,EACLN,IAAI,KAAK,CAAU,YAAGE,uBAAuB,GAAGM,SAAS;OAE7DA,SAAS;IAGf,MAAM,CAAC,CAAC;WACHP,IAAI;QACPD,IAAI;QACJG,KAAK;QACLM,IAAI,EAAET,IAAI;QACVU,EAAE,SAASC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,IAAM,CAAC;gBAACC,QAAQ,EAAE,KAAK;YAAC,CAAC;IACrE,CAAC;AACH,CAAC;QA3BYhB,cAAc,GAAdA,cAAc,A;AA6BpB,KAAK,CAACiB,iBAAiB,IAAI,CAAC,CACjCf,IAAI,GACJC,uBAAuB,EAIzB,CAAC,GAAK,CAAC;IACL,KAAK,CAACe,WAAW,GAAGlB,cAAc,CAAC,CAAC;QAClCC,IAAI,EAAE,CAAQ;QACdC,IAAI;QACJC,uBAAuB;IACzB,CAAC;IACD,MAAM,CAAC,CAAC;QACNC,KAAK,EAAEc,WAAW,CAACd,KAAK;QACxBe,GAAG,EAAED,WAAW,CAACC,GAAG;QACpBlB,IAAI,EAAEiB,WAAW,CAACjB,IAAI;QACtBS,IAAI,KAAKQ,WAAW,CAACjB,IAAI,CAAC,CAAC,EAAEiB,WAAW,CAACb,MAAM,CAAC,aAAa;QAC7DM,EAAE,SAASC,IAAI,EAAEQ,GAAG,EAAEC,MAAM,EAAEN,UAAU,GAAK,CAAC;YAC5C,KAAK,CAACO,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,EAAEI,MAAM,GAAG,CAAC;YAEhD,GAAG,EAAE,KAAK,CAACC,MAAM,IAAKR,WAAW,CAAYS,OAAO,CAAE,CAAC;gBACrD,GAAG,CAAC,CAAC,CAACC,GAAG,GAAEC,KAAK,EAAC,CAAC,GAAGH,MAAM;gBAC3B,EAAE,EAAEJ,SAAS,EAAE,CAAC;oBACdM,GAAG,OAAGE,mBAAc,iBAACF,GAAG,EAAEP,MAAM,C;oBAChCQ,KAAK,OAAGC,mBAAc,iBAACD,KAAK,EAAER,MAAM,C;gBACtC,CAAC;gBACDD,GAAG,CAACW,SAAS,CAACH,GAAG,EAAEC,KAAK,C;YAC1B,CAAC;YACD,MAAM,CAAC,CAAC;gBAACb,QAAQ,EAAE,KAAK;YAAC,CAAC;QAC5B,CAAC;IACH,CAAC;AACH,CAAC;QA/BYC,iBAAiB,GAAjBA,iBAAiB,A;AAiCvB,KAAK,CAACe,mBAAmB,IAAI,CAAC,CACnC9B,IAAI,GACJC,uBAAuB,EAIzB,CAAC,GAAK,CAAC;IACL,KAAK,CAAC8B,aAAa,GAAGjC,cAAc,CAAC,CAAC;QACpCC,IAAI,EAAE,CAAU;QAChBC,IAAI;QACJC,uBAAuB;IACzB,CAAC;IACD,MAAM,CAAC,CAAC;QACNG,QAAQ,EAAE2B,aAAa,CAAC3B,QAAQ;QAChCL,IAAI,EAAEgC,aAAa,CAAChC,IAAI;QACxBG,KAAK,EAAE6B,aAAa,CAAC7B,KAAK;QAC1Be,GAAG,EAAEc,aAAa,CAACd,GAAG;QACtBe,UAAU,EAAED,aAAa,CAACC,UAAU;QACpCxB,IAAI,GAAG,eAAe,EAAEuB,aAAa,CAAC5B,MAAM;QAC5CM,EAAE,SAASwB,GAAG,EAAEf,GAAG,EAAEC,MAAM,EAAEe,SAAS,GAAK,CAAC;YAC1C,KAAK,CAAC,CAAC,CAACC,iBAAiB,EAAC,CAAC,OAAGC,mBAAkB,qBAAC,CAAC;gBAChDC,mBAAmB,EAAE,KAAK;gBAC1BC,WAAW,EAAEP,aAAa,CAACO,WAAW;gBACtCnB,MAAM,EAAEA,MAAM;gBACdoB,KAAK,EAAEL,SAAS,CAACK,KAAK;YACxB,CAAC;YAED,KAAK,CAAC,CAAC,CAACA,KAAK,EAAC,CAAC,GAAGJ,iBAAiB;YACnC,MAAM,CAAEA,iBAAiB,CAASI,KAAK,A;YAEvCJ,iBAAiB,CAACK,MAAM,GAAGC,cAAc,CAACR,GAAG,EAAEM,KAAK,C;YAEpD,GAAG,CAACG,kBAAkB,OAAGC,IAAS,SAACR,iBAAiB;YAEpD,EAAE,EAAEO,kBAAkB,CAACE,UAAU,CAAC,CAAG,KAAG,CAAC;gBACvCF,kBAAkB,OAAGG,MAAwB,2BAACH,kBAAkB,C;YAClE,CAAC;YAEDxB,GAAG,CACA4B,QAAQ,CACPJ,kBAAkB,MAClBK,iBAAiB,oBAAChB,aAAa,GAEhCiB,IAAI,CAACN,kBAAkB,EACvBO,IAAI,E;YAEP,MAAM,CAAC,CAAC;gBACNnC,QAAQ,EAAE,IAAI;YAChB,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;QAnDYgB,mBAAmB,GAAnBA,mBAAmB,A;AAwDzB,KAAK,CAACW,cAAc,IAAIR,GAAoB,EAAEM,KAAqB,GAAK,CAAC;IAC9E,KAAK,CAACW,YAAY,OAAGC,YAAc,iBAAClB,GAAG,EAAE,CAAmB,uBAAK,CAAC,CAAC;IACnE,KAAK,CAACmB,kBAAkB,GACtB/B,MAAM,CAACgC,MAAM,CAACH,YAAY;IAE5B,MAAM,KAACI,YAAW,YAACf,KAAK,EAAEhC,SAAS,EAAEA,SAAS,EAAE,CAAC;QAC/CgD,kBAAkB,EAAC5B,KAAK,EAAE,CAAC;YACzB,EAAE,EACAA,KAAK,IAAIuB,YAAY,IACrBE,kBAAkB,CAACI,IAAI,EAAEC,eAAkC,GAAK,CAAC;gBAC/D,EAA0E,AAA1E,wEAA0E;gBAC1E,MAAM,CAACC,KAAK,CAACC,OAAO,CAACF,eAAe,IAChCA,eAAe,CAACG,QAAQ,CAACjC,KAAK,IAC9B8B,eAAe,KAAK9B,KAAK;YAC/B,CAAC,GACD,CAAC;gBACD,EAA4C,AAA5C,0CAA4C;gBAC5C,MAAM,CAAC4B,kBAAkB,CAAC5B,KAAK;YACjC,CAAC;YAED,MAAM,CAACA,KAAK;QACd,CAAC;IACH,CAAC;AACH,CAAC;QAvBYc,cAAc,GAAdA,cAAc,A"}