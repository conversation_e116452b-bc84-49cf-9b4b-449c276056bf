{"version": 3, "sources": ["../../client/router.ts"], "names": ["Router", "with<PERSON><PERSON><PERSON>", "default", "useRouter", "createRouter", "makePublicRouterInstance", "singletonRouter", "router", "readyCallbacks", "ready", "cb", "window", "push", "url<PERSON><PERSON><PERSON><PERSON>ields", "routerEvents", "core<PERSON><PERSON><PERSON><PERSON><PERSON>s", "Object", "defineProperty", "get", "events", "for<PERSON>ach", "field", "getRouter", "args", "event", "on", "eventField", "char<PERSON>t", "toUpperCase", "substring", "_singletonRouter", "err", "console", "error", "isError", "message", "stack", "Error", "React", "useContext", "RouterContext", "scopedRouter", "instance", "property", "assign", "Array", "isArray"], "mappings": "Y;;;E;+BA<PERSON><PERSON><PERSON>,CAAM;;;eAANA,OAAM;;E;+BAoHK<PERSON>,CAAU;;;2BAArBC,OAAO;;E;QAEAC,SAAS,GAATA,SAAS,A;QAWTC,YAAY,GAAZA,YAAY,A;QASZC,wBAAwB,GAAxBA,wBAAwB,A;wB;AA1JtB,GAAO,CAAP,MAAO;AACN,GAA6B,CAA7B,OAA6B;AAElB,GAA8B,CAA9B,cAA8B;AACxC,GAAiB,CAAjB,QAAiB;;;;;;;AAkBrC,KAAK,CAACC,eAAe,GAAwB,CAAC;IAC5CC,MAAM,EAAE,IAAI;IACZC,cAAc,EAAE,CAAC,CAAC;IAClBC,KAAK,EAACC,EAAc,EAAE,CAAC;QACrB,EAAE,EAAE,IAAI,CAACH,MAAM,EAAE,MAAM,CAACG,EAAE;QAC1B,EAAE,EAAE,MAAM,CAACC,MAAM,KAAK,CAAW,YAAE,CAAC;YAClC,IAAI,CAACH,cAAc,CAACI,IAAI,CAACF,EAAE,C;QAC7B,CAAC;IACH,CAAC;AACH,CAAC;AAED,EAA4E,AAA5E,0EAA4E;AAC5E,KAAK,CAACG,iBAAiB,GAAG,CAAC;IACzB,CAAU;IACV,CAAO;IACP,CAAO;IACP,CAAQ;IACR,CAAY;IACZ,CAAY;IACZ,CAAU;IACV,CAAQ;IACR,CAAS;IACT,CAAe;IACf,CAAS;IACT,CAAW;IACX,CAAgB;IAChB,CAAe;AACjB,CAAC;AACD,KAAK,CAACC,YAAY,GAAG,CAAC;IACpB,CAAkB;IAClB,CAAqB;IACrB,CAAqB;IACrB,CAAkB;IAClB,CAAiB;IACjB,CAAoB;AACtB,CAAC;AAGD,KAAK,CAACC,gBAAgB,GAAG,CAAC;IACxB,CAAM;IACN,CAAS;IACT,CAAQ;IACR,CAAM;IACN,CAAU;IACV,CAAgB;AAClB,CAAC;AAED,EAAiG,AAAjG,+FAAiG;AACjGC,MAAM,CAACC,cAAc,CAACX,eAAe,EAAE,CAAQ,SAAE,CAAC;IAChDY,GAAG,IAAG,CAAC;QACL,MAAM,CAAClB,OAAM,SAACmB,MAAM;IACtB,CAAC;AACH,CAAC,C;AAEDN,iBAAiB,CAACO,OAAO,EAAEC,KAAa,GAAK,CAAC;IAC5C,EAAsE,AAAtE,oEAAsE;IACtE,EAA6C,AAA7C,2CAA6C;IAC7C,EAAkE,AAAlE,gEAAkE;IAClE,EAA0B,AAA1B,wBAA0B;IAC1BL,MAAM,CAACC,cAAc,CAACX,eAAe,EAAEe,KAAK,EAAE,CAAC;QAC7CH,GAAG,IAAG,CAAC;YACL,KAAK,CAACX,MAAM,GAAGe,SAAS;YACxB,MAAM,CAACf,MAAM,CAACc,KAAK;QACrB,CAAC;IACH,CAAC,C;AACH,CAAC,C;AAEDN,gBAAgB,CAACK,OAAO,EAAEC,KAAa,GAAK,CAAC;IAEzCf,eAAe,CAASe,KAAK,QAAQE,IAAI,GAAY,CAAC;QACtD,KAAK,CAAChB,MAAM,GAAGe,SAAS;QACxB,MAAM,CAACf,MAAM,CAACc,KAAK,KAAKE,IAAI;IAC9B,CAAC,A;AACH,CAAC,C;AAEDT,YAAY,CAACM,OAAO,EAAEI,KAAK,GAAK,CAAC;IAC/BlB,eAAe,CAACG,KAAK,KAAO,CAAC;QAC3BT,OAAM,SAACmB,MAAM,CAACM,EAAE,CAACD,KAAK,MAAMD,IAAI,GAAK,CAAC;YACpC,KAAK,CAACG,UAAU,IAAI,EAAE,EAAEF,KAAK,CAACG,MAAM,CAAC,CAAC,EAAEC,WAAW,KAAKJ,KAAK,CAACK,SAAS,CACrE,CAAC;YAEH,KAAK,CAACC,gBAAgB,GAAGxB,eAAe;YACxC,EAAE,EAAEwB,gBAAgB,CAACJ,UAAU,GAAG,CAAC;gBACjC,GAAG,CAAC,CAAC;oBACHI,gBAAgB,CAACJ,UAAU,KAAKH,IAAI,C;gBACtC,CAAC,CAAC,KAAK,EAAEQ,GAAG,EAAE,CAAC;oBACbC,OAAO,CAACC,KAAK,EAAE,qCAAqC,EAAEP,UAAU,G;oBAChEM,OAAO,CAACC,KAAK,KACXC,QAAO,UAACH,GAAG,OAAOA,GAAG,CAACI,OAAO,CAAC,EAAE,EAAEJ,GAAG,CAACK,KAAK,KAAKL,GAAG,GAAG,CAAE,E;gBAE5D,CAAC;YACH,CAAC;QACH,CAAC,C;IACH,CAAC,C;AACH,CAAC,C;SAEQT,SAAS,GAAW,CAAC;IAC5B,EAAE,GAAGhB,eAAe,CAACC,MAAM,EAAE,CAAC;QAC5B,KAAK,CAAC4B,OAAO,GACX,CAA6B,+BAC7B,CAAqE;QACvE,KAAK,CAAC,GAAG,CAACE,KAAK,CAACF,OAAO;IACzB,CAAC;IACD,MAAM,CAAC7B,eAAe,CAACC,MAAM;AAC/B,CAAC;eAGcD,eAAe;0B;SAKdH,SAAS,GAAe,CAAC;IACvC,MAAM,CAACmC,MAAK,SAACC,UAAU,CAACC,cAAa;AACvC,CAAC;SASepC,YAAY,IAAImB,IAAI,EAAsB,CAAC;IACzDjB,eAAe,CAACC,MAAM,GAAG,GAAG,CAACP,OAAM,YAAIuB,IAAI,C;IAC3CjB,eAAe,CAACE,cAAc,CAACY,OAAO,EAAEV,EAAE,GAAKA,EAAE;K;IACjDJ,eAAe,CAACE,cAAc,GAAG,CAAC,CAAC,A;IAEnC,MAAM,CAACF,eAAe,CAACC,MAAM;AAC/B,CAAC;SAGeF,wBAAwB,CAACE,MAAc,EAAc,CAAC;IACpE,KAAK,CAACkC,YAAY,GAAGlC,MAAM;IAC3B,KAAK,CAACmC,QAAQ,GAAG,CAAC,CAAC;IAEnB,GAAG,EAAE,KAAK,CAACC,QAAQ,IAAI9B,iBAAiB,CAAE,CAAC;QACzC,EAAE,EAAE,MAAM,CAAC4B,YAAY,CAACE,QAAQ,MAAM,CAAQ,SAAE,CAAC;YAC/CD,QAAQ,CAACC,QAAQ,IAAI3B,MAAM,CAAC4B,MAAM,CAChCC,KAAK,CAACC,OAAO,CAACL,YAAY,CAACE,QAAQ,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAC/CF,YAAY,CAACE,QAAQ,GACrB,EAAmC,AAAnC,iCAAmC;A;YACrC,QAAQ;QACV,CAAC;QAEDD,QAAQ,CAACC,QAAQ,IAAIF,YAAY,CAACE,QAAQ,C;IAC5C,CAAC;IAED,EAAiG,AAAjG,+FAAiG;IACjGD,QAAQ,CAACvB,MAAM,GAAGnB,OAAM,SAACmB,MAAM,A;IAE/BJ,gBAAgB,CAACK,OAAO,EAAEC,KAAK,GAAK,CAAC;QACnCqB,QAAQ,CAACrB,KAAK,QAAQE,IAAI,GAAY,CAAC;YACrC,MAAM,CAACkB,YAAY,CAACpB,KAAK,KAAKE,IAAI;QACpC,CAAC,A;IACH,CAAC,C;IAED,MAAM,CAACmB,QAAQ;AACjB,CAAC"}